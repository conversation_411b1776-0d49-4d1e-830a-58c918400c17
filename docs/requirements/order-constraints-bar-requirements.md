
### Requirements for a "Volume Meter" component to visually display eligibility

Review the [auction rules](docs/design/A.auction-rules.md)

You will see that: 
- the auction proceeds in a series of rounds
- at the start of each round the auctioneer announces a round price
- bidders submit the quantity they wish to buy or sell at the current round
  price.
- these quantities are subject to the six Quantity Constraints Rules.

- buyers cannot indicate a willingness to buy more at a higher price than they were willing to buy at a lower price, nor less at a lower price than they were willing to buy at a higher price.

- sellers cannot indicate a willingness to sell more at a lower price than they were willing to sell at a higher price, nor less at a higher price than they were willing to sell at a lower price.

- Traders cannot switch from a buyer to a seller at a price lower than they were willing to buy at, nor can they switch from a seller to a buyer at a price higher than they were willing to sell at.

So these 6 rules are designed to prevent bidders from gaming the system, but they are not easy for traders to understand in realtime. 

Therefore our goal is to develop a component which will communicate to bidders these constrains, ie: their min/max buy and sell quantities, and whether they can switch from a buyer to seller or vice versa.


### Current Volume Meter Solution

Our solution is presented in the code below and works as follows:
- let's assume the the initial max buy and sell quantities for the bidder are both 50 MMlb (millions of pounds).
- we have a horizontal bar, divided in half, with zero in the middle, max buy of 50 on the left and max sell of 50 on the right.
- the left (buy) bar will be given the color green, and the right (sell) bar 
  will be given the color red.
- initially the bidder can enter a either a buy or sell quantity of up to 50 mmlb, with no minimum.
- so both the left and right bars are solid green and red.
- now, let's assume that the bidder enters a buy order for 25.
- so we put a yellow (or other color) mark midway on the buy (left, green) bar.
- now, if the round price goes down in the next round, then that bidder cannot enter less that 25 (per the rules above), nor can they enter any sell quantity (because they can't sell at a lower price then they were willing to buy at).
- so the green bar to the right of the yellow mark (ie 25 -> 0) is dimmed, and the entire red bar is dimmed).
- if the price was to instead go up, then the bar (s) to the left of the prior round order would be dimmed. so in this case, the green bar above 25 would be dimmed (the price has increased, the bidder cannot bid more at a higher price than they would at a lower price).
- now if the price reverses in the round after that, then again the same rule is applied: if the price goes up then the bar(s) to the left are dimmed, if the price goes down, then the bar(s) to the right are dimmed.
- so in our example, assuming the price goes down and now the bidder enters 40. 
- if the round price after that reverses and increases, then, and this is a test, what would the bars show?

Here are examples in vue