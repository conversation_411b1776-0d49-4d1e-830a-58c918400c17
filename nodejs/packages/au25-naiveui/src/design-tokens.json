{"borderRadius": {"base": "6px", "small": "3px"}, "lineHeight": {"base": "1.35"}, "fontSize": {"base": "13px", "cardTitle": "18px"}, "fontFamily": {"base": "'Public Sans', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'", "display": "'Lexend', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'", "mono": "'JetBrains Mono', SFMono-Regular, <PERSON><PERSON>, <PERSON><PERSON>as, Courier, monospace"}, "typography": {"h1": {"fontFamily": "{fontFamily.display}", "fontSize": "30px", "fontWeight": "700", "lineHeight": "41"}, "h2": {"fontFamily": "{fontFamily.display}", "fontSize": "26px", "fontWeight": "700", "lineHeight": "35"}, "h3": {"fontFamily": "{fontFamily.display}", "fontSize": "22px", "fontWeight": "700", "lineHeight": "30"}, "h4": {"fontFamily": "{fontFamily.display}", "fontSize": "18px", "fontWeight": "500", "lineHeight": "24"}, "h5": {"fontFamily": "{fontFamily.display}", "fontSize": "14px", "fontWeight": "700", "lineHeight": "19"}, "h6": {"fontFamily": "{fontFamily.base}", "fontSize": "12px", "fontWeight": "500", "lineHeight": "16"}, "p": {"fontFamily": "{fontFamily.base}", "fontSize": "{fontSize.base}", "lineHeight": "20"}}, "colors": {"light": {"sidebarBackground": "#ffffff", "bodyBackground": "#f5f7f9", "text": "#000000", "textSecondary": "#495465", "background": "#ffffff", "backgroundSecondary": "#fafbfc", "primary": "rgb(0, 178, 123)", "primary005": "rgba(0, 178, 123, 0.05)", "primary010": "rgba(0, 178, 123, 0.1)", "primary015": "rgba(0, 178, 123, 0.15)", "primary020": "rgba(0, 178, 123, 0.2)", "primary030": "rgba(0, 178, 123, 0.3)", "primary040": "rgba(0, 178, 123, 0.4)", "primary050": "rgba(0, 178, 123, 0.5)", "primary060": "rgba(0, 178, 123, 0.6)", "info": "#6267FF", "success": "#00B27B", "warning": "#FFB600", "error": "#FF0156", "info005": "rgba(98, 103, 255, 0.05)", "success005": "rgba(0, 178, 123, 0.05)", "warning005": "rgba(227, 194, 47, 0.05)", "error005": "rgba(255, 1, 86, 0.05)", "secondary1": "rgb(98, 103, 255)", "secondary1Opacity005": "rgba(98, 103, 255, 0.05)", "secondary1Opacity010": "rgba(98, 103, 255, 0.1)", "secondary1Opacity020": "rgba(98, 103, 255, 0.2)", "secondary1Opacity030": "rgba(98, 103, 255, 0.3)", "secondary2": "rgb(255, 97, 201)", "secondary2Opacity005": "rgba(255, 97, 201, 0.05)", "secondary2Opacity010": "rgba(255, 97, 201, 0.1)", "secondary2Opacity020": "rgba(255, 97, 201, 0.2)", "secondary2Opacity030": "rgba(255, 97, 201, 0.3)", "secondary3": "rgb(255, 182, 0)", "secondary3Opacity005": "rgba(255, 182, 0, 0.05)", "secondary3Opacity010": "rgba(255, 182, 0, 0.1)", "secondary3Opacity020": "rgba(255, 182, 0, 0.2)", "secondary3Opacity030": "rgba(255, 182, 0, 0.3)", "secondary4": "rgb(255, 1, 86)", "secondary4Opacity005": "rgba(255, 1, 86, 0.05)", "secondary4Opacity010": "rgba(255, 1, 86, 0.1)", "secondary4Opacity020": "rgba(255, 1, 86, 0.2)", "secondary4Opacity030": "rgba(255, 1, 86, 0.3)", "divider005": "rgba(0, 0, 0, 0.05)", "divider010": "rgba(0, 0, 0, 0.1)", "divider020": "rgba(0, 0, 0, 0.2)", "divider030": "rgba(0, 0, 0, 0.3)", "hover005": "rgba(0, 0, 0, 0.05)", "hover010": "rgba(0, 0, 0, 0.1)", "hover050": "rgba(0, 0, 0, 0.5)"}, "dark": {"sidebarBackground": "#1D1F25", "bodyBackground": "#14161A", "text": "#ffffff", "textSecondary": "#ACB5BE", "background": "#26282d", "backgroundSecondary": "#1D1F25", "primary": "rgb(0, 225, 155)", "primary005": "rgba(0, 225, 155, 0.05)", "primary010": "rgba(0, 225, 155, 0.1)", "primary015": "rgba(0, 225, 155, 0.15)", "primary020": "rgba(0, 225, 155, 0.2)", "primary030": "rgba(0, 225, 155, 0.3)", "primary040": "rgba(0, 225, 155, 0.4)", "primary050": "rgba(0, 225, 155, 0.5)", "primary060": "rgba(0, 225, 155, 0.6)", "info": "#6267FF", "success": "#00E19B", "warning": "#FFB600", "error": "#FF0156", "info005": "rgba(98, 103, 255, 0.05)", "success005": "rgba(0, 178, 123, 0.05)", "warning005": "rgba(227, 194, 47, 0.05)", "error005": "rgba(255, 1, 86, 0.05)", "secondary1": "rgb(98, 103, 255)", "secondary1Opacity005": "rgba(98, 103, 255, 0.05)", "secondary1Opacity010": "rgba(98, 103, 255, 0.1)", "secondary1Opacity020": "rgba(98, 103, 255, 0.2)", "secondary1Opacity030": "rgba(98, 103, 255, 0.3)", "secondary2": "rgb(255, 97, 201)", "secondary2Opacity005": "rgba(255, 97, 201, 0.05)", "secondary2Opacity010": "rgba(255, 97, 201, 0.1)", "secondary2Opacity020": "rgba(255, 97, 201, 0.2)", "secondary2Opacity030": "rgba(255, 97, 201, 0.3)", "secondary3": "rgb(255, 182, 0)", "secondary3Opacity005": "rgba(255, 182, 0, 0.05)", "secondary3Opacity010": "rgba(255, 182, 0, 0.1)", "secondary3Opacity020": "rgba(255, 182, 0, 0.2)", "secondary3Opacity030": "rgba(255, 182, 0, 0.3)", "secondary4": "rgb(255, 1, 86)", "secondary4Opacity005": "rgba(255, 1, 86, 0.05)", "secondary4Opacity010": "rgba(255, 1, 86, 0.1)", "secondary4Opacity020": "rgba(255, 1, 86, 0.2)", "secondary4Opacity030": "rgba(255, 1, 86, 0.3)", "divider005": "rgba(255, 255, 255, 0.05)", "divider010": "rgba(255, 255, 255, 0.1)", "divider020": "rgba(255, 255, 255, 0.2)", "divider030": "rgba(255, 255, 255, 0.3)", "hover005": "rgba(255, 255, 255, 0.05)", "hover010": "rgba(255, 255, 255, 0.1)", "hover050": "rgba(255, 255, 255, 0.5)"}}}