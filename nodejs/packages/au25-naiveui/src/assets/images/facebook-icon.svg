<svg width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Facebook " clip-path="url(#clip0_128_129)">
<path id="Vector" d="M28.5 14.4751C28.5 6.7431 22.232 0.475098 14.5 0.475098C6.76801 0.475098 0.5 6.7431 0.5 14.4751C0.5 21.4628 5.61957 27.2547 12.3125 28.305V18.522H8.75781V14.4751H12.3125V11.3907C12.3125 7.88197 14.4027 5.94385 17.6005 5.94385C19.1318 5.94385 20.7344 6.21728 20.7344 6.21728V9.6626H18.9691C17.23 9.6626 16.6875 10.7419 16.6875 11.8501V14.4751H20.5703L19.9496 18.522H16.6875V28.305C23.3804 27.2547 28.5 21.4628 28.5 14.4751Z" fill="#1877F2"/>
<path id="Vector_2" d="M19.9496 18.522L20.5703 14.4751H16.6875V11.8501C16.6875 10.7429 17.23 9.6626 18.9691 9.6626H20.7344V6.21729C20.7344 6.21729 19.1323 5.94385 17.6005 5.94385C14.4027 5.94385 12.3125 7.88197 12.3125 11.3907V14.4751H8.75781V18.522H12.3125V28.305C13.762 28.5318 15.238 28.5318 16.6875 28.305V18.522H19.9496Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_128_129">
<rect width="28" height="28" fill="white" transform="translate(0.5 0.475098)"/>
</clipPath>
</defs>
</svg>
