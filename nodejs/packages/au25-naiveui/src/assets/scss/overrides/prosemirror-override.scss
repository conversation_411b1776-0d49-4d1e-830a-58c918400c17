.ProseMirror {
	> * + * {
		margin-top: 0.75em;
	}

	&-focused {
		outline: none;
	}

	ul,
	ol {
		padding: 0 1rem;
	}

	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		line-height: 1.1;
	}

	h1 {
		font-size: 2em;
	}

	h2 {
		font-size: 1.5em;
	}

	code {
		background-color: var(--fg-secondary-color);
		padding: 3px 8px;
		border-radius: var(--border-radius-small);
		font-size: 13px;
		color: var(--bg-color);
	}

	pre {
		background: #0d0d0d;
		border-radius: var(--border-radius-small);
		color: #fff;
		font-family: "JetBrainsMono", monospace;
		padding: 0.75rem 1rem;

		code {
			background: none;
			color: inherit;
			font-size: 0.8rem;
			padding: 0;
		}
	}

	mark {
		background-color: #faf594;
	}

	img {
		height: auto;
		max-width: 100%;
	}

	hr {
		margin: 1rem 0;
	}

	blockquote {
		border-left: 2px solid var(--divider-020-color);
		padding-left: 1rem;
	}

	hr {
		border: none;
		border-top: 2px solid var(--divider-020-color);
		margin: 2rem 0;
	}

	ul[data-type="taskList"] {
		list-style: none;
		padding: 0;

		li {
			align-items: center;
			display: flex;

			> label {
				flex: 0 0 auto;
				margin-right: 0.5rem;
				user-select: none;
			}

			> div {
				flex: 1 1 auto;
			}
		}
	}

	ul {
		list-style-type: disc;
	}

	ol {
		list-style-type: decimal;
	}
}
