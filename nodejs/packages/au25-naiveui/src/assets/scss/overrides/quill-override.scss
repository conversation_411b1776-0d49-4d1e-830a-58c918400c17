@import "@vueup/vue-quill/dist/vue-quill.snow.css";

.ql-snow.ql-toolbar {
	border: 1px solid var(--border-color) !important;
	border-radius: var(--border-radius);

	.ql-picker {
		border-radius: var(--border-radius-small);

		.ql-picker-label {
			&.ql-active {
				background-color: var(--primary-005-color);
				color: var(--primary-color);

				.ql-fill {
					fill: var(--primary-color);
				}
				.ql-stroke {
					stroke: var(--primary-color);
				}
			}

			&:hover {
				background-color: transparent;
			}
		}
		&.ql-expanded {
			.ql-picker-label {
				border-color: transparent;

				&.ql-active {
					background-color: var(--primary-005-color);
					color: var(--primary-color);
				}
			}
			.ql-picker-options {
				border-radius: var(--border-radius);

				.ql-picker-item {
					border-radius: var(--border-radius-small);
					color: var(--fg-color);
					display: flex;
					.ql-fill {
						fill: var(--fg-color);
					}
					.ql-stroke {
						stroke: var(--fg-color);
					}

					&.ql-selected {
						background-color: var(--primary-005-color);
						color: var(--primary-color);
						.ql-fill {
							fill: var(--primary-color);
						}
						.ql-stroke {
							stroke: var(--primary-color);
						}
					}

					&:hover {
						background-color: var(--hover-010-color);
					}
				}
			}
		}

		&:hover {
			background-color: var(--hover-005-color);
		}
	}

	button {
		border-radius: var(--border-radius-small);

		&.ql-active {
			background-color: var(--primary-005-color);
			color: var(--primary-color);

			.ql-fill {
				fill: var(--primary-color);
			}
			.ql-stroke {
				stroke: var(--primary-color);
			}
		}

		&:hover {
			background-color: var(--hover-005-color);
		}
	}

	.ql-stroke {
		stroke: var(--fg-secondary-color);
	}

	.ql-fill {
		fill: var(--fg-secondary-color);
	}

	.ql-picker-options {
		background-color: var(--bg-color);
		border: 1px solid var(--border-color) !important;

		.ql-stroke {
			stroke: #000;
		}

		.ql-fill {
			fill: #000;
		}
	}
}

.ql-snow.ql-container {
	flex-grow: 1;
	height: inherit;
	border-radius: var(--border-radius);
	font-family: var(--font-family);
	border: 1px solid var(--border-color) !important;
	margin-top: 10px;
	font-size: 16px;
}
.ql-toolbar.ql-snow + .ql-container.ql-snow {
	border-top: 1px solid var(--border-color);
}

.direction-rtl {
	.ql-editor {
		text-align: right;
	}
}
