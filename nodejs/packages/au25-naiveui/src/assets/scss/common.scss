@import "@/assets/scss/_mixin.scss";

.custom-label {
	color: var(--label-color);
	font-size: 10px;
	padding: 2px 6px;
	border-radius: var(--border-radius-small);
	position: relative;
	display: inline-block;
	font-weight: bold;

	&::before {
		content: "";
		width: 100%;
		height: 100%;
		background-color: var(--label-color);
		border-radius: var(--border-radius-small);
		position: absolute;
		top: 0;
		left: 0;
		z-index: 0;
		opacity: 0.15;
	}
}

.page-wrapped {
	height: calc(100svh - var(--toolbar-height) - var(--view-padding) - (var(--view-padding) / 2));
}
.page-min-wrapped {
	min-height: calc(100svh - var(--toolbar-height) - var(--view-padding) - (var(--view-padding) / 2));
}
.page-mobile-full {
	@media (max-width: 700px) {
		@include page-full-view;
	}
}
.direction-rtl {
	.page-mobile-full {
		@media (max-width: 700px) {
			right: calc(var(--view-padding) * -1);
			left: unset;
		}
	}
}

.page {
	.page-header {
		margin-bottom: 20px;
		display: flex;
		align-items: baseline;
		justify-content: space-between;
		gap: 20px;

		.title {
			font-family: var(--font-family-display);
			font-weight: bold;
			font-size: 30px;
		}

		.links {
			display: flex;
			gap: 20px;

			a {
				text-decoration: underline;
				text-decoration-color: var(--primary-color);
				position: relative;
				top: -2px;

				.n-icon,
				.n-icon-wrapper {
					position: relative;
					top: 3px;
				}
			}
		}

		@media (max-width: 700px) {
			flex-direction: column;
			gap: 6px;

			.title {
				font-size: 20px;
			}
		}
	}
	.components-list {
		display: grid;
		gap: 1.25em;
		align-items: start;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));

		@media (max-width: 400px) {
			grid-template-columns: repeat(auto-fit, minmax(100%, 1fr));
		}

		& > div {
			overflow: hidden;
		}
	}
}

/*
	DOES NOT WORK ON FIREFOX!
	:has() CSS relational pseudo-class not yet supported by Firefox
	(https://caniuse.com/css-has)
	you can find a worker around in pages that use this class
*/
#app {
	.layout {
		.main {
			.view:has(.page-without-footer) {
				& + footer {
					display: none;
				}
			}
		}
	}
}

.scrollbar-styled {
	/* Works on Firefox */

	scrollbar-width: thin;
	scrollbar-color: var(--hover-010-color) var(--bg-secondary-color);

	/* Works on Chrome, Edge, and Safari */
	&::-webkit-scrollbar,
	::-webkit-scrollbar {
		width: 12px;
	}

	&::-webkit-scrollbar-track,
	::-webkit-scrollbar-track {
		background: var(--bg-secondary-color);
	}

	&::-webkit-scrollbar-thumb,
	::-webkit-scrollbar-thumb {
		background-color: var(--hover-010-color);
		border-radius: 20px;
		border: 2px solid var(--bg-secondary-color);
	}
}

.code-bg-transparent {
	code {
		background-color: transparent;
	}
}
