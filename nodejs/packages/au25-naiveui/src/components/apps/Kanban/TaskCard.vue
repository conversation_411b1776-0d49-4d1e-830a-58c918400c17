<template>
	<div class="task-card flex flex-col justify-between">
		<div class="task-header flex justify-between gap-3">
			<div class="task-title">{{ task.title }}</div>
			<Icon v-if="mobile" :size="20" class="pan-area" :name="PanIcon" />
		</div>
		<div class="task-footer flex justify-between items-end">
			<span class="task-date">{{ task.dateText }}</span>
			<span
				v-if="task.label"
				class="task-label custom-label"
				:style="`--label-color:${labelsColors[task.label.id]}`"
			>
				{{ task.label.title }}
			</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { Task } from "@/mock/kanban"
import Icon from "@/components/common/Icon.vue"
import { useThemeStore } from "@/stores/theme"
import { computed, toRefs } from "vue"

const props = defineProps<{
	task: Task
	mobile: boolean
}>()
const { task, mobile } = toRefs(props)

const PanIcon = "carbon:move"
const themeStore = useThemeStore()
const secondaryColors = computed(() => themeStore.secondaryColors)

const labelsColors = {
	design: secondaryColors.value.secondary1,
	"feature-request": secondaryColors.value.secondary2,
	backend: secondaryColors.value.secondary3,
	qa: secondaryColors.value.secondary4
} as unknown as { [key: string]: string }
</script>

<style lang="scss" scoped>
.task-card {
	cursor: move;
	border-radius: var(--border-radius-small);
	padding: 8px 10px;
	margin-bottom: 10px;
	margin-top: 3px;
	background-color: var(--bg-color);
	transition: all 0.2s;
	border: 1px solid var(--border-color);

	.pan-area {
		margin-top: 2px;
	}

	.task-title {
		font-weight: bold;
		font-size: 15px;
		line-height: 1.3;
	}

	.task-footer {
		margin-top: 14px;
		.task-date {
			font-size: 14px;
			opacity: 0.8;
		}
	}

	&:hover {
		border-color: var(--primary-color);
	}
}
</style>
