<template>
    <n-card title="Create New Auction"
            style="max-width: 800px; margin: 20px auto;">
        <n-form ref="formRef"
                :model="formModel"
                :rules="formRules"
                @submit.prevent="handleSubmit">
            <n-grid :cols="2"
                    :x-gap="24">
                <n-form-item label="Auction ID (System Generated)"
                             path="auction_id">
                    <n-input v-model:value="formModel.auction_id"
                             disabled/>
                </n-form-item>
                <n-form-item label="Auction Name"
                             path="auction_name">
                    <n-input v-model:value="formModel.auction_name"
                             placeholder="Enter auction name"/>
                </n-form-item>

                <n-form-item label="Price Label"
                             path="price_label">
                    <n-input v-model:value="formModel.price_label"
                             placeholder="Enter price label"/>
                </n-form-item>
                <n-form-item label="Quantity Label"
                             path="quantity_label">
                    <n-input v-model:value="formModel.quantity_label"
                             placeholder="Enter quantity label"/>
                </n-form-item>

                <n-form-item label="Price Decimal Places"
                             path="price_decimal_places">
                    <n-input-number v-model:value="formModel.price_decimal_places"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Minimum Quantity"
                             path="quantity_minimum">
                    <n-input-number v-model:value="formModel.quantity_minimum"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Quantity Step"
                             path="quantity_step">
                    <n-input-number v-model:value="formModel.quantity_step"
                                    :min="1"/>
                </n-form-item>

                <n-form-item label="Initial Price Change"
                             path="price_change_initial">
                    <n-input-number v-model:value="formModel.price_change_initial"/>
                </n-form-item>
                <n-form-item label="Price Change After Reversal"
                             path="price_change_post_reversal">
                    <n-input-number v-model:value="formModel.price_change_post_reversal"/>
                </n-form-item>

                <n-form-item label="Excess Level 0 Label"
                             path="excess_level_0_label">
                    <n-input v-model:value="formModel.excess_level_0_label"
                             placeholder="Enter excess level 0 label"/>
                </n-form-item>
                <n-form-item label="Excess Level 1 Label"
                             path="excess_level_1_label">
                    <n-input v-model:value="formModel.excess_level_1_label"
                             placeholder="Enter excess level 1 label"/>
                </n-form-item>
                <n-form-item label="Excess Level 1 Quantity"
                             path="excess_level_1_quantity">
                    <n-input-number v-model:value="formModel.excess_level_1_quantity"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Excess Level 2 Label"
                             path="excess_level_2_label">
                    <n-input v-model:value="formModel.excess_level_2_label"
                             placeholder="Enter excess level 2 label"/>
                </n-form-item>
                <n-form-item label="Excess Level 2 Quantity"
                             path="excess_level_2_quantity">
                    <n-input-number v-model:value="formModel.excess_level_2_quantity"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Excess Level 3 Label"
                             path="excess_level_3_label">
                    <n-input v-model:value="formModel.excess_level_3_label"
                             placeholder="Enter excess level 3 label"/>
                </n-form-item>
                <n-form-item label="Excess Level 3 Quantity"
                             path="excess_level_3_quantity">
                    <n-input-number v-model:value="formModel.excess_level_3_quantity"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Excess Level 4 Label"
                             path="excess_level_4_label">
                    <n-input v-model:value="formModel.excess_level_4_label"
                             placeholder="Enter excess level 4 label"/>
                </n-form-item>
                <n-form-item label="Excess Level 4 Quantity"
                             path="excess_level_4_quantity">
                    <n-input-number v-model:value="formModel.excess_level_4_quantity"
                                    :min="0"/>
                </n-form-item>

                <n-form-item label="Round Open Minimum Seconds"
                             path="round_open_min_seconds">
                    <n-input-number v-model:value="formModel.round_open_min_seconds"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Round Closed Minimum Seconds"
                             path="round_closed_min_secs">
                    <n-input-number v-model:value="formModel.round_closed_min_secs"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Round Orange Seconds"
                             path="round_orange_secs">
                    <n-input-number v-model:value="formModel.round_orange_secs"
                                    :min="0"/>
                </n-form-item>
                <n-form-item label="Round Red Seconds"
                             path="round_red_secs">
                    <n-input-number v-model:value="formModel.round_red_secs"
                                    :min="0"/>
                </n-form-item>

                <n-form-item label="Starting Year"
                             path="starting_year">
                    <n-input-number v-model:value="formModel.starting_year"
                                    :min="2024"/>
                </n-form-item>
                <n-form-item label="Starting Month"
                             path="starting_month">
                    <n-input-number v-model:value="formModel.starting_month"
                                    :min="1"
                                    :max="12"/>
                </n-form-item>
                <n-form-item label="Starting Day"
                             path="starting_day">
                    <n-input-number v-model:value="formModel.starting_day"
                                    :min="1"
                                    :max="31"/>
                </n-form-item>
                <n-form-item label="Starting Hour"
                             path="starting_hour">
                    <n-input-number v-model:value="formModel.starting_hour"
                                    :min="0"
                                    :max="23"/>
                </n-form-item>
                <n-form-item label="Starting Minutes"
                             path="starting_mins">
                    <n-input-number v-model:value="formModel.starting_mins"
                                    :min="0"
                                    :max="59"/>
                </n-form-item>
                <n-form-item label="Starting Price Announcement Minutes"
                             path="starting_price_announcement_mins">
                    <n-input-number v-model:value="formModel.starting_price_announcement_mins"
                                    :min="0"/>
                </n-form-item>

                <n-form-item label="Month is 1-Based"
                             path="month_is_1_based">
                    <n-switch v-model:value="formModel.month_is_1_based"/>
                </n-form-item>
                <n-form-item label="Use Counterparty Credits"
                             path="use_counterparty_credits">
                    <n-select v-model:value="formModel.use_counterparty_credits"
                              :options="[{ label: 'true', value: 'true' }, { label: 'false', value: 'false' }]"/>
                </n-form-item>
                <n-form-item label="Cost Multiplier"
                             path="cost_multiplier">
                    <n-input v-model:value="formModel.cost_multiplier"
                             placeholder="Enter cost multiplier"/>
                </n-form-item>
            </n-grid>

            <n-button type="primary"
                      @click="handleSubmit">Create Auction
            </n-button>
        </n-form>
    </n-card>
</template>

<script setup
        lang="ts">
import {ref} from 'vue';
import type {FormRules} from 'naive-ui';
import {NButton, NCard, NForm, NFormItem, NGrid, NInput, NInputNumber, NSelect, NSwitch, useMessage} from 'naive-ui';

interface DeAuctionSaveCommand {
    auction_id: string;
    auction_name: string;
    cost_multiplier: string;
    excess_level_0_label: string;
    excess_level_1_label: string;
    excess_level_1_quantity: string;
    excess_level_2_label: string;
    excess_level_2_quantity: string;
    excess_level_3_label: string;
    excess_level_3_quantity: string;
    excess_level_4_label: string;
    excess_level_4_quantity: string;
    month_is_1_based: boolean;
    price_change_initial: string;
    price_change_post_reversal: string;
    price_decimal_places: string;
    price_label: string;
    quantity_label: string;
    quantity_minimum: string;
    quantity_step: string;
    round_closed_min_secs: string;
    round_open_min_seconds: string;
    round_orange_secs: string;
    round_red_secs: string;
    starting_day: string;
    starting_hour: string;
    starting_mins: string;
    starting_month: string;
    starting_price_announcement_mins: string;
    starting_year: string;
    use_counterparty_credits: string;
}

const formRef = ref(null);
const message = useMessage();

const formModel = ref<DeAuctionSaveCommand>({
    auction_id: 'AUTO-GENERATED',
    auction_name: '',
    cost_multiplier: '',
    excess_level_0_label: '',
    excess_level_1_label: '',
    excess_level_1_quantity: '',
    excess_level_2_label: '',
    excess_level_2_quantity: '',
    excess_level_3_label: '',
    excess_level_3_quantity: '',
    excess_level_4_label: '',
    excess_level_4_quantity: '',
    month_is_1_based: false,
    price_change_initial: '',
    price_change_post_reversal: '',
    price_decimal_places: '',
    price_label: '',
    quantity_label: '',
    quantity_minimum: '',
    quantity_step: '',
    round_closed_min_secs: '',
    round_open_min_seconds: '',
    round_orange_secs: '',
    round_red_secs: '',
    starting_day: '',
    starting_hour: '',
    starting_mins: '',
    starting_month: '',
    starting_price_announcement_mins: '',
    starting_year: '',
    use_counterparty_credits: 'false',
});

const formRules = ref<FormRules>({
    auction_name: {required: true, message: 'Please enter auction name'},
    price_label: {required: true, message: 'Please enter price label'},
    quantity_label: {required: true, message: 'Please enter quantity label'},
    price_decimal_places: {required: true, type: 'number', message: 'Please enter price decimal places'},
    quantity_minimum: {required: true, type: 'number', message: 'Please enter minimum quantity'},
    quantity_step: {required: true, type: 'number', message: 'Please enter quantity step'},
    price_change_initial: {required: true, type: 'number', message: 'Please enter initial price change'},
    price_change_post_reversal: {required: true, type: 'number', message: 'Please enter price change after reversal'},
    round_open_min_seconds: {required: true, type: 'number', message: 'Please enter round open minimum seconds'},
    round_closed_min_secs: {required: true, type: 'number', message: 'Please enter round closed minimum seconds'},
    round_orange_secs: {required: true, type: 'number', message: 'Please enter round orange seconds'},
    round_red_secs: {required: true, type: 'number', message: 'Please enter round red seconds'},
    starting_year: {required: true, type: 'number', message: 'Please enter starting year'},
    starting_month: {required: true, type: 'number', message: 'Please enter starting month'},
    starting_day: {required: true, type: 'number', message: 'Please enter starting day'},
    starting_hour: {required: true, type: 'number', message: 'Please enter starting hour'},
    starting_mins: {required: true, type: 'number', message: 'Please enter starting minutes'},
    starting_price_announcement_mins: {
        required: true,
        type: 'number',
        message: 'Please enter starting price announcement minutes'
    },
});

const handleSubmit = () => {
    // formRef.value?.validate((errors) => {
    //     if (!errors) {
    //         message.success('Form submitted successfully!');
    //         console.log('Form Data:', formModel.value);
    //         // Here you would typically send the formModel.value to the server
    //     } else {
    //         message.error('Please correct the form errors.');
    //     }
    // });
};
</script>
