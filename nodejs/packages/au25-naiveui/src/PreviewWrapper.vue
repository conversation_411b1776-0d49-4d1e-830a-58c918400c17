<template>
  <div class="wrapped">
    <n-config-provider :theme="theme" :theme-overrides="themeOverrides">
      <n-loading-bar-provider>
        <n-message-provider>
          <n-notification-provider>
            <n-dialog-provider>
              <slot />
            </n-dialog-provider>
          </n-notification-provider>
        </n-message-provider>
      </n-loading-bar-provider>
      <n-global-style />
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import {
  darkTheme,
  NConfigProvider,
  NDialogProvider,
  NGlobalStyle,
  NLoadingBarProvider,
  NMessageProvider,
  NNotificationProvider,
  type GlobalThemeOverrides
} from 'naive-ui'
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import { getCurrentInstance } from 'vue'

// Create and configure pinia
const pinia = createPinia()
pinia.use(createPersistedState({
  key: id => `__persisted__${id}`
}))

// Install pinia on the current component instance
const app = getCurrentInstance()?.appContext.app
if (app) {
  app.use(pinia)
}

// Theme configuration
const theme = darkTheme
const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: '#2080f0',
    primaryColorHover: '#4098fc',
    primaryColorPressed: '#1060c9'
  }
}
</script>

<style>
.wrapped {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background: var(--n-color);
  color: var(--n-text-color);
}
</style>
