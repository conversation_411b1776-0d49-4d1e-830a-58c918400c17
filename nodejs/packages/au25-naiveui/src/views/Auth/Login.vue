<template>
	<div class="page-auth">
		<Settings v-if="!isLogged" v-model:align="align" v-model:active-color="activeColor" />

		<div v-if="!isLogged" class="flex wrapper justify-center">
			<div v-if="align === 'right'" class="image-box basis-2/3" />
			<div class="form-box basis-1/3 flex items-center justify-center" :class="{ centered: align === 'center' }">
				<AuthForm :type="type" />
			</div>
			<div v-if="align === 'left'" class="image-box basis-2/3" />
		</div>
	</div>
</template>

<script lang="ts" setup>
import type { FormType } from "@/components/auth/types.d"
import AuthForm from "@/components/auth/AuthForm.vue"
import Settings, { type Align } from "@/components/auth/Settings.vue"
import { useAuthStore } from "@/stores/auth"
import { computed, onBeforeMount, ref, toRefs } from "vue"
import { useRoute } from "vue-router"

const props = defineProps<{
	formType?: FormType
}>()
const { formType } = toRefs(props)

const route = useRoute()
const align = ref<Align>("left")
const activeColor = ref("")
const type = ref<FormType | undefined>(formType.value || undefined)
const authStore = useAuthStore()
const isLogged = computed(() => authStore.isLogged)

onBeforeMount(() => {
	if (route.query.step) {
		const step = route.query.step as FormType
		type.value = step
	}
})
</script>

<style lang="scss" scoped>
@import "./main.scss";

.page-auth {
	.wrapper {
		.image-box {
			background-color: v-bind(activeColor);
		}
	}
}
</style>
