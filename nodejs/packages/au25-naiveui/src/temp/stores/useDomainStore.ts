import { defineStore } from "pinia";
import type { Auction, Group, Host, MockObject, SystemMetric, User } from "../types/domain";

export const useDomainStore = defineStore("domainStore", {
  state: () => ({
    // AdminDashboard
    users: [] as User[],
    hosts: [] as Host[],
    notifications: [] as Notification[],
    auditLogs: [] as AuditLog[],

    // UserManagement
    groups: [] as Group[],

    // AuctionManagement
    auctions: [] as Auction[],
    financialLimits: [] as FinancialLimit[],

    // MockEnvironment
    mockObjects: [] as MockObject[],
    labels: [] as Label[],

    // ReportingDashboard
    reports: [] as Report[],

    // TraderDashboard
    traderAuctions: [] as Auction[],

    // AdminHealthMonitor
    systemMetrics: [] as SystemMetric[],
  }),

  getters: {
    // AdminDashboard Getters
    activeUsers() {
      return this.users.filter((user) => user.status === "active");
    },
    mockHosts() {
      return this.hosts.filter((host) => host.mockMode);
    },

    // UserManagement Getters
    usersByGroup: (state) => (groupId: string) => {
      return this.users.filter((user) => user.groupId === groupId);
    },

    // AuctionManagement Getters
    activeAuctions() {
      return this.auctions.filter((auction) => auction.status === "active");
    },

    // NotificationManagement Getters
    emailNotifications() {
      return this.notifications.filter(
        (notification) => notification.type === "email"
      );
    },

    // MockEnvironment Getters
    mockHostsOnly() {
      return this.hosts.filter((host) => host.mockMode);
    },

    // ReportingDashboard Getters
    completedAuctions() {
      return this.auctions.filter((auction) => auction.status === "completed");
    },

    // AdminHealthMonitor Getters
    criticalMetrics() {
      return this.systemMetrics.filter(
        (metric) => metric.status === "critical"
      );
    },
  },

  actions: {
    // AdminDashboard Actions
    async fetchUsers() {
      // Placeholder for API call to fetch users
      const response = await api.fetchUsers();
      this.users = response.data;
    },
    async fetchHosts() {
      // Placeholder for API call to fetch hosts
      const response = await api.fetchHosts();
      this.hosts = response.data;
    },
    async fetchNotifications() {
      const response = await api.fetchNotifications();
      this.notifications = response.data;
    },
    async fetchAuditLogs() {
      const response = await api.fetchAuditLogs();
      this.auditLogs = response.data;
    },
    async deleteUser(userId: string) {
      await api.deleteUser(userId);
      this.users = this.users.filter((user) => user.id !== userId);
    },
    async deleteHost(hostId: string) {
      await api.deleteHost(hostId);
      this.hosts = this.hosts.filter((host) => host.id !== hostId);
    },

    // UserManagement Actions
    async fetchGroups() {
      const response = await api.fetchGroups();
      this.groups = response.data;
    },
    async deleteGroup(groupId: string) {
      await api.deleteGroup(groupId);
      this.groups = this.groups.filter((group) => group.id !== groupId);
    },

    // AuctionManagement Actions
    async fetchAuctions() {
      const response = await api.fetchAuctions();
      this.auctions = response.data;
    },
    async setFinancialLimit(limit: FinancialLimit) {
      // Placeholder logic to set the financial limit
      this.financialLimits.push(limit);
    },
    async deleteAuction(auctionId: string) {
      await api.deleteAuction(auctionId);
      this.auctions = this.auctions.filter(
        (auction) => auction.id !== auctionId
      );
    },

    // NotificationManagement Actions
    async deleteNotification(notificationId: string) {
      await api.deleteNotification(notificationId);
      this.notifications = this.notifications.filter(
        (notification) => notification.id !== notificationId
      );
    },

    // MockEnvironment Actions
    async fetchMockObjects() {
      const response = await api.fetchMockObjects();
      this.mockObjects = response.data;
    },
    async deleteMockObject(objectId: string) {
      await api.deleteMockObject(objectId);
      this.mockObjects = this.mockObjects.filter(
        (mockObject) => mockObject.id !== objectId
      );
    },

    // ReportingDashboard Actions
    async generateReport(params: ReportParams) {
      const response = await api.generateReport(params);
      this.reports.push(response.data);
    },
    async fetchReports() {
      const response = await api.fetchReports();
      this.reports = response.data;
    },

    // TraderDashboard Actions
    async updateFinancialLimit(limit: FinancialLimit) {
      // Placeholder for updating financial limit for the trader
      const index = this.financialLimits.findIndex((fl) => fl.id === limit.id);
      if (index !== -1) {
        this.financialLimits[index] = limit;
      }
    },

    // AdminHealthMonitor Actions
    async fetchMetrics() {
      const response = await api.fetchSystemMetrics();
      this.systemMetrics = response.data;
    },
  },
});