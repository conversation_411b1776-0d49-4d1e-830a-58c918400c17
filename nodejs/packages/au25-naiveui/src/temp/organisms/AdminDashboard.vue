<!-- AdminDashboard.vue -->
<template>
    <SegmentedPage class="AdminDashboard">
      <template #sidebar-header>
        <AdminNavigationMenu />
      </template>
      <template #sidebar-content>
        <HostUserGroupList />
      </template>
      <template #sidebar-footer>
<!--        <SystemHealthIndicator />-->

      </template>
      <template #main-toolbar>
<!--        <AdminToolbar />-->
      </template>
      <template #main-content>
<!--        <AdminContentArea />-->
      </template>
      <template #main-footer>
<!--        <NotificationBanner />-->
      </template>
    </SegmentedPage>
  </template>

  <script setup lang="ts">
  import AdminNavigationMenu from "@/temp/organisms/AdminNavigationMenu.vue"
  import HostUserGroupList from "@/temp/organisms/HostUserGroupList.vue"
  import { useDomainStore } from "@/temp/stores/useDomainStore"
  import SegmentedPage from "@/components/common/SegmentedPage.vue"
  // import SystemHealthIndicator from "@/au2410/views/organisms/SystemHealthIndicator.vue"
  // import AdminToolbar from "@/au2410/views/organisms/AdminToolbar.vue"
  // import AdminContentArea from "@/au2410/views/organisms/AdminContentArea.vue"
  // import NotificationBanner from "@/au2410/views/organisms/NotificationBanner.vue"
  // import { useDomainStore } from "@/au2410/stores/useDomainStore"


  // Initialize the admin store
  const domainStore = useDomainStore();

  // TODO: Stub out any necessary script logic here
  </script>
  <style lang="scss" scoped>
  .AdminDashboard {
  }
  </style>
