// adminStore.ts

import { defineStore } from 'pinia';

/* Domain Types */

// User entity
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'Admin' | 'Auctioneer' | 'Trader' | 'ExternalObserver';
  avatar?: string; // Optional URL to avatar image
  groupId?: string; // Optional group that user belongs to
  isMock?: boolean; // Whether the user is a mock user
}

// Group entity
export interface Group {
  id: string;
  name: string;
  description: string;
  members: string[]; // List of User IDs belonging to this group
}

// Host entity
export interface Host {
  id: string;
  name: string;
  type: 'Production' | 'Mock';
  mockMode: boolean; // Whether the host is in mock mode
  maxUsers?: number; // Optional limit on the number of users
}

// Auction entity
export interface Auction {
  id: string;
  name: string;
  description?: string; // Optional description of the auction
  startingPrice: number;
  status: 'Upcoming' | 'Active' | 'Completed';
  participants: string[]; // List of Trader User IDs
  hostId: string; // ID of the associated Host
  financialLimit?: number; // Optional financial limit for participants
}

// Notification entity
export interface Notification {
  id: string;
  type: 'System' | 'Auction' | 'Custom';
  frequency: 'Immediate' | 'Daily' | 'Weekly';
  message: string;
  recipients: string[]; // List of User IDs to notify
}

// System Metric entity
export interface SystemMetric {
  id: string;
  metric: string;
  value: number;
  timestamp: string; // ISO string representing the time of the metric
}

// Mock Object entity
export interface MockObject {
  id: string;
  type: 'User' | 'Auction' | 'Host' | 'Label';
  name: string;
  description?: string;
}

// Report entity
export interface Report {
  id: string;
  title: string;
  generatedAt: string; // ISO string representing the time the report was generated
  auctionId: string; // ID of the Auction the report is related to
  content: string; // Detailed content of the report (could be in markdown or HTML)
}

/* Pinia Store State */
export interface StoreState {
  users: User[];
  groups: Group[];
  hosts: Host[];
  auctions: Auction[];
  notifications: Notification[];
  systemMetrics: SystemMetric[];
  mockObjects: MockObject[];
  reports: Report[];
}

/* Pinia Store Definition */

export const useAdminStore = defineStore('admin', {
  state: (): StoreState => ({
    users: [],
    groups: [],
    hosts: [],
    auctions: [],
    notifications: [],
    systemMetrics: [],
    mockObjects: [],
    reports: [],
  }),
  getters: {
    getUserById: (state) => (id: string): User | undefined => state.users.find((user) => user.id === id),
    getAuctionByHostId: (state) => (hostId: string): Auction[] => state.auctions.filter((auction) => auction.hostId === hostId),
    getNotificationsByType: (state) => (type: 'System' | 'Auction' | 'Custom'): Notification[] =>
      state.notifications.filter((notification) => notification.type === type),
    getMockObjectsByType: (state) => (type: 'User' | 'Auction' | 'Host' | 'Label'): MockObject[] =>
      state.mockObjects.filter((mockObject) => mockObject.type === type),
  },
  actions: {
    addUser(user: User) {
      this.users.push(user);
    },
    updateUser(user: User) {
      const index = this.users.findIndex((u) => u.id === user.id);
      if (index !== -1) {
        this.users[index] = user;
      }
    },
    deleteUser(userId: string) {
      this.users = this.users.filter((user) => user.id !== userId);
    },
    addAuction(auction: Auction) {
      this.auctions.push(auction);
    },
    updateAuction(auction: Auction) {
      const index = this.auctions.findIndex((a) => a.id === auction.id);
      if (index !== -1) {
        this.auctions[index] = auction;
      }
    },
    deleteAuction(auctionId: string) {
      this.auctions = this.auctions.filter((auction) => auction.id !== auctionId);
    },
    addGroup(group: Group) {
      this.groups.push(group);
    },
    updateGroup(group: Group) {
      const index = this.groups.findIndex((g) => g.id === group.id);
      if (index !== -1) {
        this.groups[index] = group;
      }
    },
    deleteGroup(groupId: string) {
      this.groups = this.groups.filter((group) => group.id !== groupId);
    },
    addNotification(notification: Notification) {
      this.notifications.push(notification);
    },
    updateNotification(notification: Notification) {
      const index = this.notifications.findIndex((n) => n.id === notification.id);
      if (index !== -1) {
        this.notifications[index] = notification;
      }
    },
    deleteNotification(notificationId: string) {
      this.notifications = this.notifications.filter((notification) => notification.id !== notificationId);
    },
    addMockObject(mockObject: MockObject) {
      this.mockObjects.push(mockObject);
    },
    addReport(report: Report) {
      this.reports.push(report);
    },
    updateSystemMetric(metric: SystemMetric) {
      const index = this.systemMetrics.findIndex((m) => m.id === metric.id);
      if (index !== -1) {
        this.systemMetrics[index] = metric;
      } else {
        this.systemMetrics.push(metric);
      }
    },
  },
});
