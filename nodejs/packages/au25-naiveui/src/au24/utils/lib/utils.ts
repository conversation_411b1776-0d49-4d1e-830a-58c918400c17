import { range } from "lodash"
// import * as pretty<PERSON>son from 'prettyjson';
import * as yaml from "js-yaml"

// import diff from 'jest-diff'

/**
 * THESE FUNCTIONS REALLY SHOULD BE IN THE SHARED utils LIB!
 * - there is some overlap, eg uuid()
 */

// export const sleep = (ms = 0): Promise<void> => new Promise(resolve => setTimeout(resolve, ms))

export function clampInteger(value, min = 0, max = 100) {
	return Math.round(Math.min(Math.max(value, min), max))
}

export const sleep = (ms = 0): Promise<void> => {
	return new Promise(resolve => setTimeout(resolve, ms))
}

export function is_blank(o: null | undefined | string): boolean {
	switch (o) {
		case null:
			return true
		case "undefined": // TODO: not sure if this works ??
			return true
		case "":
			return true
		default:
			return false
	}
}

export function uuid(): string {
	// simplified from CharlizeFayeServer:

	const chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("")
	let rnd = 0
	let r

	return range(36)
		.map(i => {
			if (i == 8 || i == 13 || i == 18 || i == 23) {
				return "-"
			} else if (i == 14) {
				return "4"
			} else {
				if (rnd <= 0x02) rnd = (0x2000000 + Math.random() * 0x1000000) | 0
				r = rnd & 0xf
				rnd = rnd >> 4
				return chars[i == 19 ? (r & 0x3) | 0x8 : r]
			}
		})
		.join("")
		.slice(-12)
}


// export function compareTypes(a, b) {
//   // Did broke IE10 because babel didn't transpile root node_modules
//   return
//   // const result = diff(
//   //     Object.keys(a).sort(),
//   //     Object.keys(b).sort())
//
//   // if (result.indexOf('Compared values have no visual difference.') < 0) {
//   //     console.debug(pretty(result)
//   //     error('objects should be equal ' + (a.EVENT ? a.EVENT : ''))
//   // }
// }

// export function find_by_oid(list: Oidable[], oid) {
//     return list.find(o => o.OID == oid)
// }
//
// export function remove_by_oid(list: Oidable[], oid): void {
//     const existing = find_by_oid(list, oid)
//     if (existing) {
//         list.splice(list.indexOf(existing), 1)
//     }
// }

// export function reify(o:any):any{
//     return JSON.parse(JSON.stringify(o))
// }

/* from: https://blog.usejournal.com/reactive-window-parameters-in-vuejs-fc5de75d7ab5
Usage:

// AppNav.vue
import WindowInstanceMap from './WindowInstanceMap.js'
export default {
  computed: {
    scrollY () { return WindowInstanceMap.scrollY }
  }
}

see here for throttler: https://developer.mozilla.org/en-US/docs/Web/Events/resize
see example with requestanimation frame

(function() {

  window.addEventListener("resize", resizeThrottler, false);

  var resizeTimeout;
  function resizeThrottler() {
    // ignore resize events as long as an actualResizeHandler execution is in the queue
    if ( !resizeTimeout ) {
      resizeTimeout = setTimeout(function() {
        resizeTimeout = null;
        actualResizeHandler();

       // The actualResizeHandler will execute at a rate of 15fps
       }, 66);
    }
  }

  function actualResizeHandler() {
    // handle the resize event
    ...
  }

}());

*/

// TODO: fix this:
// from: https://stackoverflow.com/questions/13815640/a-proper-wrapper-for-console-log-with-correct-line-number
// export const debug =
//   import.meta.env.VITE_APP_LOG_MESSAGES ?
//     console.log.bind(window.console) :
//     () => {
//     }
// export const debug = import.meta.env.VITE_SHOW_CONNECTOR_LOG === 'true' ? console.log.bind(window.console) : () => {
// };

export const undefined_replacer = (k, v) => (v === undefined ? "undefined" : v)

/* removing because there seems to be an error with prettyjson, it seems to use process.env? */
// export const pretty: (any) => string =
//   (msg): string => '\n' + prettyjson.render(JSON.parse(JSON.stringify(msg || {}, undefined_replacer)))

// note the library vue-json-pretty is useful in the browser
export function pretty(o: any) {
	console.log(
		yaml.dump(JSON.parse(JSON.stringify(o)), {
			// flowLevel: 3,
			// styles: {
			//   '!!int'  : 'hexadecimal',
			//   '!!null' : 'camelcase'
			// }
		})
	)
}

export function duration_ms(start: number): number {
	return Math.round(performance.now() - start)
}

export function duration_ms_str(start: number): string {
	return duration_ms(start) + " ms"
}

export function prepend_slash(s: string) {
	if (!s.startsWith("/")) s = "/" + s
	return s
}

export function perf(start: number) {
	const duration = ((performance.now() - start) / 1000 + "s").substr(0, 5)
	console.log(duration)
}
