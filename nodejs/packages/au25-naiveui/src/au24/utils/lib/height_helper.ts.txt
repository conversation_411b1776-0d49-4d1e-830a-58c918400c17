// DEPRECATED IN FAVOR OF AuScreen

import { WindowInstanceMap } from './WindowInstanceMap';

export abstract class HeightHelper {
  abstract get height(): number

  get height_str(): string {
    return (this.height || 0) + 'px'
  }
}

export class FixedHeight extends HeightHelper {
  constructor(private fixed_height: number, private limit = 0.9) {
    super()
  }

  get height(): number {
    return Math.round(Math.min(this.fixed_height, WindowInstanceMap.height * this.limit)) // not bigger than limit of the viewport
  }
}



export class OffsetHeight extends HeightHelper {
  constructor(private offset: number) {
    super()
  }

  get height(): number {
    return Math.max(WindowInstanceMap.height - this.offset, 0) // not negative
  }
}

export class OffsetMaxHeight extends HeightHelper {
  // offset height up to a max, but not negative
  constructor(private offset: number, private max: number) {
    super()
  }

  get height(): number {
    const h = Math.max(WindowInstanceMap.height - this.offset, 0) // not negative
    return Math.max(Math.min(h, this.max))
  }
}

export class PercentageHeight extends HeightHelper {
  constructor(private percentage: number) {
    super()
  }

  get height(): number {
    return Math.round(WindowInstanceMap.height * this.percentage / 100)
  }
}
