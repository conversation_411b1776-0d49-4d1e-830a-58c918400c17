import { RetrierExecutor } from "asva-executors"

export const checkUntil = async(condition: () => boolean, limit = 1000): Promise<void> => {
  return new RetrierExecutor(condition, 10, limit).run()
}

export class Deferred<T> {
  public promise: Promise<T>
  public resolved = false
  public resolveCallback: (value: T) => void

  constructor() {
    this.promise = new Promise<T>(resolve => {
      this.resolveCallback = resolve
    })
  }

  resolve(value: T = undefined): void {
    this.resolved = true
    this.resolveCallback(value)
  }
}
