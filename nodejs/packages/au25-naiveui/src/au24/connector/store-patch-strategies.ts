import { LiveClientStore } from "@/au24/types/generated.js"
import { create } from "jsondiffpatch"
import { applyPatch, compare, Operation } from "fast-json-patch"

export function patchLiveStore(current_store: LiveClientStore, new_store: LiveClientStore) {
	return patch_store_strategy_3_jsonpatch(current_store, new_store)
}

function patch_store_strategy_1_replace_all_by_key(current_store: LiveClientStore, new_store: LiveClientStore) {
	Object.keys(new_store).forEach(k => {
		//console.log(this.store[k], data.store[k]);
		current_store[k] = new_store[k]
	})
}

function patch_store_strategy_2_jsondffpatcher(current_store: LiveClientStore, new_store: LiveClientStore) {
	//  a)  generate patch operations:
	const start_compare = performance.now()
	const jsondffpatcher = create() // TODO: explore {options}: https://github.com/benjamine/jsondiffpatch#options
	const diff = jsondffpatcher.diff(current_store, new_store)
	// console.log(`jsondiffpatcher diff took: ${duration_ms_str(start_compare)}`);
	//console.log(JSON.stringify({ patches }, null, 2));

	//  b) apply patch operations:
	const start_patch = performance.now()
	jsondffpatcher.patch(current_store, diff)
	// console.log('jsondiffpatcher patch took: ' + duration_ms_str(start_patch));
}

function patch_store_strategy_3_jsonpatch(current_store: LiveClientStore, new_store: LiveClientStore) {
	// Map 14, 2024: moving this to inside of Pinia!

	const start_compare = performance.now()
	const diff: Operation[] = compare(current_store, new_store)
	// console.log(`fast-json-patch diff took: : ${duration_ms_str(start_compare)}`);
	//console.log(JSON.stringify({ patches }, null, 2));

	//  b) apply patch operations:
	const start_patch = performance.now()
	applyPatch(current_store, diff)
	// console.log('patch took: ' + duration_ms_str(start_patch));
}
