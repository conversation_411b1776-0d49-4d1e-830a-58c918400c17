import {
	AddElements,
	Browser<PERSON>essageK<PERSON>,
	ClientCommand,
	CommandSucceeded,
	SetLiveStore,
	ShowMessage,
	TerminateSession
} from "@/au24/types/generated.ts"
import { useCommandBus } from "@/au24/connector/command-buses.ts"
import { LiveStoreType } from "@/au24/stores/live-client-store.ts"

export enum IncomingCommandType {
	CommandSucceeded = "CommandSucceeded",
	ShowMessage = "ShowMessage",
	TerminateSession = "TerminateSession",
	// NetworkDown = "NetworkDown", // handled by Vueuse
	// NetworkUp = "NetworkUp",     // handled by Vueuse
	SetLiveStore = "SetLiveStore",
	AddElements = "AddElements"
}

export function handleIncomingCommand(cmd: ClientCommand, store: LiveStoreType) {
	const { commandSucceededBus, terminateSessionBus } = useCommandBus()

	const handler: { [key in keyof typeof IncomingCommandType] } = {
		[IncomingCommandType.CommandSucceeded]: (cmd: CommandSucceeded) => {
			console.log("CommandSucceeded")
			commandSucceededBus.emit(cmd as CommandSucceeded)
			window.$message.success("Command succeeded")
		},
		[IncomingCommandType.ShowMessage]: (cmd: ShowMessage) => {
			console.log("ShowMessage")
			//useShowMessageBus().emit(cmd)

			// TODO: should we have a separate message type for errors?
			// - we have Modal, Dialog, Message, Notification
			// - need to figure that out?
			if (cmd.browser_message_kind === BrowserMessageKind.ALERT) {
			}

			// need to decide between simple warnings,
			// - and form errors
			if (cmd.message.length === 1) {
				window.$dialog.warning({
					title: cmd.message[0],
					//content: cmd.message.join('\n'),
					//positiveText: 'Wow!',
					onPositiveClick: () => {
						window.$message.success("Great!")
					}
				})
			}
		},
		[IncomingCommandType.SetLiveStore]: (cmd: SetLiveStore) => {
			store?.apply_patch(cmd.store)
		},
		[IncomingCommandType.TerminateSession]: (cmd: TerminateSession) => {
			console.log("TerminateSession")
			terminateSessionBus.emit(cmd)
		},
		[IncomingCommandType.AddElements]: (cmd: AddElements) => {
			// Handle AddElements
			console.log("Adding elements to path (not implemented):", cmd.path)
		}
	}

	const commandHandler = handler[cmd.command]
	if (commandHandler) {
		commandHandler(cmd)
	} else {
		throw new Error(`Unknown command: ${cmd.command}`)
	}
}
