// Can't rely on process.env or other define plugin niceties,
// because this is a library, it has no webpack file!
// So: instead we have to pass in all config

import { uuid } from "@/au24/utils"

/**
 * if WEBSOCKET_URL defined then use it
 * else use the remote host
 */
export const to_websocket_url = (location: Location, session_id: string): string => {
	if (!import.meta.env.VITE_WEBSOCKET_URL) {
		// Intended to work with both localhost and staging/production url.
		// http://localhost:8080 -> to ws://localhost:8080/socket/123456
		// https://dev1.auctionologies.com -> to https://dev1.auctionologies.com/socket/123456
		return `${location.protocol.replace("http", "ws")}//${location.host}/socket/${session_id}`
	}
	return `${import.meta.env.VITE_WEBSOCKET_URL}/${session_id}`
}

// only used here I think:
class ClientConnectorConfig {
	readonly session_id: string = uuid()
	websocket_url_actual: string

	/**
	 * If we have WEBSOCKET_URL, and it starts with 'ws' then we use it
	 * Else we use the remote host
	 */
	setURL(location: Location, browser: { name: string; version: string; os: string } | null) {
		this.websocket_url_actual = `${to_websocket_url(location, this.session_id)}?browser_name=${browser?.name}&browser_version=${browser?.version}&browser_os=${browser?.os}`
	}

	DISCONNECT_RELOAD_MSEC = 15_000
	NODE_ENV = import.meta.env.VITE_NODE_ENV
	SESSION_PING_INTERVAL = 5_000
	SHOW_CONNECTOR_LOG = import.meta.env.VITE_SHOW_CONNECTOR_LOG
	VITE_APP_LOGINS = import.meta.env.VITE_APP_LOGINS
	VITE_APP_LOG_MESSAGES = import.meta.env.VITE_APP_LOG_MESSAGES
	VITE_APP_SHOW_DEBUG = import.meta.env.VITE_APP_SHOW_DEBUG
	WEBSOCKET_URL = import.meta.env.VITE_WEBSOCKET_URL

	is_production = this.NODE_ENV === "production"
	show_connector_log = this.SHOW_CONNECTOR_LOG === "true"
	show_debug = this.VITE_APP_SHOW_DEBUG === "true"

	// SUPPRESS_DISCONNECT: boolean;

	// msec to wait before reloading page, ie: browser-driven disconnect
	// - should be longer than the server driven
	// - TODO: if server pauses for this amount of time for any reason, then we're booted!,
	// - TODO: need to decide if we want clients to disconnect (if there is a server problem, they'll just get a blank screen!!
	// - TODO: server needs to have a better way of handling this, specifically
	//   - server needs to know if the delay was due to it's own processing !
	//   - server should allow user to force re-login
}

export const createMockConnectorConfig = (url?: string, session_id?: string): ClientConnectorConfig => ({
	websocket_url_actual: url || "ws://example.com",
	session_id: session_id || "test-session",
	show_connector_log: false,
	DISCONNECT_RELOAD_MSEC: 15000,
	NODE_ENV: "development",
	SESSION_PING_INTERVAL: 5000,
	SHOW_CONNECTOR_LOG: false,
	VITE_APP_LOGINS: false,
	VITE_APP_LOG_MESSAGES: false,
	VITE_APP_SHOW_DEBUG: false,
	WEBSOCKET_URL: "",
	is_production: false,
	show_debug: false,
	setURL(location: Location, browser: { name: string; version: string; os: string } | null) {
		// doesn't do anything, as the url is set above
	}
})
