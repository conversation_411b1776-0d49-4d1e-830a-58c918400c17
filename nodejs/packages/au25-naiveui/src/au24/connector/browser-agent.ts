// TODO: generate this from kotlin,
export interface BrowserAgent {
	ua: string
	browser: {
		name: string | undefined
		version: string | undefined
		major: string | undefined
	}
	device: {
		model: string | undefined
		type: string | undefined
		vendor: string | undefined
	}
	engine: {
		name: string | undefined
		version: string | undefined
	}
	os: {
		name: string | undefined
		version: string | undefined
	}
	cpu: {
		architecture: string | undefined
	}
}
