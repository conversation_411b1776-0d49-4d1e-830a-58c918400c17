import WS from "vitest-websocket-mock"

describe("WebSocket mock server", () => {
	beforeEach(() => {
		WS.clean()
	})

	afterEach(() => {
		WS.clean()
	})

	test("the server keeps track of received messages, and yields them as they come in", async () => {
		const server = new WS("ws://localhost:1234")
		const client = new WebSocket("ws://localhost:1234")

		await server.connected
		client.send("hello")
		await expect(server).toReceiveMessage("hello")
		expect(server).toHaveReceivedMessages(["hello"])
	})

	test("the mock server sends messages to connected clients", async () => {
		const server = new WS("ws://localhost:1234")
		const client1 = new WebSocket("ws://localhost:1234")
		await server.connected
		const client2 = new WebSocket("ws://localhost:1234")
		await server.connected

		const messages = { client1: [], client2: [] }
		client1.onmessage = e => {
			messages.client1.push(e.data)
		}
		client2.onmessage = e => {
			messages.client2.push(e.data)
		}

		server.send("hello everyone")
		expect(messages).toEqual({
			client1: ["hello everyone"],
			client2: ["hello everyone"]
		})
	})
})
