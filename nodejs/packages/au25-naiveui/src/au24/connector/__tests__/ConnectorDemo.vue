<template>
	<div>
		<NButton @click="login">login</NButton>
	</div>
</template>

<script setup lang="ts">
import { createClientConnector } from "@/au24/connector/connector.ts"
import { provide } from "vue"
import { login_command } from "@/au24/types/generated"
import { createDemo__store_for_auctioneer } from "@/au24/helpers/demo-helpers/CharlizeStore.helper.ts"
import { useDialog, useLoadingBar, useMessage, useNotification } from "naive-ui"

// from project: naive-ui-provider on github
// - allows use of these methods in any component
// - type definition is in: global.d.ts
// - added to MainContainer and ConnectorDemo
window.$message = useMessage()
window.$dialog = useDialog()
window.$notification = useNotification()
window.$loadingBar = useLoadingBar()

const connector = createClientConnector(createDemo__store_for_auctioneer(), {
	url: "ws://localhost:4040/socket/",
	session_id: "1",
	show_connector_log: true
})

provide("connector", connector)

function login() {
	connector.publish(login_command({ username: "a", password: "1" }))
}
</script>

<style scoped></style>
