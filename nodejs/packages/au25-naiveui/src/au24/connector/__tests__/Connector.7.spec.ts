import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { App, createApp, ref } from "vue"
import { IncomingCommandBus, useCommandBus } from "../command-buses"
import { WebSocketServer } from "ws"
import WS from "vitest-websocket-mock"
import { EngineCommandEnvelope } from "../../types/generated"
import { ConnectorOptions, createClientConnector, encodeAndZip } from "../connector"
import { ClientConnectorConfig } from "../ClientConnectorConfig"

export function startWebSocketServer(port: number): WebSocketServer {
	const wss = new WebSocketServer({ port })

	wss.on("connection", function connection(ws) {
		ws.on("error", console.error)

		ws.on("message", function message(data) {
			console.log("received: %s", data)
		})
	})

	return wss
}

vi.mock("@vueuse/core", async () => {
	const original = await vi.importActual("@vueuse/core")
	return {
		...original,
		useWebWorkerFn: vi.fn(fn => ({
			workerFn: (data: ArrayBuffer) => fn(data)
		}))
	}
})

describe("Client Connector", () => {
	const port1 = 1234
	const websocket_path_1 = `ws://localhost:${port1}`

	const port2 = 2345
	const websocket_path_2 = `ws://localhost:${port2}`

	const port3 = 3456
	const websocket_path_3 = `ws://localhost:${port3}`

	let wss1: WebSocketServer | null = null
	let wss2: WebSocketServer | null = null

	let app: App | null = null

	const outgoing_command: Partial<EngineCommandEnvelope> = {
		command: "test",
		session_id: "test-session"
	}

	async function cleanup(name: string, wss: WebSocketServer) {
		console.log("Cleaning up: ", name)
		if (wss) {
			// close all clients:
			wss.clients.forEach(ws => {
				console.log("Closing client: " + ws.url)
				if (ws.readyState === ws.OPEN) {
					ws.close()
				}
			})

			// Close the server
			await new Promise<void>((resolve, reject) => {
				console.log("Closing WebSocket server: " + name)
				wss.close(error => {
					if (error) {
						console.error("Failed to close WebSocket server: " + name, error)
						reject(error)
					} else {
						console.log("WebSocket server closed: " + name)
						resolve()
					}
				})
			})

			wss = null
		}
	}

	beforeEach(() => {
		WS.clean()
	})

	afterEach(async () => {
		WS.clean()
		//await cleanup(wss1)
		//await cleanup(wss2)

		if (app) {
			app.unmount()
			app = null
		}
	})

	it("1. publishes socket messages on the Incoming bus", async () => {
		// const wss = startWebSocketServer(port1)

		wss1 = startWebSocketServer(port1)

		createClientConnector({ url: websocket_path_1 })

		const receivedCommands: any[] = []
		IncomingCommandBus.on(cmd => {
			receivedCommands.push(cmd)
		})

		const command = { command: "test", data: {} }
		const zippedCommand = encodeAndZip(command)

		// Wait for the client to establish a connection
		await new Promise(resolve => setTimeout(resolve, 100))

		// Send the zipped command to the connected client
		wss1.clients.forEach(client => {
			if (client.readyState === client.OPEN) {
				client.send(zippedCommand)
			}
		})

		await new Promise(resolve => setTimeout(resolve, 100))

		expect(receivedCommands).toEqual([command])

		await cleanup("wss1", wss1)
		wss1 = null
	}, 5000)

	it("2. connects to the server and publishes messages", async () => {
		//const wss = startWebSocketServer(port2)

		const wss2 = startWebSocketServer(port2)

		const show_connector_log = ref(false)

		const clientConnectorPlugin = (app: App, options: ConnectorOptions) => {
			createClientConnector(options)
		}

		app = createApp({
			template: "<div></div>"
		})

		app.use(clientConnectorPlugin, { path: websocket_path_2 })
		app.mount(document.createElement("div"))

		// Create a promise to wait for the server to receive the expected message
		let resolveMessagePromise: (value?: unknown) => void
		const receivedMessagePromise = new Promise(resolve => {
			resolveMessagePromise = resolve
		})

		wss2.on("connection", ws => {
			ws.on("message", data => {
				const receivedMessage = JSON.parse(data.toString())
				if (receivedMessage.command === outgoing_command.command) {
					expect(receivedMessage).toEqual(outgoing_command)
					resolveMessagePromise()
				}
			})
		})

		// Wait for the client to establish a connection and send the outgoing command
		await new Promise(resolve => {
			const intervalId = setInterval(() => {
				if (wss2.clients.size > 0) {
					clearInterval(intervalId)
					useCommandBus().outgoingCommandBus.emit(outgoing_command)
					resolve(null)
				}
			}, 100)
		})

		// Wait for the server to receive the expected message
		await receivedMessagePromise

		// Cleanup
		await cleanup("wss2", wss2)
		wss2 = null
		app.unmount()
		app = null
	}, 5000)

	// using vitest-websocket-mock for the publishing:

	it("3. queues messages when offline and sends them when reconnected - using vitest-websocket-mock", async () => {
		const server = new WS(websocket_path_3)
		const show_connector_log = ref(false)

		createClientConnector({ url: websocket_path_3 })

		useCommandBus().outgoingCommandBus().emit(outgoing_command)

		// should be no messages here yet:
		expect(server).toHaveReceivedMessages([])

		await server.connected

		// now should have messages
		await expect(server).toReceiveMessage(JSON.stringify(outgoing_command))
		expect(server).toHaveReceivedMessages([JSON.stringify(outgoing_command)])
		console.log("server.messages", server.messages)
	}, 5000)
})

/*
it('queues messages when offline and sends them when reconnected', async () => {
const show_connector_log = ref(false)

const clientConnectorPlugin = (app: App, options: any) => {
	createClientConnector({
		websocket_url: options.websocket_url,
		show_connector_log: options.show_connector_log,
	})
}

const app = createApp({
  template: '<div></div>',
})
app.use(clientConnectorPlugin, {
	websocket_url,
	show_connector_log,
})
app.mount(document.createElement('div'))

// Create a promise to wait for the server to receive the expected message
let resolveMessagePromise: (value?: unknown) => void
const receivedMessagePromise = new Promise((resolve) => {
	resolveMessagePromise = resolve
})

const receivedMessages: any[] = []

wss.on('connection', (ws) => {
	ws.on('message', (data) => {
		const receivedMessage = JSON.parse(data.toString())
		receivedMessages.push(receivedMessage)
		if (receivedMessage.command === outgoing_command.command) {
			resolveMessagePromise()
		}
	})
})

// Send the outgoing command when the client is offline
useOutgoingCommandBus().emit(outgoing_command)

// Wait for a short delay to ensure no messages are received yet
await new Promise((resolve) => setTimeout(resolve, 100))
expect(receivedMessages).toHaveLength(0)

// Wait for the client to establish a connection
await new Promise((resolve) => {
	const intervalId = setInterval(() => {
		if (wss.clients.size > 0) {
			clearInterval(intervalId)
			resolve(null)
		}
	}, 100)
})

// Wait for the server to receive the expected message
await receivedMessagePromise

// Verify that the server received the expected message
expect(receivedMessages).toHaveLength(1)
expect(receivedMessages[0]).toEqual(outgoing_command)

// Cleanup
app.unmount()
})

*/

/*
it('publishes socket messages on the Incoming bus', async () => {
	const server = new WS(websocket_url)
	const show_connector_log = ref(false)

	createClientConnector({
		websocket_url,
		show_connector_log,
	})

	await server.connected
	server.server.binaryType = 'arraybuffer'

	const receivedCommands: any[] = []
	IncomingCommandBus.on((cmd) => {
		receivedCommands.push(cmd)
	})

	const command = { command: 'test', data: {} }
	const zippedCommand = encodeAndZip(command)

	// test the encodeAndZip function
	//const unzippedCommand = await unzipAndDecode(zippedCommand.buffer)
	//expect(unzippedCommand).toEqual(command)

	server.send(zippedCommand)

	await until(ref).toBe(true, { timeout: 1000 })

	expect(receivedCommands).toEqual([command])
})
 */

/*

it('increments seconds since last message when the server is offline', async () => {
	vi.useFakeTimers()

	const show_connector_log = ref(false)
	const connector = createClientConnector({
		websocket_url: 'ws://localhost:1234',
		show_connector_log,
	})

	const secondsReceived: number[] = []
	const { on: onSecondsSinceLastMessageReceived } = useSecondsSinceLastMessageReceivedBus()
	onSecondsSinceLastMessageReceived((seconds) => {
		secondsReceived.push(seconds)
	})

	vi.advanceTimersByTime(5000)

	expect(secondsReceived).toEqual([1, 2, 3, 4, 5])

	vi.useRealTimers()
})
*/
