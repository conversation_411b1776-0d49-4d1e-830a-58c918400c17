import { encodeAnd<PERSON><PERSON>, unzipAndDecode } from "../connector"

describe("encodeAndZ<PERSON>", () => {
	it("should encode and zip the input object correctly", async () => {
		const inputData = { command: "test", data: { key: "value" } }
		const zippedData = encodeAndZip(inputData)
		const result = await unzipAndDecode(zippedData.buffer)
		expect(result).toEqual(inputData)
	})

	it("should return null if the input data is invalid", async () => {
		const invalidData = new Uint8Array([1, 2, 3, 4, 5])
		const result = await unzipAndDecode(invalidData.buffer)
		expect(result).toBeNull()
	})
})
