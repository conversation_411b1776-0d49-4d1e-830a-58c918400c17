// using threads:

import * as pako from "pako"
import { expose } from "threads"

expose(function unzipAndDecode(data): any | null {
	try {
		//const start = performance.now();
		const unzipped_arr = pako.inflate(data)
		const unzipped_string = new TextDecoder().decode(unzipped_arr)
		const cmd = JSON.parse(unzipped_string)
		//console.log({ unzipped_string });
		//console.log({ cmd });
		//const duration = (performance.now() - start).toFixed(3);
		// console.log(`parsing ${cmd.command} took ${duration} ms`, { cmd });
		return cmd
	} catch (e) {
		console.error("unzipAndDecode:", e)
		return null
	}
})

