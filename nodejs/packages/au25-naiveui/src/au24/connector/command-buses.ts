import { useEventBus } from "@vueuse/core"
import {
	type CommandSucceeded,
	type EngineCommandEnvelope, type NetworkDown, type NetworkUp,
	type ShowMessage,
	type TerminateSession
} from "@/au24/types/generated.js"
import { onUnmounted } from "vue" // Adjust the import path as needed

// Define the event buses as singletons
const commandSucceededBus = useEventBus<CommandSucceeded>("CommandSucceeded")
const showMessageBus = useEventBus<ShowMessage>("ShowMessage")
const terminateSessionBus = useEventBus<TerminateSession>("TerminateSession")
const networkDownBus = useEventBus<NetworkDown>("NetworkDown")
const networkUpBus = useEventBus<NetworkUp>("NetworkUp")

const secondsSinceLastMessageReceivedBus = useEventBus<number>("SecondsSinceLastMessageReceivedBus")
const outgoingCommandBus = useEventBus<EngineCommandEnvelope>("outgoingCommandBus")

export function useCommandBus() {
	return {
		commandSucceededBus,
		showMessageBus,
		terminateSessionBus,
		networkDownBus,
		networkUpBus,
		secondsSinceLastMessageReceivedBus,
		outgoingCommandBus
	}
}

export function useAutoCommandListener<T>(bus: EventBus<T>, handler: (event: T) => void) {
	bus.on(handler)
	onUnmounted(() => {
		bus.off(handler)
	})
}

/*
USAGE:

const { showMessageBus, commandSucceededBus } = useEventBuses()

useEventListere(showMessageBus, (event: ShowMessage) => {
  console.log("Received ShowMessage event:", event)
})

OR:

const handleShowMessage = (event: ShowMessage) => {
  console.log("Received ShowMessage event:", event)
}

useEventListener(showMessageBus, handleShowMessage)

*/

// TODO: may have to implement hmr handling, not sure
