// src/au24/routes/navigation/navigation_service.ts
import { createRouter, createWebHistory, type Router, type RouteRecordRaw } from "vue-router"

import { defineStore } from "pinia"

export const useNavigationStore = defineStore("navigation-store", {
	state: () => ({
		current_page: "/",
		is_logged_in: false
	}),
	actions: {
		setPage(page: string) {
			console.log("store.setPage:", page)
			if (!this.is_logged_in && page !== "/") {
				this.current_page = "/"
			} else {
				this.current_page = page
			}
			if (this.is_logged_in && page === "/") {
				this.setLoginStatus(false)
			}
		},
		setLoginStatus(status: boolean) {
			this.is_logged_in = status
			if (status && this.current_page === "/") {
				this.current_page = "/home"
			} else if (!status && this.current_page !== "/") {
				this.current_page = "/"
			}
		}
	}
})

let navigation_service: Router | null

export function createNavigationRouter(routes: Readonly<RouteRecordRaw[]>) {
	if (!navigation_service) {
		navigation_service = createRouter({
			history: createWebHistory(),
			routes
		})
		navigation_service.beforeEach((to, from, next) => {
			const store = useNavigationStore()
			console.log("router.beforeEach: ", {
				is_logged_in: store.is_logged_in,
				current_page: store.current_page,
				to: to.path,
				from: from.path
			})

			if (!store.is_logged_in && to.path !== "/") {
				console.log('router next("/")')
				next("/")
			} else if (store.is_logged_in && to.path === "/" && from.path !== "/") {
				const confirmLogout = confirm("Do you want to log off?")
				if (confirmLogout) {
					console.log("location.reload()2")
					location.reload() // Only reload the page, do not call the store directly
				} else {
					console.log("router next(false)")
					next(false) // Cancel navigation
				}
			} else {
				next()
			}
		})
	}
	return navigation_service
}
