<template>
	<div style="border: 1px solid yellow" class="w-full h-full border-red">
		<h1>App6</h1>
		<div>Current path: {{ current_page }}</div>
		<div>Is logged in: {{ is_logged_in }}</div>
		<button @click="toggleLogin">Toggle Login</button>

		<button @click="requestPage('/')">/</button>
		<button @click="requestPage('/home')">/home</button>
		<button @click="requestPage('/auction')">/auction</button>
		<button @click="requestPage('/contact')">/contact</button>
		<button @click="requestPage('/kickout')">Kickout (Replace)</button>
		<!--		<router-view></router-view>-->
		<RouterView v-slot="{ Component }">
			<transition :name="`router-${routerTransition}`" mode="out-in" appear>
				<component
					:is="Component"
					:key="true"
					:class="[`theme-${themeName}`, `layout-VerticalNav`, themeName]"
				/>
			</transition>
		</RouterView>
	</div>
</template>

<script setup lang="ts">
import { storeToRefs } from "pinia"
import { type Component, computed, onMounted, watch } from "vue"
import { useRoute, useRouter } from "vue-router"
import { useNavigationStore } from "@/au24/routes/navigation/navigation_service.ts"
import { RouterTransition, type ThemeName } from "../../../../types/theme.d.ts"
import { useDialog, useLoadingBar, useMessage, useNotification } from "naive-ui"
import { useThemeStore } from "@/stores/theme.ts"

const routerTransition = computed<RouterTransition>(() => themeStore.routerTransition)

const message = useMessage()
const notification = useNotification()

// from project: naive-ui-provider on github
// - allows use of these methods in any component
// - type definition is in: global.d.ts
// - added to MainContainer and ConnectorDemo
window.$message = useMessage()
window.$dialog = useDialog()
window.$notification = useNotification()
window.$loadingBar = useLoadingBar()

// useCommandSucceeddedBus().on((e: CommandSucceeded) => {
// 	message.success(e.command, { closable: true })
// 	notification.success({
// 		title: e.command,
// 		content: e.command
// 	})
// })

// useShowMessageBus().on((e: ShowMessage) => {
// 	message.error(e.message.join("\n"), { closable: true })
// 	notification.error({
// 		title: e.command,
// 		content: e.message.join("\n")
// 	})
// })

const themeName = computed<ThemeName>(() => themeStore.themeName)

const themeStore = useThemeStore()

const navigation_store = useNavigationStore()
const router = useRouter()
const route = useRoute()
const { is_logged_in, current_page } = storeToRefs(navigation_store)

function toggleLogin() {
	navigation_store.setLoginStatus(!is_logged_in.value)
	console.log("toggle login: set to:", is_logged_in.value)
}

function requestPage(path: string) {
	console.log("requestPage:", path, "is_logged_in:", is_logged_in.value, "store current_page:", current_page.value)
	message.error("test")
	navigation_store.setPage(path)
}

watch(current_page, (newPage, oldPage) => {
	if (newPage !== oldPage) {
		if (newPage === route.path) {
			console.log("Same page navigation blocked")
			return
		}

		router.push({ path: newPage })
	}
})

onMounted(() => {
	navigation_store.setLoginStatus(false)
	themeStore.setTheme("dark")
})
</script>
