// src/router/index.js
import { createRouter, createWebHistory } from "vue-router"
import { createDiscrete<PERSON><PERSON> } from "naive-ui"
import { useAuAuthStore } from "@/au24/stores/au-auth-store.js"
import LoginPage from "@/au24/view/pages/login/LoginPage.vue"
import HomePage from "@/au24/view/pages/home/<USER>"
import LayoutPage from "@/au24/view/layouts/LayoutPage.vue"

const au_router = createRouter({
	history: createWebHistory(),
	routes: [
		{ path: "/", name: "Login", component: LayoutPage },
		{ path: "/home", name: "Home", component: HomePage },
		{ path: "/auction", name: "Auction", component: LoginPage }

		// Add other routes here
	]
})


window.addEventListener("beforeunload", (event) => {
	console.log("beforeunload event:", event)
	// event.preventDefault()
	// return event.returnValue = ''
})


// TODO: new suggestion from Opus3:
/*
router.beforeEach((to, from, next) => {
  if (isLoggedIn() && to.path === '/') {
    // Display confirmation dialog
    const confirmLogout = confirm('Are you sure you want to log out?');
    if (confirmLogout) {
      // Send logout command to the server
      sendLogoutCommandToServer();
      return false; // Prevent navigation
    } else {
      return false; // Prevent navigation
    }
  }

  // Send navigation request to the server
  sendNavigationRequestToServer(to.path);
  return false; // Prevent navigation
});
 */

// this works but double clicking the back button will send them to the prior page with logging them out!
au_router.beforeEach(async (to, from, next) => {
	const authStore = useAuAuthStore()
	console.log("to:", to.path, "logged in:", authStore.is_logged_in, "from:", from.path)

	if (to.path === "/" && authStore.is_logged_in && from.path !== "/") {
		try {

			const { dialog } = createDiscreteApi(["dialog"])
			//	console.log("Dialog instance created:", dialog)

			const confirmed = await new Promise((resolve) => {
				dialog.info({
					title: "Confirm Logout",
					content: "Do you want to log off?",
					positiveText: "Yes",
					negativeText: "No",
					onPositiveClick: () => resolve(true),
					onNegativeClick: () => resolve(false),
					onClose: () => resolve(false)
				})
			})

			if (confirmed) {
				const commands = await import("./outgoing-commands.ts")
				commands.logout()
				authStore.logout()
				next()
			} else {
				next("/home")
			}
		} catch (error) {
			console.error("Error in navigation guard:", error)
			next("/home")
		}
	} else {
		if (to.path !== "/" && !authStore.is_logged_in) {
			next("/")
		} else {
			next()
		}
	}
})

export default au_router

// au_router.beforeEach(async (to, from, next) => {
// 	const authStore = useAuAuthStore();
// 	const message = useMessage();
//
// 	if (to.path !== '/' && !authStore.is_logged_in) {
// 		next('/');
// 	} else if (to.path === '/' && authStore.is_logged_in && from.path !== '/') {

// 		const confirm = await new Promise((resolve) => {
// 			dialog.info({
// 				title: 'Confirm Logout',
// 				content: 'Do you want to log out?',
// 				positiveText: 'Yes',
// 				negativeText: 'No',
// 				onPositiveClick: () => resolve(true),
// 				onNegativeClick: () => resolve(false),
// 			});
// 		});
//
// 		if (confirm) {
// 			import('./outgoing-commands').then((commands) => {
// 				commands.logout();
// 				authStore.logout();
// 				next();
// 			});
// 		} else {
// 			next(false);
// 		}
// 	} else {
// 		next();
// 	}
// });
// export default au_router;
