import { createMultipleByClosure, random_bool, random_number, random_number_string, random_time } from "@/au24/utils"
import {
	type CompanyElement,
	type DeBlotter,
	type DeRoundElement,
	type DeRoundTraderElement,
	type DeTraderElement,
	OrderSubmissionType,
	OrderType,
	PriceDirection
} from "@/au24/types/generated.js"
import { flatten, random } from "lodash"

export const createDemo__excess_level = () => Array(random_number(4)).fill("+").join("") + "+"

export function createDemo__DeBlotter(companies: CompanyElement[], roundNumber: number = 0): DeBlotter {
	const traders: DeTraderElement[] = companies.map(c => createDemo__DeTraderElement(c))

	const rounds: DeRoundElement[] = createMultipleByClosure(createDemo__DeRoundElement, 20, true)

	const round_traders: DeRoundTraderElement[] = flatten(
		rounds.map(round =>
			traders.map(t =>
				createDemo__DeRoundTraderElement(round.round_number, {
					company_id: t.company_id,
					company_shortname: t.shortname
				})
			)
		)
	)

	return { rounds, traders, round_traders }
}

export function createDemo__DeRoundElement(roundNumber: number = 0): DeRoundElement {
	const buy_vol = random_number()
	const sell_vol = random_number()
	return {
		all_orders_in_next_round_will_be_mandatory: false,
		buy_quantity: buy_vol, // _string(),
		buyer_count: random_number(), // _string(),
		excess_side: OrderType.SELL,
		excess_indicator: "5+",
		excess_quantity: Math.abs(buy_vol - sell_vol),
		has_reversed: false,
		id: `ROUND.${roundNumber}`,
		match_quantity_changed: random_number(), // _string(),
		matched: random_number(), // _string(),
		potential: random_number(), // _string(),
		potential_changed: random_number(), // _string(),
		raw_matched: random_number(), //_string(),
		round_direction: roundNumber == 0 ? null : random_bool() ? PriceDirection.UP : PriceDirection.DOWN,
		round_duration: random_number_string(),
		round_number: roundNumber,
		round_price: roundNumber * (1 + random_number(100) / 100) + 0.375,
		round_price_str: random_number_string() + ".375",
		sell_quantity_change: random_number(),
		sell_quantity: sell_vol, //_string(),
		seller_count: random_number() //_string()
	}
}

export function createDemo__DeTraderElement(company: CompanyElement): DeTraderElement {
	return {
		//approved: true,
		company_id: company.company_id,
		//current_buy_max: '200.00',
		has_seen_auction: true,
		id: company.id,
		rank: random(0, 100),
		shortname: company.company_shortname
	}
}

export function createDemo__DeRoundTraderElement(
	roundNumber: number,
	company: { company_id: string; company_shortname: string }
): DeRoundTraderElement {
	const isSell = random_bool()
	const vol = random_number(50)
	return {
		bid_while_closed: false,
		buyer_credit_limit: 40_000_000.0,
		buyer_credit_limit_str: "$40,000,000.00",
		changed: false,
		cid: company.company_id,
		company_shortname: company.company_shortname,
		id: random_number_string(),
		constraints: {
			max_buy_quantity: vol + random_number(50 - vol),
			min_buy_quantity: 0,
			min_sell_quantity: 0,
			max_sell_quantity: vol + random_number(50 - vol)
		},
		match: random_number(vol),
		order_submission_type: OrderSubmissionType.MANUAL,
		order_submitted_by: "user " + company.company_id,
		order_type: random_bool() ? OrderType.SELL : OrderType.BUY,
		round: roundNumber,
		timestamp_formatted: random_time(),
		quantity_int: vol,
		quantity_str: vol + ""
	}
}
