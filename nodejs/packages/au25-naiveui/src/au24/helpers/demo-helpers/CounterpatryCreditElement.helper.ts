import { type CompanyElement, type CounterpartyCreditElement } from "@/au24/types/generated.js"
import { probability, random_digits } from "@/au24/utils"

export const createDemo__CounterpartyCreditElement = (
	buyer_id: string,
	seller_id: string
): CounterpartyCreditElement => {
	const limit = random_digits(5)
	return {
		id: random_digits(5),
		buyer_id,
		buyer_longname: "debt long",
		buyer_shortname: "debt sh",
		seller_id,
		seller_longname: "cred long",
		seller_shortname: "cred sh",
		//limit: +limit,
		limit_str: limit
	}
}

export const createDemo__CounterpartyCreditElementForCompanies = (
	companies: CompanyElement[]
): CounterpartyCreditElement[] => {
	const nodes = []
	companies.forEach(companyA => {
		companies.forEach(companyB => {
			if (companyA === companyB) {
				return
			}
			if (!probability(0.1)) {
				return
			}
			nodes.push(createDemo__CounterpartyCreditElement(companyA.id, companyB.id))
		})
	})
	return nodes
}
