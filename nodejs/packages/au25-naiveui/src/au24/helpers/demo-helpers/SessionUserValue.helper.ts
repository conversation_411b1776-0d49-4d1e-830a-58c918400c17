import { random_bool, random_enum, random_number_string, random_string } from "@/au24/utils"
import { AuUserRole, ClientSocketState, PageName, type SessionUserValue } from "@/au24/types/generated.js"

export function createDemo__SessionUserValue(): SessionUserValue {
	return {
		company_id: random_string(),
		company_longname: random_string(),
		company_shortname: random_string(),
		current_auction_id: random_string(),
		current_page: random_enum(PageName),
		isAuctioneer: random_bool(),
		isOnline: random_bool(),
		role: random_enum(AuUserRole),
		session_id: random_string(),
		socket_state: ClientSocketState.OPENED,
		user_id: random_number_string(),
		username: "user_" + random_string()
	}
}

export function createDemo_SessionUser_Auctioneer(): SessionUserValue {
	return {
		...createDemo__SessionUserValue(),
		isAuctioneer: true,
		role: AuUserRole.AUCTIONEER
	}
}
