import {
  random_number,
  random_enum,
  random_bool,
  random_string,
} from '@/au21-frontend/utils'
import {TeBidElement, TeBidState} from '../_generated/generated'

export function createDemo__TeBidElement(count: number): TeBidElement {
  return {
    allocation: '99,000,000',
    auction_id: 'allocation_id',
    bid_demand_max: '99,000,000',
    bid_demand_min: 'bid_volume_min',
    bid_rate: '10.12345',
    bid_risk_adjusted_npv: ' 99,000,000',
    bid_state: random_enum(TeBidState),
    bid_term: '500',
    company_id: '123',
    id: count + 1 + '',
    rank: 2,
    show_withdraw: random_bool(),
    timestamp: random_number({rand: 10000000, mult: 1}),
    timestamp_formatted: 'Jan 31, 18:14:30',
    user_id: '123',
    user_label: `username_${random_string(1)} (company_${random_string(3)})`,
  }
}

