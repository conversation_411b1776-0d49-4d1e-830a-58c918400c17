import type { DeCommonStatusValue } from "@/au24/types/generated.ts"
import { DeCommonState, PriceDirection } from "@/au24/types/generated.ts"

export function createDemo__DeCommonStatusValue(): DeCommonStatusValue {
	return {
		isClosed: false,
		price_direction: PriceDirection.UP,
		price_has_reversed: false,
		round_number: 5,
		round_price: "100.250",
		round_seconds: 10,
		//starting_price: '100.000',
		starting_price_announced: true,
		starting_time_text: "starting time label",
		common_state: DeCommonState.ROUND_OPEN,
		common_state_text: "common status label"
	}
}

export function createDemo__DeCommonStatusValue_setup(): DeCommonStatusValue {
	return {
		isClosed: false,
		price_direction: null,
		price_has_reversed: false,
		round_number: 1,
		round_price: "20.5",
		round_seconds: 0,
		// starting_price: null,
		starting_price_announced: true,
		starting_time_text: "",
		common_state: DeCommonState.SETUP,
		common_state_text: "label"
	}
}
