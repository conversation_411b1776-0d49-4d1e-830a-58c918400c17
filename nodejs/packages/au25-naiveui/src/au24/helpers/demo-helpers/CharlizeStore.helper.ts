import { CharlizeStore } from "../services/connector/CharlizeStore"
import { createDemo__DeAuctionValue_for_auctioneer } from "./DeAuctionValue.helper"
import { createDemo__SessionUserValue } from "./SessionUserValue.helper"
import { type CompanyElement, type UserElement } from "@/au24/types/generated.js"
import { createDemo__DeCommonStatusValue } from "./DeCommonStatusValue.helper"
import { createDefault__DeSettingsValue } from "./DeSettingsValue.helper"
import { createDemo__DeTraderInfoValue } from "./DeTrader.helper"

export function createDemo__store_for_auctioneer(
	companies: CompanyElement[] = [],
	users: UserElement[] = []
): CharlizeStore {
	return {
		stale_store: {
			stale_de_matrix_rounds: []
		},
		live_store: {
			auction_rows: [],
			companies: companies,
			counterparty_credits: [],
			de_auction: createDemo__DeAuctionValue_for_auctioneer(companies, users),
			seconds_since_last_message_received: 0,
			session_user: createDemo__SessionUserValue(),
			time: undefined,
			users: []
		}
	}
}

export function createDemo__store_for_trader(): CharlizeStore {
	return {
		stale_store: {
			stale_de_matrix_rounds: []
		},
		live_store: {
			auction_rows: [],
			companies: [],
			counterparty_credits: [],
			de_auction: {
				auction_counterparty_credits: [],
				auction_id: "auction-1",
				auctioneer_info: null,
				auctioneer_status: null,
				award_value: null,
				blotter: null,
				common_status: createDemo__DeCommonStatusValue(),
				matrix_last_round: null,
				messages: [],
				notice: "",
				settings: createDefault__DeSettingsValue(),
				trader_history_rows: [],
				trader_info: createDemo__DeTraderInfoValue(),
				users_that_have_seen_auction: []
			},
			seconds_since_last_message_received: 0,
			session_user: null,
			time: null,
			users: []
		}
	}
}
