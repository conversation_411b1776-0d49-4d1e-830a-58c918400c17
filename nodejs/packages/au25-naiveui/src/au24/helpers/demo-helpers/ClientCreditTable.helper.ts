import { random_bool, random_number_string } from "@/au24/utils"

export type ClientCreditTableRow = {
	companyId: string
	companyName: string
	//currentCredit: number | null,
	currentCreditStr: string
	newCreditStr: string
}

export type ClientCreditTableColumnParams = {
	name: "buyer" | "credit-limit" | "new-credit"
}

export const createDemo__ClientCreditTableRow = (): ClientCreditTableRow => {
	const companyId = "c." + random_number_string()
	const currentCreditStr = random_bool() ? "no limit" : `$${random_number_string()}.00`
	return {
		companyId,
		companyName: `company ${companyId}`,
		currentCreditStr: currentCreditStr,
		newCreditStr: currentCreditStr // we'll start with the same value
	}
}
