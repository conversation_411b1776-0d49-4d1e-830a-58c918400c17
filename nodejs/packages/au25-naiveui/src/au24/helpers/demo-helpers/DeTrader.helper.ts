import { random_number_old } from "@/au24/utils"
import type { type DeInitialLimits, type DeTraderInfoValue } from "@/au24/types/generated.ts"
import { OrderSubmissionType, OrderType } from "@/au24/types/generated.ts"

export function createDemo__DeTraderInfoValue(): DeTraderInfoValue {
	const quantity = random_number_old({ rand: 10, mult: 10 }) - 50

	return {
		auction_id: "1",
		award_direction: "buy",
		award_line: "award_line",
		awarded_price: "awarded_price",
		awarded_round_number: "awarded_round_number",
		awarded_value: "awarded_value",
		awarded_quantity: "19",
		initial_limits: <DeInitialLimits>{
			initial_buying_cost_limit: 50,
			initial_buying_cost_limit_str: "$50,000,000",
			initial_selling_quantity_limit: 50,
			initial_selling_quantity_limit_str: "50,000,000"
		},
		bid_constraints: {
			max_buy_quantity: quantity + 10,
			max_sell_quantity: quantity,
			min_buy_quantity: 0,
			min_sell_quantity: quantity + 5
		},
		company_id: "1",
		order_submission_type: OrderSubmissionType.MANUAL,
		order_quantity: quantity,
		order_type: Math.random() > 0.5 ? OrderType.BUY : OrderType.SELL,
		price_label: "cpp",
		round_number: 1,
		round_price: "100",
		value: "$30,000,000.00",
		quantity_label: "MMlb"
	}
}
