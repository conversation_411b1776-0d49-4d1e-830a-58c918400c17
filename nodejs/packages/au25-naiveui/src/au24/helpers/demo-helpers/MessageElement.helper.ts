import { random_bool, random_from_array, random_string } from "@/au24/utils"
import { AuMessageType, type MessageElement } from "@/au24/types/generated.js"

export const createDemo__MessageElement_auctioneer = (): MessageElement => {
	return {
		from: "auctioneer",
		to: "not implemented",
		id: "MESSAGE.2",
		message: "Message from Auctioneer" + (random_bool() && random_bool() ? random_string(50) : ""),
		message_type: random_from_array([AuMessageType.AUCTIONEER_BROADCAST, AuMessageType.TRADER_TO_AUCTIONEER]),
		message_type_label: "Auctioneer Label",
		timestamp: new Date().getTime(),
		timestamp_label: new Date().toDateString()
	}
}

export const createDemo__MessageElement_trader = (): MessageElement => {
	return {
		from: "trader",
		to: "not implemented",
		id: "MESSAGE.1",
		message: "Message from Trader" + (random_bool() && random_bool() ? random_string(50) : ""),
		message_type: random_from_array([AuMessageType.AUCTIONEER_BROADCAST, AuMessageType.TRADER_TO_AUCTIONEER]),
		message_type_label: "Trader Label",
		timestamp: new Date().getTime(),
		timestamp_label: new Date().toDateString()
	}
}
