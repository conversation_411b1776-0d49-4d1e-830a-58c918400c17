import { DeEligibilityTableRow } from "../pages/De/DeAuctioneer/eligibility_table/DeEligibilityTable.types"
import { random_number_string, random_string } from "@/au24/utils"

export function createDemo__DeEligibilityTableRow(): DeEligibilityTableRow {
	const company_id = random_number_string()
	return {
		company_id,
		company_name: random_string(),
		id: company_id,
		max_buy_quantity: random_number_string(90) + 10 + "",
		min_buy_quantity: random_number_string(10),
		min_sell_quantity: random_number_string(10),
		max_sell_quantity: random_number_string(90) + 10 + ""
	}
}
