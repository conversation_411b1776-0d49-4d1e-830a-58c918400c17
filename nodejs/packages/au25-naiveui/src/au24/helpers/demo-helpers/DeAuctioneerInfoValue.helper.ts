import { type DeAuctioneerInfoValue, DeFlowControlType } from "@/au24/types/generated.js"
import { random_number_string } from "@/au24/utils"

export const demo_controls: { [key in DeFlowControlType]: boolean } = {
	//ENGAGE_AUTO_PILOT: false,
	//DISENGAGE_AUTO_PILOT: false,
	HEARTBEAT: false,
	SET_STARTING_PRICE: false,
	ANNOUNCE_STARTING_PRICE: false,
	START_AUCTION: false,
	CLOSE_ROUND: false,
	REOPEN_ROUND: false,
	NEXT_ROUND: false,
	AWARD_AUCTION: false
}

export function createDemo__DeAuctioneerInfoValue(): DeAuctioneerInfoValue {
	return {
		allow_credit_editing: true,
		last_buyers: "",
		last_excess: "",
		last_match: "",
		last_round: 1,
		last_sell_dec: "",
		last_sellers: "",
		last_total_buy: random_number_string(1000),
		last_total_sell: random_number_string(1000),
		pen_buyers: "",
		pen_excess: "",
		pen_match: "",
		pen_round: "",
		pen_sell_dec: "",
		pen_sellers: "",
		pen_total_buy: "",
		pen_total_sell: "",
		potential: random_number_string(1000)
	}
}
