import { type DeMatrixEdgeElement } from "@/au24/types/generated.js"
import { random_number, random_number_string } from "@/au24/utils"

export function createDemo__DeMatrixEdgeElement(
	round: number = 1,
	buyCompanyId: string,
	sellCompanyId: string
): DeMatrixEdgeElement {
	const buy_limit = random_number(50)
	const sell_limit = random_number(50)
	const credit_quantity_limit = random_number(50)
	const capacity: number = Math.min(buy_limit, sell_limit, credit_quantity_limit)
	const match: number = random_number(capacity)
	const value: number = match * 1_000_000

	return {
		buy_quantity_limit: 0,
		buyer_cid: buyCompanyId,
		buyer_shortname: "b " + buyCompanyId,
		capacity,
		credit_str: "no limit",
		//  credit_str: "$1,000,000.00",
		credit_quantity_limit: credit_quantity_limit,
		id: random_number_string(),
		match,
		r: round,
		selling_quantity_limit: 0,
		seller_cid: sellCompanyId,
		seller_shortname: "s " + sellCompanyId,
		value,
		value_str: `$${value}.00`
	}
}
