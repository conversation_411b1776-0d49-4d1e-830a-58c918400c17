import { type CounterpartyCreditElement } from "@/au24/types/generated.js"

export function createDemo__CounterpartyCreditElement(id: number): CounterpartyCreditElement {
	const limit = Math.ceil(Math.random() * 200) + ""

	return {
		buyer_id: id + 100 + "",
		buyer_longname: `Debtor ${id + 200}`,
		buyer_shortname: `buyer_${id + 200}`,
		id: id + "",
		seller_id: id + 100 + "",
		seller_longname: `Creditor ${id + 200}`,
		seller_shortname: `seller_${id + 200}`,
		//limit: random_number_old(),
		limit_str: limit + ".00"
	}
}
