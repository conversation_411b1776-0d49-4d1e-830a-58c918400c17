import { type DeBidConstraints, OrderType, PriceDirection } from "@/au24/types/generated.js"

/**
 *
 * Must keep in sync with backend
 * TODO: we should make this type-safe like in Kotlin  When statements
 */
export function calculate_constraints(o: {
	prev_constraints: DeBidConstraints
	order_type: OrderType
	order_quantity: number
	next_round_direction: PriceDirection
}): DeBidConstraints {
	if (o.order_type == OrderType.NONE) {
		if (o.next_round_direction == PriceDirection.UP) {
			return {
				min_buy_quantity: 0,
				max_buy_quantity: 0,
				min_sell_quantity: 0,
				max_sell_quantity: o.prev_constraints.max_sell_quantity
			} as DeBidConstraints
		} else if (o.next_round_direction == PriceDirection.DOWN) {
			return {
				min_buy_quantity: 0,
				max_buy_quantity: o.prev_constraints.max_buy_quantity,
				min_sell_quantity: 0,
				max_sell_quantity: 0
			} as DeBidConstraints
		}
	} else if (o.order_type == OrderType.BUY) {
		if (o.next_round_direction == PriceDirection.UP) {
			return {
				min_buy_quantity: o.prev_constraints.min_buy_quantity,
				max_buy_quantity: o.order_quantity,
				min_sell_quantity: o.prev_constraints.min_sell_quantity,
				max_sell_quantity: o.prev_constraints.max_sell_quantity
			} as DeBidConstraints
		} else if (o.next_round_direction == PriceDirection.DOWN) {
			return {
				min_buy_quantity: o.order_quantity,
				max_buy_quantity: o.prev_constraints.max_buy_quantity,
				min_sell_quantity: 0,
				max_sell_quantity: 0
			} as DeBidConstraints
		}
	} else if (o.order_type == OrderType.SELL) {
		if (o.next_round_direction == PriceDirection.UP) {
			return {
				min_buy_quantity: 0,
				max_buy_quantity: 0,
				min_sell_quantity: o.order_quantity,
				max_sell_quantity: o.prev_constraints.max_sell_quantity
			} as DeBidConstraints
		} else if (o.next_round_direction == PriceDirection.DOWN) {
			return {
				min_buy_quantity: o.prev_constraints.min_buy_quantity,
				max_buy_quantity: o.prev_constraints.max_buy_quantity,
				min_sell_quantity: o.prev_constraints.min_sell_quantity,
				max_sell_quantity: o.order_quantity
			} as DeBidConstraints
		}
	}
	alert("Unable to calculate BidConstraints")
	throw new Error("Unable to calculate BidConstraints")
}
