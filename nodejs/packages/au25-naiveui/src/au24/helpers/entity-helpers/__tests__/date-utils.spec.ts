import { Moment } from "moment"
import { DateTimeValue } from "../../../types/generated"
import { dateTimeValue_to_moment, moment_to_DateTimeValue } from "../DateTimeValue"

describe("date-utils", () => {
	it("moment_to_DateTimeValue + dateTimeValue_to_moment", async () => {
		const dateTimeValue: DateTimeValue = {
			day_of_week: 0,
			day_of_month: 3,
			hour: 2,
			minutes: 8,
			month: 1,
			seconds: 31,
			year: 2020
		}
		const momentValue: Moment = dateTimeValue_to_moment(dateTimeValue)
		const result: DateTimeValue = moment_to_DateTimeValue(momentValue, false)
		// day_of_week is merely presentational and ignored in translation, so we just assign it.
		dateTimeValue.day_of_week = result.day_of_week

		expect(dateTimeValue).toEqual(result)
	})
})
