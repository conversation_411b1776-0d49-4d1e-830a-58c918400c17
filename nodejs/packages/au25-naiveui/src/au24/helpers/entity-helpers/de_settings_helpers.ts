import { date_to_dateTimeValue, dateTimeValue_to_date } from "@/au24/helpers/entity-helpers/date-time-helper.ts"
import type { type DeSettingsValue } from "@/au24/types/generated"

export interface DeSettingsFormModel {
	auctionName: string
	startingTime: Date | null
	minsBeforeStart: number
	showOrangeAfter: number
	showRedAfter: number
	initialPriceChange: number
	postReversalPriceChange: number
	priceLabel: string
	decimalPlaces: number
	category: string
	quantityLabel: string
	quantityDecrement: number
	minimumQuantity: number
	sellerQuantityLimit: number
	buyerCreditLimit: number
	excessLevel0Label: string
	//excessLevel0Quantity: 0;
	excessLevel1Label: string
	excessLevel1Quantity: number
	excessLevel2Label: string
	excessLevel2Quantity: number
	excessLevel3Label: string
	excessLevel3Quantity: number
	excessLevel4Label: string
	excessLevel4Quantity: number
}

export const formModelToSettingsValue = (formModel: DeSettingsFormModel): DeSettingsValue => {
	return {
		auction_name: formModel.auctionName,
		cost_multiplier: "", // Assuming there's no corresponding field in DeSettingsFormModel
		excess_level_0_label: formModel.excessLevel0Label,
		excess_level_1_label: formModel.excessLevel1Label,
		excess_level_1_quantity: formModel.excessLevel1Quantity.toString(),
		excess_level_2_label: formModel.excessLevel2Label,
		excess_level_2_quantity: formModel.excessLevel2Quantity.toString(),
		excess_level_3_label: formModel.excessLevel3Label,
		excess_level_3_quantity: formModel.excessLevel3Quantity.toString(),
		excess_level_4_label: formModel.excessLevel4Label,
		excess_level_4_quantity: formModel.excessLevel4Quantity.toString(),
		price_change_initial: formModel.initialPriceChange.toString(),
		price_change_post_reversal: formModel.postReversalPriceChange.toString(),
		price_decimal_places: formModel.decimalPlaces,
		price_label: formModel.priceLabel,
		quantity_label: formModel.quantityLabel,
		quantity_minimum: formModel.minimumQuantity.toString(),
		quantity_step: formModel.quantityDecrement?.toString() || "",
		round_closed_min_secs: 0, // Assuming there's no corresponding field in DeSettingsFormModel
		round_open_min_secs: 0, // Assuming there's no corresponding field in DeSettingsFormModel
		round_orange_secs: formModel.showOrangeAfter,
		round_red_secs: formModel.showRedAfter,
		starting_price_announcement_mins: formModel.minsBeforeStart || 0,
		starting_time: formModel.startingTime ? date_to_dateTimeValue(formModel.startingTime) : null,
		use_counterparty_credits: formModel.buyerCreditLimit !== undefined
	}
}

export const settingsValueToFormModel = (settingsValue: DeSettingsValue | null): DeSettingsFormModel => {
	if (!settingsValue) {
		return {
			auctionName: "",
			startingTime: new Date(),
			minsBeforeStart: 0,
			showOrangeAfter: 0,
			showRedAfter: 0,
			initialPriceChange: 0,
			postReversalPriceChange: 0,
			priceLabel: "",
			decimalPlaces: 0,
			category: "",
			quantityLabel: "",
			quantityDecrement: 1,
			minimumQuantity: 1,
			sellerQuantityLimit: 0,
			buyerCreditLimit: 0,
			excessLevel0Label: "",
			excessLevel1Label: "",
			excessLevel1Quantity: 0,
			excessLevel2Label: "",
			excessLevel2Quantity: 0,
			excessLevel3Label: "",
			excessLevel3Quantity: 0,
			excessLevel4Label: "",
			excessLevel4Quantity: 0
		}
	}

	return {
		auctionName: settingsValue.auction_name,
		startingTime: settingsValue.starting_time ? dateTimeValue_to_date(settingsValue.starting_time) : null,
		minsBeforeStart: settingsValue.starting_price_announcement_mins,
		showOrangeAfter: settingsValue.round_orange_secs,
		showRedAfter: settingsValue.round_red_secs,
		initialPriceChange: parseFloat(settingsValue.price_change_initial),
		postReversalPriceChange: parseFloat(settingsValue.price_change_post_reversal),
		priceLabel: settingsValue.price_label,
		decimalPlaces: settingsValue.price_decimal_places,
		category: "", // Assuming there's no corresponding field in DeSettingsValue
		quantityLabel: settingsValue.quantity_label,
		quantityDecrement: settingsValue.quantity_step ? parseFloat(settingsValue.quantity_step) : 1,
		minimumQuantity: parseFloat(settingsValue.quantity_minimum),
		sellerQuantityLimit: 0, // Assuming there's no corresponding field in DeSettingsValue
		buyerCreditLimit: 0, //settingsValue.use_counterparty_credits ? null : 0, // Adjust based on your requirements
		excessLevel0Label: settingsValue.excess_level_0_label,
		excessLevel1Label: settingsValue.excess_level_1_label,
		excessLevel1Quantity: parseFloat(settingsValue.excess_level_1_quantity),
		excessLevel2Label: settingsValue.excess_level_2_label,
		excessLevel2Quantity: parseFloat(settingsValue.excess_level_2_quantity),
		excessLevel3Label: settingsValue.excess_level_3_label,
		excessLevel3Quantity: parseFloat(settingsValue.excess_level_3_quantity),
		excessLevel4Label: settingsValue.excess_level_4_label,
		excessLevel4Quantity: parseFloat(settingsValue.excess_level_4_quantity)
	}
}
