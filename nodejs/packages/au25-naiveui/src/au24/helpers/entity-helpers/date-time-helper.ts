/*
TODO This function should strictly do conversion, no mapping for specific UIs.
    NB: DateTimeValue is now 0-based, like the date-time-picker
 */

//import { Moment } from "moment"

import type { type DateTimeValue } from "@/au24/types/generated"

export const nowDateTimeWithZeroSeconds = (): DateTimeValue => {
	const now = new Date()

	return {
		day_of_month: now.getDate(),
		day_of_week: now.getDay(),
		hour: now.getHours(),
		minutes: now.getMinutes(),
		month: now.getMonth() + 1, // JavaScript months are 0-indexed
		seconds: 0, // Set seconds to zero
		year: now.getFullYear()
		// Add any other properties from StoreValue if necessary
	}
}

export const dateTimeValue_to_date = (dateTimeValue: DateTimeValue): Date => {
	return new Date(
		dateTimeValue.year,
		dateTimeValue.month - 1, // JavaScript months are 0-indexed
		dateTimeValue.day_of_month,
		dateTimeValue.hour,
		dateTimeValue.minutes,
		dateTimeValue.seconds
	)
}

export const date_to_dateTimeValue = (date: Date): DateTimeValue => {
	return {
		day_of_month: date.getDate(),
		day_of_week: date.getDay(),
		hour: date.getHours(),
		minutes: date.getMinutes(),
		month: date.getMonth() + 1, // JavaScript months are 0-indexed
		seconds: date.getSeconds(),
		year: date.getFullYear()
	}
}

// export const dateTimeValue_to_dayjs = (dateTimeValue: DateTimeValue): dayjs.Dayjs => {
//   return dayjs({
//     year: dateTimeValue.year,
//     month: dateTimeValue.month - 1, // dayjs months are 0-indexed
//     day: dateTimeValue.day_of_month,
//     hour: dateTimeValue.hour,
//     minute: dateTimeValue.minutes,
//     second: dateTimeValue.seconds,
//   });
// };


// export const moment_to_DateTimeValue = (moment: Moment, zero_seconds = false): DateTimeValue => {
//   return {
//     year: moment.year(),
//     month: moment.month(), // reverted to zero based months
//     day_of_month: moment.date(),
//     day_of_week: moment.day(),
//     hour: moment.hour(),
//     minutes: moment.minutes(),
//     seconds: zero_seconds ? 0 : moment.seconds(),
//   }
// }
//
// export const dateTimeValue_to_moment = (dateTimeValue: DateTimeValue): Moment => {
//   return moment({
//     year: dateTimeValue.year,
//     month: dateTimeValue.month,
//     day: dateTimeValue.day_of_month,
//     hours: dateTimeValue.hour,
//     minutes: dateTimeValue.minutes,
//     seconds: dateTimeValue.seconds,
//   })
// }
//
// export const nowDateTimeWithZeroSeconds = (): DateTimeValue => {
//   return moment_to_DateTimeValue(moment(), true);
// }
