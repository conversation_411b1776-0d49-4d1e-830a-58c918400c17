import { AuUserRole, type SessionUserValue } from "@/au24/types/generated.js"

const roles: AuUserRole[] = [
	AuUserRole.TRADER,
	AuUserRole.AUCTIONEER
	//  AuUserRole.ADMIN, // put back: users can be one of: TRADER/AUCTIONEER/ADMIN, and non-admins can be tester +/- observer
]

const roleToOptionNameMap: Record<AuUserRole, string> = {
	[AuUserRole.TRADER]: "Trader",
	[AuUserRole.AUCTIONEER]: "Auctioneer"
	//  [AuUserRole.ADMIN]: 'Administrator', // added ADMIN back in (TODO: we need to remove Admin for now)
}

export const getAuUserRoleName = (role: AuUserRole | null): string | null => (role ? roleToOptionNameMap[role] : null)

export const getAuUserRoleOptions = () => roles.map(role => ({ value: role, name: getAuUserRoleName(role) }))

export const isAuctioneerOrAdmin = (session: SessionUserValue | null) =>
	session == null ? false : session.isAuctioneer // [AuUserRole.AUCTIONEER, AuUserRole.ADMIN].includes(session.role)
