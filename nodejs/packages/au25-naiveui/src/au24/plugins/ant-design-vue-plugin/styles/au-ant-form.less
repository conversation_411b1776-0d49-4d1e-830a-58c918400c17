@import (reference) "../ant-design-vue-plugin";

// CALENDAR

.ant-calendar-picker {
  i.ant-calendar-picker-clear {
    background-color: @inputColor !important;
  }
}

.ant-calendar-picker-container {
  .ant-calendar-input {
    background-color: @inputColor;
    border-color: @inputBorderColor;
  }
}


// INPUT

.ant-input {
  background-color: @inputColor;
  border-color: @inputBorderColor;
  padding-left: 3px;
  padding-right: 3px;
  padding-top: 1px;
  padding-bottom: 1px;
  border-radius: 0;

  &--disabled {
    //background-color: @au-pseudo-input-color;
    border: solid @au-background 1px !important;
    border-radius: 4px !important;
    color: black;
    cursor: text;
  }
}

.ant-input-number {
  background-color: @inputColor;
  border-color: @inputBorderColor;
}

// FORM

.ant-form-item {
  color: white;
}

.ant-form-item-label label {
  color: white;
}

// LIST

.ant-list {
  color: black;
}

.ant-empty-image {
  display: none;
}


// SELECT

.ant-select {
  &.ant-select-disabled {
    .ant-select-selection {
      background-color: @disabledColor;
      border-color: @inputBorderColor;
    }
  }

  &.ant-select-enabled {
    .ant-select-selection {
      background-color: @inputColor;
      border-color: @inputBorderColor;
    }
  }
}

