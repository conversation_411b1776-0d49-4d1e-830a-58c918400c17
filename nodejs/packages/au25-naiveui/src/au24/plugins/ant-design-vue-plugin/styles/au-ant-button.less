@import (reference) "../ant-design-vue-plugin";
@import (reference) "../../../au-styles/variables";
// ant-btn:

@btn-padding-base_state: 0 @padding-md - 1px;

@au-btn-background-color: hsl(0, 0%, 65%);
@au-btn-background-color-disabled: hsl(0, 0%, 50%);
@au-btn-border-color: hsl(0, 0%, 75%);
@au-btn-hover-color: hsl(0, 0%, 90%);

//.au-btn {
//  border: 1px solid @au-primary-color-disabled;
//}

.ant-btn .ant-btn-default {
  // border: 0;
  // background-image: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.5) 51%);
}

.ant-btn-group {
  border-left-color: @au-btn-border-color;
  border-right-color: @au-btn-border-color;
}

.ant-btn-primary {
  background-color: @au-btn-background-color; // @au-primary-color;
  border-color: @au-btn-border-color; // @au-primary-color;
  color: black !important;

  &:disabled { // was: .ant-btn-primary[disabled]
    background-color: @au-btn-background-color-disabled; // @au-primary-color-disabled;
  }

  &:hover {
    background-color: @au-btn-hover-color;
    border-color: @au-btn-border-color;
  }

  &:hover:disabled {
    background-color: @au-btn-background-color-disabled;
  }
}
