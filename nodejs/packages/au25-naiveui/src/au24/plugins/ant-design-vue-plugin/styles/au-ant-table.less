@import (reference) "../ant-design-vue-plugin";

// TABLE
.ant-table-wrapper {
  background-color: white;
}

.ant-table {
  .ant-table-thead > tr:first-child > th:last-child {
    border-top-right-radius: 0;
  }

  table {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  td {
    border: solid #e7e7e7 1px !important;

  }

  .ant-table-thead > tr > th {
    font-weight: 700;
  }

  // NOTE This removes scroll from header making create_scenario_result_rows look nicer.
  // But the downside is that create_scenario_result_rows columns width is not calculated properly,
  // so head and body columns get out of sync.
  &.ant-table-fixed-header .ant-table-scroll {
    //.ant-create_scenario_result_rows-header {
    //  overflow-y: auto !important;
    //}
    //.ant-create_scenario_result_rows-body {
    //  overflow-y: auto !important;
    //}
  }

  th {
    background-color: #c3c3c3;
    border: solid 1px #696969 !important;
  }

  th, td {
    background-color: white;
  }
}

.ant-table-thead > tr:hover:not(.ant-table-expanded-row) > td, .ant-table-tbody > tr:hover:not(.ant-table-expanded-row) > td {
  background: @au-background-very-light;
}
