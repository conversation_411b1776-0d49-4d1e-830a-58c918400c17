import "./ant-design-vue-plugin.less"

import {
	<PERSON><PERSON>,
	Card,
	Checkbox,
	Col,
	DatePicker,
	Divider,
	Dropdown,
	Form,
	Icon,
	Input,
	InputNumber,
	Layout,
	List,
	Modal,
	notification,
	Popconfirm,
	Radio,
	Row,
	Select,
	Slider,
	Spin,
	Switch,
	Table,
	Tabs,
	TimePicker
} from "ant-design-vue"
import Vue from "vue"

// We serve demo-components similarly to how ant-design itself does
// See `node_modules/ant-design-vue/es/index.js`

const components = [
	But<PERSON>,
	Card,
	Checkbox,
	Col,
	DatePicker,
	Divider,
	Dropdown,
	Form,
	Icon,
	Input,
	InputNumber,
	Layout,
	List,
	Modal,
	Popconfirm,
	Radio,
	Row,
	Select,
	Slider,
	Spin,
	Switch,
	Table,
	Tabs,
	TimePicker
]

components.forEach(component => Vue.use(component))

Vue.prototype.$notification = notification
Vue.prototype.$modal = Modal
declare module "vue/types/vue" {
	interface Vue {
		$notification: typeof notification
		$modal: typeof Modal
		$vb: { lorem: (limit: number) => string }
	}
}
