// @import '~ant-design-vue/dist/antd.less';
@import "./from-old-node-modules/distX/antd";
@import "../../au-styles/variables";

@import "styles/au-ant-alerts";
@import "styles/au-ant-button";
@import "styles/au-ant-card";
@import "styles/au-ant-form";
@import "styles/au-ant-modal";
@import "styles/au-ant-slider";
@import "styles/au-ant-table";

// VARIABLES:

// NB: default values are here:
// - https://github.com/vueComponent/ant-design-vue/blob/master/components/style/themes/default.less

@font-family: Helvetica;

@card-shadow: none;
@card-head-padding: 6px;
@card-padding-base_state: 0;
@card-padding-wider: 32px;

@btn-font-weight: 'normal';
@btn-font-size-sm: 12px;
@btn-height-sm: 20px;


// change checkbox color
.ant-checkbox-inner {
  background-color: @au-primary-color;
}


input.ant-input-number-input, input.ant-input {
  border-radius: 0;
  transition: box-shadow 0.1s;

  &:focus {
    box-shadow: 0 0 8px 3px rgb(167, 221, 238);
  }
}


.ant-modal-body {
  background-color: @au-background-light;
  color: @au-text-color;
  padding: 6px;
}

// RADIO

.ant-radio-wrapper {
  color: @au-text-color;

  span.ant-radio + * {
    padding-left: 4px;
    padding-right: 4px;
  }
}

.ant-radio-disabled + span {
  color: @au-text-color;
  opacity: 0.5;
}

.ant-radio-disabled .ant-radio-inner:after {
  background-color: @au-background;
}


// modal

.ant-modal-body {
  color: @dark-bg-color;
  padding: 0;
  border: 0;
  margin: 0;
}
