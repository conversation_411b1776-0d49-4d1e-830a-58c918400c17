import {CharlizeStore} from '../../services/connector/CharlizeStore';
import {WindowInstanceMap} from '@au21-frontend/utils';
import {Container, OnlyInstantiableByContainer, Singleton} from 'typescript-ioc';
import {AuUserRole} from "@au21-frontend/client-connector";

// Service intended to reactively monitor screen changes as well as expose them in easily accessible way.

/*
 *  NEED TO REMOVE THIS FROM Container SO that it can be used in demos
 */

//@Singleton
//@OnlyInstantiableByContainer
export class AuScreen {

  static readonly MIN_HEIGHT = 400
  static readonly AUCTIONEER_NAVBAR_HEIGHT = 90
  static readonly TRADER_NAVBAR_HEIGHT = 90 // includes Login page btw

  store = Container.get(CharlizeStore)

  get is_auctioneer(): boolean {
    return this.store.live_store.session_user?.role == AuUserRole.AUCTIONEER
  }

  get height_outer() {
    return WindowInstanceMap.height
  }

  get layout_height():number{
    return Math.max(AuScreen.MIN_HEIGHT, this.height_outer)
  }

  // NB: PA<PERSON> HEIGHT IS THE AREA BELOW THE NAVBAR TO THE BOTTOM OF THE SCREEN
  get page_height() {
     return this.layout_height - (this.is_auctioneer ?
        AuScreen.AUCTIONEER_NAVBAR_HEIGHT :
        AuScreen.TRADER_NAVBAR_HEIGHT)
  }

  //
  // get width_outer_px() {
  //   return this.store.app_width + 'px'
  // }

  get app_width(): number {
    return this.is_auctioneer ? 1620 : 920;
  }

  get width_inner(): number {
    return this.app_width - 16
  }

  // get width_inner_px() {
  //   return this.width_inner + 'px'
  // }

  get modal_height(): number | null {
    return this.page_height - 30
  }
}
