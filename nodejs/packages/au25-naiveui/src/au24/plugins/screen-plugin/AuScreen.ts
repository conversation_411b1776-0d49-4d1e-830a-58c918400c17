import { computed, ComputedRef, Ref, ref } from "vue"
import { useWindowSize } from "@vueuse/core"

const MIN_HEIGHT = 400
const AUCTIONEER_NAVBAR_HEIGHT = 85
const TRADER_NAVBAR_HEIGHT = 70

// not sure where to put this, idea is to support numbers or strings, eg: '100%' or '100vw' etc
// export const WidthType = [Number, String]
// export const HeightType = [Number, String]

interface AuScreen {
	app_width: Ref<number>
	height_outer: Ref<number>
	layout_height: ComputedRef<number>
	modal_height: ComputedRef<number>
	page_height: ComputedRef<number>
	width_inner: ComputedRef<number>
}

let _instance: AuScreen | null = null
let _isAuctioneer: boolean = false

export function useAuScreen(isAuctioneer: boolean = false): AuScreen {
	if (_instance) {
		// can't implement this otherwise we have to pass isAuctioneer every time!
		// if (_isAuctioneer !== isAuctioneer) {
		// 	console.error("useAuScreen: isAuctioneer cannot be changed after first use.")
		// }
		return _instance
	}

	_isAuctioneer = isAuctioneer

	console.log(`useAuScreen: isAuctioneer set to  ${isAuctioneer} for this entire session`)

	const { height: height_outer } = useWindowSize()

	const layout_height = computed(() => {
		return Math.max(MIN_HEIGHT, height_outer.value)
	})

	const page_height = computed(() => {
		return layout_height.value - (isAuctioneer ? AUCTIONEER_NAVBAR_HEIGHT : TRADER_NAVBAR_HEIGHT)
	})

	const app_width = ref<number>(isAuctioneer ? 1620 : 920)

	const width_inner = computed(() => {
		return app_width.value - 16
	})

	const modal_height = computed(() => {
		return page_height.value - 60
	})

	_instance = {
		app_width,
		height_outer,
		layout_height,
		modal_height,
		page_height,
		width_inner
	}

	return _instance
}
