<template>
	<n-modal v-model:show="show">
		<n-card
			style="width: 600px"
			title="Alert"
			:bordered="true"
			size="huge"
			role="dialog"
			aria-modal="true"
			closable
			@close="show = false"
		>
			<template #header-extra></template>
			<div v-for="(message, index) in messages" :key="index">
				{{ message }}
			</div>
			<template #footer></template>
		</n-card>
	</n-modal>
</template>

<script setup lang="ts">
import { useAutoCommandListener, useCommandBus } from "@/au24/connector/command-buses.ts"
import { ShowMessage } from "@/au24/types/generated.ts"
import { ref } from "vue"

const show = ref(false)
const messages = ref<string[]>([])
const { showMessageBus } = useCommandBus()

useAutoCommandListener(showMessageBus, (e: ShowMessage) => {
	show.value = true
	messages.value = e.message
})
</script>
