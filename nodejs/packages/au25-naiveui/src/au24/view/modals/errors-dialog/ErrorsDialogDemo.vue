<!-- ErrorsDialogDemo.vue -->
<template>
	<div class="errors-dialog-demo">
		<h2>Errors Dialog Demo</h2>
		<n-space vertical>
			<n-button @click="showErrorMessage">Show Error Message</n-button>
			<n-button @click="simulateCommandSucceeded">Simulate Command Succeeded</n-button>
		</n-space>
	</div>
</template>

<script setup lang="ts">
import { NButton, NSpace } from "naive-ui"
import { useCommandBus } from "@/au24/connector/command-buses.ts"
import { BrowserMessageKind, CommandSucceeded, ShowMessage } from "@/au24/types/generated.ts"
import { IncomingCommandType } from "@/au24/connector/incoming-command-handler.ts"

const { showMessageBus, commandSucceededBus } = useCommandBus()

const errorMessages = [
	["Error: Unable to connect to the server.", "Please check your internet connection and try again."],
	["Error: Invalid input detected.", "Please ensure all required fields are filled correctly."],
	["Error: Operation timed out.", "The server took too long to respond.", "Please try again later."],
	[
		"Error: Insufficient permissions.",
		"You do not have the necessary rights to perform this action.",
		"Please contact your system administrator."
	]
]

const showErrorMessage = () => {
	console.log("Emitting ShowMessage event")
	const randomMessage = errorMessages[Math.floor(Math.random() * errorMessages.length)]
	const message: ShowMessage = {
		command: IncomingCommandType.ShowMessage,
		browser_message_kind: BrowserMessageKind.ALERT,
		message: errorMessages[0]! // randomMessage
	}
	showMessageBus.emit(message)
}

const simulateCommandSucceeded = () => {
	console.log("Simulating Command Succeeded")
	showErrorMessage()
	setTimeout(() => {
		const successEvent: CommandSucceeded = {
			command: IncomingCommandType.CommandSucceeded
			// Add any other properties required by CommandSucceeded
		}
		commandSucceededBus.emit(successEvent)
	}, 500)
}
</script>

<style scoped>
.errors-dialog-demo {
	padding: 20px;
}
</style>
