<!-- ErrorsDialog.vue -->
<template>
	<teleport to="body">
		<div v-if="showDialog" class="global-error-dialog-container">
			<n-dialog
				v-model:show="showDialog"
				title="Error"
				type="error"
				:closable="true"
				@close="handleClose"
				:style="{ zIndex: 10000 }"
				:bordered="true"
			>
				<template #default>
					<div v-for="(message, index) in messages" :key="index">
						{{ message }}
					</div>
				</template>
				<template #action>
					<n-button @click="handleClose">Close</n-button>
				</template>
			</n-dialog>
		</div>
	</teleport>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { NButton, NDialog } from "naive-ui"
import { useAutoCommandListener, useCommandBus } from "@/au24/connector/command-buses.ts"
import { BrowserMessageKind, CommandSucceeded, ShowMessage } from "@/au24/types/generated.ts"

const showDialog = ref(false)
const messages = ref<string[]>([])

const { showMessageBus } = useCommandBus()
const { commandSucceededBus } = useCommandBus()

useAutoCommandListener(showMessageBus, (event: ShowMessage) => {
	console.log("Received ShowMessage event:", event)
	if (event.browser_message_kind === BrowserMessageKind.ALERT) {
		messages.value = event.message
		showDialog.value = true
		console.log("Dialog opened, messages:", messages.value)
	}
})

useAutoCommandListener(commandSucceededBus, (event: CommandSucceeded) => {
	console.log("Received CommandSucceeded event:", event)
	handleClose()
})

const handleClose = () => {
	showDialog.value = false
	messages.value = []
	console.log("Dialog closed")
}

watch(
	() => showDialog.value,
	newValue => {
		console.log("showDialog changed:", newValue)
	}
)
</script>

<style scoped>
.global-error-dialog-container {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}
</style>
