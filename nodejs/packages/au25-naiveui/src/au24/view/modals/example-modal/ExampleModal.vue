<template>
	<n-button @click="showModal = true">Start me up</n-button>
	<n-modal v-model:show="showModal">
		<n-card
			style="width: 600px"
			title="Modal"
			:bordered="false"
			size="huge"
			role="dialog"
			aria-modal="true"
			closable
			@close="showModal = false"
		>
			<template #header-extra>Oops!</template>
			Content
			<template #footer>Footer</template>
		</n-card>
	</n-modal>
</template>

<script setup lang="ts">
// 1) This is the old way:

// const props = defineProps<{
// 	show: boolean
// }>()
//
// const emit = defineEmits<{
// 	(e: 'update:show', value: boolean): void
// }>()
//
// const showModal = ref(props.show)
//
// const closeModal = () => {
// 	showModal.value = false
// 	emit('update:show', false)
// }

// 2) two-way sync with v-model:

const showModal = defineModel<boolean>({
	default: false
})

/**
 * Usage in the parent:
 *  <NButton @click="showModal = true">show</NButton>
 *  <ExampleModal v-model="showModal"></ExampleModal>
 *  const showModal = ref(false)
 */
</script>
