import { mount } from "@vue/test-utils"
import { describe, expect, it, vi } from "vitest"
import Sankey2 from "@/au24/view/widgets/sankey/Sankey2.vue"
import { AuSankeyLink, AuSankeyNode } from "@/au24/view/widgets/sankey/sankey-helper"

// Mock D3 and D3-Sankey
vi.mock("d3", () => ({
	select: vi.fn(() => ({
		selectAll: vi.fn(() => ({
			remove: vi.fn()
		})),
		append: vi.fn(() => ({
			attr: vi.fn().mockReturnThis(),
			style: vi.fn().mockReturnThis(),
			text: vi.fn().mockReturnThis()
		}))
	}))
}))

vi.mock("d3-sankey", () => ({
	sankey: vi.fn(() => ({
		nodeWidth: vi.fn().mockReturnThis(),
		nodePadding: vi.fn().mockReturnThis(),
		extent: vi.fn().mockReturnThis()
	})),
	sankeyLinkHorizontal: vi.fn()
}))

describe("Sankey2", () => {
	const sampleNodes: AuSankeyNode[] = [{ name: "Sell" }, { name: "TraderA" }, { name: "TraderB" }, { name: "Buy" }]

	const sampleLinks: AuSankeyLink[] = [
		{ source: { name: "Sell" }, target: { name: "TraderA" }, quantity: 80 },
		{ source: { name: "Sell" }, target: { name: "TraderB" }, quantity: 90 },
		{ source: { name: "TraderA" }, target: { name: "Buy" }, quantity: 100 },
		{ source: { name: "TraderB" }, target: { name: "Buy" }, quantity: 120 },
		{ source: { name: "TraderA" }, target: { name: "TraderB" }, quantity: 30 }
	]

	it("should render the component", () => {
		const wrapper = mount(Sankey2, {
			props: {
				width: 800,
				height: 600,
				nodes: sampleNodes,
				links: sampleLinks
			}
		})
		expect(wrapper.find('[data-testid="sankey-container"]').exists()).toBe(true)
		expect(wrapper.find('[data-testid="sankey-svg"]').exists()).toBe(true)
	})

	it("should set correct SVG dimensions", () => {
		const wrapper = mount(Sankey2, {
			props: {
				width: 800,
				height: 600,
				nodes: sampleNodes,
				links: sampleLinks
			}
		})
		const svg = wrapper.find('[data-testid="sankey-svg"]')
		expect(svg.attributes("width")).toBe("800")
		expect(svg.attributes("height")).toBe("600")
	})

	it("should display no data message when nodes are empty", () => {
		const wrapper = mount(Sankey2, {
			props: {
				width: 800,
				height: 600,
				nodes: [],
				links: sampleLinks
			}
		})
		expect(wrapper.find('[data-testid="no-data-message"]').exists()).toBe(true)
	})

	it("should display no data message when links are empty", () => {
		const wrapper = mount(Sankey2, {
			props: {
				width: 800,
				height: 600,
				nodes: sampleNodes,
				links: []
			}
		})
		expect(wrapper.find('[data-testid="no-data-message"]').exists()).toBe(true)
	})

	it("should not display no data message when both nodes and links are provided", () => {
		const wrapper = mount(Sankey2, {
			props: {
				width: 800,
				height: 600,
				nodes: sampleNodes,
				links: sampleLinks
			}
		})
		expect(wrapper.find('[data-testid="no-data-message"]').exists()).toBe(false)
		expect(wrapper.find('[data-testid="sankey-content"]').exists()).toBe(true)
	})
})
