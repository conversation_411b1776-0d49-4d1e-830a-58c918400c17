<template>
	<div class="sankey-demo">
		<h1>Sankey Diagram Demo</h1>
		<div>
			<p>Current size: {{ width }}x{{ height }}</p>
		</div>
		<div v-if="error">Error: {{ error }}</div>
		<Sankey2 :width="width" :height="height" :links="sankeyLinks" :nodes="sankeyNodes" />
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import Sankey2 from "@/au24/view/widgets/sankey/Sankey2.vue"
import { AuSankeyLink, AuSankeyNode, transformToSankeyData } from "@/au24/view/widgets/sankey/sankey-helper.ts"
import { sampleSankeyEdges, sampleSankeyTraders } from "@/au24/view/widgets/sankey/sankey-demo-data.ts"

const width = ref(600)
const height = ref(400)
const error = ref<string | null>(null)

// Sample Traders

const sankeyNodes = ref<AuSankeyNode[]>([])
const sankeyLinks = ref<AuSankeyLink[]>([])

onMounted(() => {
	try {
		const to_string = (arr: any[]) => {
			arr.forEach(a => console.log(JSON.stringify(a)))
			console.log(arr)
		}

		console.log("Sample Traders:", console.table(sampleSankeyTraders))
		console.log("Sample Edges:", console.table(sampleSankeyEdges))

		const { nodes, links } = transformToSankeyData(sampleSankeyTraders, sampleSankeyEdges)
		console.log("Transformed Nodes:", console.table(nodes))
		console.log("Transformed Links:", console.table(links))

		sankeyNodes.value = nodes
		sankeyLinks.value = links

		console.log("Sankey Nodes set:", console.table(sankeyNodes.value))
		console.log("Sankey Links set:", console.table(sankeyLinks.value))
	} catch (e) {
		error.value = e instanceof Error ? e.message : String(e)
		console.error("Error in data transformation:", e)
	}
})
</script>

<style scoped>
.sankey-demo {
	font-family: Arial, sans-serif;
	max-width: 1000px;
	margin: 0 auto;
	padding: 20px;
}

h1 {
	text-align: center;
	color: #333;
}
</style>
