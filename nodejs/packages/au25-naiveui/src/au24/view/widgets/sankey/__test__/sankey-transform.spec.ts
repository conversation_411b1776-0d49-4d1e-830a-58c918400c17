import { describe, expect, it } from "vitest"
import { transformToSankeyData } from "@/au24/view/widgets/sankey/sankey-helper"
import { DeMatrixEdgeElement, DeRoundTraderElement } from "@/au24/types/generated"

describe("transformToSankeyData", () => {
	const sampleTraders: DeRoundTraderElement[] = [
		{
			company_shortname: "TraderA",
			cid: "TA001",
			constraints: {
				max_buy_quantity: 100,
				max_sell_quantity: 80,
				min_buy_quantity: 0,
				min_sell_quantity: 0
			}
		},
		{
			company_shortname: "TraderB",
			cid: "TB002",
			constraints: {
				max_buy_quantity: 120,
				max_sell_quantity: 90,
				min_buy_quantity: 0,
				min_sell_quantity: 0
			}
		},
		{
			company_shortname: "TraderC",
			cid: "TC003",
			constraints: {
				max_buy_quantity: 80,
				max_sell_quantity: 110,
				min_buy_quantity: 0,
				min_sell_quantity: 0
			}
		}
	] as DeRoundTraderElement[]

	it("should transform data without cycles correctly", () => {
		const edges: DeMatrixEdgeElement[] = [
			{ seller_cid: "TA001", buyer_cid: "TB002", match: 30 },
			{ seller_cid: "TA001", buyer_cid: "TC003", match: 40 }
		] as DeMatrixEdgeElement[]

		const { nodes, links } = transformToSankeyData(sampleTraders, edges)
		links.forEach(link => console.log(JSON.stringify(link)))
		expect(nodes.length).toBe(5) // Sell, TraderA, TraderB, TraderC, Buy
		expect(links.length).toBe(5)
		expect(links[0]).toStrictEqual({ source: { name: "Sell" }, target: { name: "TraderA" }, quantity: 80 })
		expect(links[1]).toStrictEqual({ source: { name: "TraderB" }, target: { name: "Buy" }, quantity: 120 })
		expect(links[2]).toStrictEqual({ source: { name: "TraderC" }, target: { name: "Buy" }, quantity: 80 })
		expect(links[3]).toStrictEqual({ source: { name: "TraderA" }, target: { name: "TraderB" }, quantity: 30 })
		expect(links[4]).toStrictEqual({ source: { name: "TraderA" }, target: { name: "TraderC" }, quantity: 40 })
	})

	it("should detect cycles and throw an error", () => {
		const edgesWithCycle: DeMatrixEdgeElement[] = [
			{ seller_cid: "TA001", buyer_cid: "TB002", match: 30 },
			{ seller_cid: "TB002", buyer_cid: "TC003", match: 40 },
			{ seller_cid: "TC003", buyer_cid: "TA001", match: 20 } // This creates a cycle
		] as DeMatrixEdgeElement[]

		expect(() => transformToSankeyData(sampleTraders, edgesWithCycle)).toThrowError(
			/Cycles detected in Sankey diagram data/
		)
	})

	it("should handle empty input correctly", () => {
		const result = transformToSankeyData([], [])
		expect(result.nodes.length).toBe(2) // Should still have Sell and Buy nodes
		expect(result.links.length).toBe(0)
	})

	it("should handle traders with no transactions", () => {
		const edges: DeMatrixEdgeElement[] = [
			{ seller_cid: "TA001", buyer_cid: "TB002", match: 30 }
		] as DeMatrixEdgeElement[]

		const { nodes, links } = transformToSankeyData(sampleTraders, edges)
		nodes.forEach(n => console.log(JSON.stringify(n)))
		links.forEach(link => console.log(JSON.stringify(link)))
		expect(nodes.length).toBe(4) // Should include all traders plus Sell and Buy
		expect(links.length).toBe(3)

		expect(links[0]).toStrictEqual({ source: { name: "Sell" }, target: { name: "TraderA" }, quantity: 80 })
		expect(links[1]).toStrictEqual({ source: { name: "TraderB" }, target: { name: "Buy" }, quantity: 120 })
		expect(links[2]).toStrictEqual({ source: { name: "TraderA" }, target: { name: "TraderB" }, quantity: 30 })
	})

	it("should handle zero-match transactions", () => {
		const edgesWithZero: DeMatrixEdgeElement[] = [
			{ seller_cid: "TA001", buyer_cid: "TB002", match: 30 },
			{ seller_cid: "TA001", buyer_cid: "TC003", match: 0 }
		] as DeMatrixEdgeElement[]

		// TODO: should we remove TC003 as a buyer node if they have no buy volume?

		const { nodes, links } = transformToSankeyData(sampleTraders, edgesWithZero)
		links.forEach(link => console.log(JSON.stringify(link)))
		expect(nodes.length).toBe(4) // Should include all traders > 0 plus Sell and Buy
		expect(links.length).toBe(3)
		expect(links.some(link => link.quantity === 0)).toBe(false)
	})
})
