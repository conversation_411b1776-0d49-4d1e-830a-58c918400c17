<template>
	<div data-testid="sankey-container">
		<svg :width="width" :height="height" data-testid="sankey-svg">
			<g v-if="error" data-testid="error-message">
				<text x="10" y="20" fill="red">Error: {{ error }}</text>
			</g>
			<g v-else-if="processedNodes.length === 0 || processedLinks.length === 0" data-testid="no-data-message">
				<text x="10" y="20">No data to display</text>
			</g>
			<g v-else data-testid="sankey-content">
				<!-- D3 will render content here -->
			</g>
		</svg>
	</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, watch } from "vue"
import * as d3 from "d3"
import * as d3Sankey from "d3-sankey"
import { SankeyLink } from "d3-sankey"
import { AuSankeyLink, AuSankeyNode } from "@/au24/view/widgets/sankey/sankey-helper.ts"

const props = defineProps<{
	width: number
	height: number
	nodes: AuSankeyNode[]
	links: AuSankeyLink[]
}>()

const error = ref<string | null>(null)

interface ProcessedNode {
	name: string
	index?: number
}

interface ProcessedLink {
	source: number
	target: number
	value: number
}

const processedNodes = computed<ProcessedNode[]>(() => {
	return props.nodes.map((node, index) => ({
		name: node.name,
		index: index
	}))
})

const processedLinks = computed<ProcessedLink[]>(() => {
	const nodeMap = new Map(processedNodes.value.map(node => [node.name, node.index]))
	return props.links.map(link => ({
		source: nodeMap.get(link.source.name) ?? 0,
		target: nodeMap.get(link.target.name) ?? 0,
		value: link.quantity
	}))
})

const drawSankey = () => {
	try {
		const svg = d3.select('[data-testid="sankey-svg"]')
		svg.selectAll("*").remove() // Clear previous render

		const header_height = 20
		const footer_height = 30
		const margin = {
			top: header_height + 10,
			right: 10,
			bottom: footer_height + 10,
			left: 10
		}
		const width = props.width - margin.left - margin.right
		const height = props.height - margin.top - margin.bottom

		if (processedNodes.value.length === 0 || processedLinks.value.length === 0) {
			svg.append("text").attr("x", 10).attr("y", 20).text("No data to display")
			return
		}

		console.log("Processed Nodes:", console.table(processedNodes.value))
		console.log("Processed Links:", console.table(processedLinks.value))

		const sankey = d3Sankey
			.sankey<ProcessedNode, ProcessedLink>()
			.nodeWidth(15)
			.nodePadding(10)
			.extent([
				[10, 10],
				[props.width - 10, props.height - 10]
			])

		const { nodes, links } = sankey({
			nodes: processedNodes.value,
			links: processedLinks.value
		})

		// Create a group for the Sankey diagram elements
		const sankeyGroup = svg.append("g")

		// Draw links (now before nodes and labels)
		const link = sankeyGroup
			.append("g")
			.attr("fill", "none")
			.attr("stroke-opacity", 0.5)
			.selectAll("path")
			.data(links)
			.join("path")
			.attr("d", d3Sankey.sankeyLinkHorizontal())
			.attr("stroke", "#555")
			.attr("stroke-width", d => Math.max(1, d.width ?? 0))
			.attr("opacity", d => (d.value === 0 ? 0 : 1))

		// Draw nodes
		sankeyGroup
			.append("g")
			.selectAll("rect")
			.data(nodes)
			.join("rect")
			.attr("x", d => d.x0 ?? 0)
			.attr("y", d => d.y0 ?? 0)
			.attr("height", d => (d.y1 ?? 0) - (d.y0 ?? 0))
			.attr("width", d => (d.x1 ?? 0) - (d.x0 ?? 0))
			.attr("fill", "#69b3a2")

		// Add labels for depth 1 and 2 nodes
		sankeyGroup
			.append("g")
			.selectAll("text")
			.data(nodes.filter(d => d.depth === 1 || d.depth === 2))
			.join("text")
			.attr("x", d => {
				if (d.depth === 1) return (d.x0 ?? 0) - 6
				if (d.depth === 2) return (d.x1 ?? 0) + 6
				return 0
			})
			.attr("y", d => ((d.y1 ?? 0) + (d.y0 ?? 0)) / 2)
			.attr("dy", "0.35em")
			.attr("fill", "white")
			.attr("text-anchor", d => (d.depth === 1 ? "end" : "start"))
			.attr("pointer-events", "none") // Make labels non-interactive
			.text(d => d.name)

		// Add "Sell" and "Buy" labels in the header
		svg.append("text")
			.attr("x", ((nodes[0]?.x1 ?? 0) + (nodes[1]?.x0 ?? 0)) / 2)
			.attr("y", header_height / 2)
			.attr("text-anchor", "middle")
			.attr("dominant-baseline", "middle")
			.attr("fill", "white")
			.text("Sell")

		svg.append("text")
			.attr("x", ((nodes[nodes.length - 2]?.x1 ?? 0) + (nodes[nodes.length - 1]?.x0 ?? 0)) / 2)
			.attr("y", header_height / 2)
			.attr("text-anchor", "middle")
			.attr("dominant-baseline", "middle")
			.attr("fill", "white")
			.text("Buy")

		// Add footer text (initially empty)
		const footerText = svg
			.append("text")
			.attr("x", width / 2)
			.attr("y", height + margin.top + footer_height / 2)
			.attr("text-anchor", "middle")
			.attr("fill", "white")
			.attr("dominant-baseline", "middle")
			.text("")

		// Highlighting function
		const highlight = (event: MouseEvent, d: SankeyLink<any, any>) => {
			// Reset all links
			link.attr("stroke", "#555").attr("stroke-opacity", 0.5)

			// Highlight the hovered link
			d3.select(event.target as SVGPathElement)
				.attr("stroke", "red")
				.attr("stroke-opacity", 1)

			// Highlight connected links
			if (d.source.depth === 0) {
				// Highlight depth 1-2 links with the same seller
				link.filter((l: SankeyLink<any, any>) => l.source.depth === 1 && l.source === d.target)
					.attr("stroke", "red")
					.attr("stroke-opacity", 1)
			} else if (d.target.depth === 3) {
				// Highlight depth 1-2 links with the same buyer
				link.filter((l: SankeyLink<any, any>) => l.target.depth === 2 && l.target === d.source)
					.attr("stroke", "red")
					.attr("stroke-opacity", 1)
			}

			// Update footer text for depth 1-2 links
			if (d.source.depth === 1 && d.target.depth === 2) {
				footerText.text(`Matched seller: ${d.source.name}, buyer: ${d.target.name}, quantity: ${d.value}`)
			}
		}

		// Reset function
		const reset = () => {
			link.attr("stroke", "#555").attr("stroke-opacity", 0.5)
			footerText.text("")
		}

		// Add hover events
		link.on("mouseover", highlight).on("mouseout", reset)
	} catch (e) {
		console.error("Error in Sankey rendering:", e)
		error.value = e instanceof Error ? e.message : String(e)
	}
}

onMounted(drawSankey)
watch(() => [props.width, props.height, processedNodes.value, processedLinks.value], drawSankey)
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
