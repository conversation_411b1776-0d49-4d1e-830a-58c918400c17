import { DeMatrixEdgeElement, DeRoundTraderElement } from "@/au24/types/generated.ts"

export interface AuSankeyNode {
	name: string
	depth?: number
	x0?: number
	x1?: number
	y0?: number
	y1?: number
}

export interface AuSankeyLink {
	source: AuSankeyNode
	target: AuSankeyNode
	quantity: number
	width?: number
}

export function transformToSankeyData(
	traders: DeRoundTraderElement[],
	edges: DeMatrixEdgeElement[]
): {
	nodes: AuSankeyNode[]
	links: AuSankeyLink[]
} {
	const nodes: AuSankeyNode[] = [{ name: "Sell" }]
	const links: AuSankeyLink[] = []
	const nodeMap: Map<string, AuSankeyNode> = new Map()

	// Check for cycles (and exclude zero matches):
	const buyerCids = new Set(edges.filter(e => e.match > 0).map(e => e.buyer_cid))

	const sellerCids = new Set(edges.filter(e => e.match > 0).map(e => e.seller_cid))

	const cyclesCids = [...buyerCids].filter(cid => sellerCids.has(cid))

	if (cyclesCids.length > 0) {
		const cycleTraders = traders.filter(trader => cyclesCids.includes(trader.cid))
		const cycleNames = cycleTraders.map(trader => trader.company_shortname).join(", ")
		const errorMsg = `Cycles detected in Sankey diagram data. The following traders appear as both buyers and sellers: ${cycleNames}`
		console.error(errorMsg)
		throw new Error(errorMsg)
	}

	const sellers = traders.filter(n => sellerCids.has(n.cid))
	const buyers = traders.filter(n => buyerCids.has(n.cid))

	// Add 'Sell' node
	const sellNode = nodes[0]!
	nodeMap.set("Sell", sellNode)

	// Process sellers:
	sellers.forEach(trader => {
		const traderNode: AuSankeyNode = { name: trader.company_shortname }
		nodes.push(traderNode)
		nodeMap.set(trader.cid, traderNode)

		links.push({
			source: sellNode,
			target: traderNode,
			quantity: trader.constraints.max_sell_quantity
		})
	})

	// process buyers:
	const buyNode: AuSankeyNode = { name: "Buy" }
	nodes.push(buyNode)
	nodeMap.set("Buy", buyNode)

	// Add links from traders to 'Buy'
	buyers.forEach(trader => {
		const traderNode: AuSankeyNode = { name: trader.company_shortname }
		nodes.push(traderNode)
		nodeMap.set(trader.cid, traderNode)

		links.push({
			source: traderNode,
			target: buyNode,
			quantity: trader.constraints.max_buy_quantity
		})
	})

	// Process edges
	edges.forEach(edge => {
		const sourceNode = nodeMap.get(edge.seller_cid)
		const targetNode = nodeMap.get(edge.buyer_cid)

		console.log({ match: edge.match })

		// remove the zero matches
		if (sourceNode && targetNode && edge.match > 0) {
			links.push({
				source: sourceNode,
				target: targetNode,
				quantity: edge.match
			})
		}
	})

	return { nodes, links }
}
