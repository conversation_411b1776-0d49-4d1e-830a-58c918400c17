<!-- {'model': 'claude-3-sonnet-20240229', 'input_tokens': 1610, 'output_tokens': 802, 'input_cost': '$0.004830', 'output_cost': '$0.012030', 'total_cost': '$0.016860'} -->
Here's the Vue 3 version of the provided code, using Naive UI:

<template>
	<div>
		<div>
			<button @click="buy">Buy</button>
			<button @click="sell">Sell</button>
			<button @click="buy_buy">Buy-Buy</button>
			<button @click="sell_sell">Sell-Sell</button>
			<button @click="buy_sell">Buy-Sell</button>
			<DeOrderConstraintsWithRange
				:order_type="order_type"
				:order_quantity="order_quantity"
				:constraints="constraints"
				quantity_label="quantity_label"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { type DeBidConstraints, OrderType } from "@/au24/types/generated.js"
import { createDemo__DeBidConstraints } from "@/au24/helpers/demo-helpers/DeBidConstraints.helper"
import DeOrderConstraintsWithRange from "./Au24OrderConstraintsWithRange.vue"


const order_quantity = ref(10)
const order_type = ref<OrderType>(OrderType.BUY)
const quantity_label = "MMlb"
const constraints = ref<DeBidConstraints>(createDemo__DeBidConstraints())

onMounted(() => {
	buy()
})

function buy() {
	order_quantity.value = 10
	constraints.value = {
		max_buy_quantity: 40,
		min_buy_quantity: 0,
		min_sell_quantity: 0,
		max_sell_quantity: 30
	}
}

function sell() {}

function buy_buy() {}

function sell_sell() {}

function buy_sell() {}
</script>
