<template>
    <div class="test-container">
        <!-- Controls -->
        <div class="controls">
            <button
                    @click="handlePriceChange('INCREASE')"
            >
                Price Increase
            </button>
            <button
                    @click="handlePriceChange('DECREASE')"
            >
                Price Decrease
            </button>
            <button @click="resetConstraints">
                Reset
            </button>
        </div>

        <!-- Test Scenarios -->
        <div class="scenarios">
            <!-- Basic Two-sided -->
            <div class="scenario">
                <h3>Basic Two-sided</h3>
                <div class="bars">
                    <Au24OrderConstraintsBar
                            :constraints="basicTwoSidedLeft"
                            :height="30"
                            :order_quantity="25"
                            :order_type="OrderType.BUY"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                    <Au24OrderConstraintsBar
                            :constraints="basicTwoSidedRight"
                            :height="30"
                            :order_quantity="25"
                            :order_type="OrderType.SELL"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                </div>
            </div>

            <!-- One-sided -->
            <div class="scenario">
                <h3>One-sided</h3>
                <div class="bars">
                    <Au24OrderConstraintsBar
                            :constraints="oneSidedLeft"
                            :height="30"
                            :order_quantity="20"
                            :order_type="OrderType.BUY"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                    <Au24OrderConstraintsBar
                            :constraints="oneSidedRight"
                            :height="30"
                            :order_quantity="20"
                            :order_type="OrderType.SELL"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                </div>
            </div>

            <!-- Switch -->
            <div class="scenario">
                <h3>Switch</h3>
                <div class="bars">
                    <Au24OrderConstraintsBar
                            :constraints="switchLeft"
                            :height="30"
                            :order_quantity="25"
                            :order_type="OrderType.SELL"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                    <Au24OrderConstraintsBar
                            :constraints="switchRight"
                            :height="30"
                            :order_quantity="25"
                            :order_type="OrderType.BUY"
                            :price_change="lastPriceChange"
                            :show_labels="true"
                            :tick_font_size="12"
                            :tick_step="10"
                            :width="400"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import {DeBidConstraints, OrderType} from "@/au24/types/generated"
import {PriceChange, updateConstraints} from "@/au24/view/widgets/constraints/ConstraintsLogic";
import Au24OrderConstraintsBar from "@/au24/view/widgets/constraints/Au24OrderConstraintsBar.vue";

// Initial constraints
const BASIC_TWO_SIDED: DeBidConstraints = {
    max_buy_quantity: 50,
    min_buy_quantity: 0,
    max_sell_quantity: 50,
    min_sell_quantity: 0
}

const ONE_SIDED_BUY: DeBidConstraints = {
    max_buy_quantity: 40,
    min_buy_quantity: 10,
    max_sell_quantity: 0,
    min_sell_quantity: 0
}

const ONE_SIDED_SELL: DeBidConstraints = {
    max_buy_quantity: 0,
    min_buy_quantity: 0,
    max_sell_quantity: 40,
    min_sell_quantity: 10
}

const SWITCH_LEFT: DeBidConstraints = {
    max_buy_quantity: 40,
    min_buy_quantity: 0,
    max_sell_quantity: 50,
    min_sell_quantity: 0
}

const SWITCH_RIGHT: DeBidConstraints = {
    max_buy_quantity: 50,
    min_buy_quantity: 0,
    max_sell_quantity: 40,
    min_sell_quantity: 0
}

// State
const lastPriceChange = ref<'up' | 'down' | null>(null)
const basicTwoSidedLeft = ref<DeBidConstraints>({ ...BASIC_TWO_SIDED })
const basicTwoSidedRight = ref<DeBidConstraints>({ ...BASIC_TWO_SIDED })
const oneSidedLeft = ref<DeBidConstraints>({ ...ONE_SIDED_BUY })
const oneSidedRight = ref<DeBidConstraints>({ ...ONE_SIDED_SELL })
const switchLeft = ref<DeBidConstraints>({ ...SWITCH_LEFT })
const switchRight = ref<DeBidConstraints>({ ...SWITCH_RIGHT })

// Handlers
function handlePriceChange(change: PriceChange) {
    lastPriceChange.value = change === 'INCREASE' ? 'up' : 'down'

    // Update each scenario
    basicTwoSidedLeft.value = updateConstraints({
        currentConstraints: basicTwoSidedLeft.value,
        orderType: OrderType.BUY,
        orderQuantity: 25,
        priceChange: change
    })

    basicTwoSidedRight.value = updateConstraints({
        currentConstraints: basicTwoSidedRight.value,
        orderType: OrderType.SELL,
        orderQuantity: 25,
        priceChange: change
    })

    oneSidedLeft.value = updateConstraints({
        currentConstraints: oneSidedLeft.value,
        orderType: OrderType.BUY,
        orderQuantity: 20,
        priceChange: change
    })

    oneSidedRight.value = updateConstraints({
        currentConstraints: oneSidedRight.value,
        orderType: OrderType.SELL,
        orderQuantity: 20,
        priceChange: change
    })

    switchLeft.value = updateConstraints({
        currentConstraints: switchLeft.value,
        orderType: OrderType.SELL,
        orderQuantity: 25,
        priceChange: change
    })

    switchRight.value = updateConstraints({
        currentConstraints: switchRight.value,
        orderType: OrderType.BUY,
        orderQuantity: 25,
        priceChange: change
    })

    // Reset lastPriceChange after animation duration
    setTimeout(() => {
        lastPriceChange.value = null
    }, 1000)
}

function resetConstraints() {
    basicTwoSidedLeft.value = { ...BASIC_TWO_SIDED }
    basicTwoSidedRight.value = { ...BASIC_TWO_SIDED }
    oneSidedLeft.value = { ...ONE_SIDED_BUY }
    oneSidedRight.value = { ...ONE_SIDED_SELL }
    switchLeft.value = { ...SWITCH_LEFT }
    switchRight.value = { ...SWITCH_RIGHT }
    lastPriceChange.value = null
}
</script>

<style scoped>
.test-container {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.controls {
    display: flex;
    gap: 10px;
}

.scenarios {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.scenario {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.bars {
    display: flex;
    gap: 20px;
}

button {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid #ccc;
    cursor: pointer;
}

h3 {
    margin: 0;
    font-size: 14px;
    color: #666;
}
</style>
