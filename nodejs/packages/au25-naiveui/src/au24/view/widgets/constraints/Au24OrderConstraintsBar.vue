<template>
    <div
            :style="{
      width: `${width}px`,
      height: `${height}px`
    }"
            class="AuOrderConstraintsBar"
    >
        <!-- Labels section -->
        <div
                v-if="show_labels"
                :style="{
        position: 'absolute',
        fontSize: `${tick_font_size}px`,
        top: `${height - 2 + tick_font_size / 2}px`
      }"
        >
            <div
                    :style="{
          color: 'white',
          position: 'absolute',
          left: `${mid_x}px`,
          transform: 'translateX(-50%)',
          textAlign: 'center'
        }"
                    class="tick-label"
            >
                0
            </div>

            <div
                    v-for="(tick, i) in buy_ticks"
                    :key="`buy-${i}`"
                    :style="{
          left: `${mid_x - to_pixels(tick.vol)}px`,
          color: colors.au_buy(),
          width: `${tick_width}px`,
          textAlign: 'center',
          transform: 'translateX(-50%)'
        }"
                    class="tick-label"
            >
                {{ tick.vol }}
            </div>

            <div
                    v-for="(tick, i) in sell_ticks"
                    :key="`sell-${i}`"
                    :style="{
          left: `${mid_x + to_pixels(tick.vol)}px`,
          color: colors.au_sell(),
          width: `${tick_width}px`,
          textAlign: 'center',
          transform: 'translateX(-50%)'
        }"
                    class="tick-label"
            >
                {{ tick.vol }}
            </div>
        </div>

        <svg
                :height="height"
                :viewBox="`0 0 ${bar_width} ${height}`"
                :width="bar_width"
                xmlns="http://www.w3.org/2000/svg"
        >
            <!-- Buy side base -->
            <rect
                    :fill="buy_dimmed"
                    :height="height - 6"
                    :width="bar_width / 2"
                    :x="0"
                    :y="3"
                    class="base-rect"
            />

            <!-- Buy constraints -->
            <rect
                    :fill="colors.au_buy_dimmed()"
                    :height="height - 6"
                    :width="buy_constraint_width"
                    :x="buy_constraint_x"
                    :y="3"
                    :class="[
          'constraint-rect',
          'buy-constraint',
          {
            'animating-increase': isAnimating && props.price_change === 'up',
            'animating-decrease': isAnimating && props.price_change === 'down'
          }
        ]"
            />

            <!-- Sell side base -->
            <rect
                    :fill="sell_dimmed"
                    :height="height - 6"
                    :width="bar_width / 2"
                    :x="bar_width / 2"
                    :y="3"
                    class="base-rect"
            />

            <!-- Sell constraints -->
            <rect
                    :fill="colors.au_sell_dim()"
                    :height="height - 6"
                    :width="sell_constraint_width"
                    :x="sell_constraint_x"
                    :y="3"
                    :class="[
          'constraint-rect',
          'sell-constraint',
          {
            'animating': isAnimating && props.price_change === 'down',
            'animating-increase': isAnimating && props.price_change === 'up'
          }
        ]"
            />

            <!-- Tick marks -->
            <rect
                    v-for="(x, i) in tick_x_arr"
                    :key="i"
                    :height="height"
                    :width="0.25"
                    :x="x"
                    fill="white"
            />

            <!-- Order indicator -->
            <rect
                    v-if="order_type"
                    :fill="order_color"
                    :height="height"
                    :width="order_width"
                    :x="order_x"
                    class="order-indicator"
            />
        </svg>
    </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from "vue"
import chroma from "chroma-js"
import { range } from "lodash"
import { type DeBidConstraints, OrderType } from "@/au24/types/generated.js"
import { AuColors, auColors } from "@/au24/au-styles/AuColors"

interface Props {
    constraints: DeBidConstraints | null
    height: number
    max_quantity?: number
    order_quantity: number | null
    order_type: OrderType | null
    show_labels?: boolean
    tick_font_size: number
    tick_step?: number
    width: number
    price_change?: 'up' | 'down' | null
}

const props = withDefaults(defineProps<Props>(), {
    constraints: null,
    height: 300,
    max_quantity: 50,
    order_quantity: null,
    order_type: null,
    show_labels: true,
    tick_font_size: 12,
    tick_step: 10,
    width: 120,
    price_change: null
})

// State
const isAnimating = ref(false)
const previousConstraints = ref<DeBidConstraints | null>(null)

// Colors and dimensions
const colors: AuColors = auColors
const bar_width = computed(() => props.width)
const buy_dimmed = computed(() => chroma(colors.au_buy()).alpha(0.1).toString())
const sell_dimmed = computed(() => chroma(colors.au_sell()).alpha(0.1).toString())
const order_color = computed(() => colors.order_bright(props.order_type))

// Constraint calculations
const max_buy_quantity = computed(() => props.constraints?.max_buy_quantity ?? 0)
const min_buy_quantity = computed(() => props.constraints?.min_buy_quantity ?? 0)
const max_sell_quantity = computed(() => props.constraints?.max_sell_quantity ?? 0)
const min_sell_quantity = computed(() => props.constraints?.min_sell_quantity ?? 0)

// Layout calculations
const mid_x = computed(() => bar_width.value / 2)
const pixels_per_vol = computed(() => (0.9 * props.width) / (props.max_quantity * 2))
const to_pixels = (vol: number) => vol * pixels_per_vol.value
const tick_width = computed(() => props.tick_step * pixels_per_vol.value)

// Buy constraint positioning and sizing
const buy_constraint_width = computed(() => {
    return to_pixels(max_buy_quantity.value - min_buy_quantity.value)
})

const buy_constraint_x = computed(() => {
    return mid_x.value - to_pixels(max_buy_quantity.value)
})

// Sell constraint positioning and sizing
const sell_constraint_width = computed(() =>
        Math.max(0, to_pixels(max_sell_quantity.value - min_sell_quantity.value))
)

const sell_constraint_x = computed(() => {
    return mid_x.value + to_pixels(min_sell_quantity.value)
})

// Ticks calculations
const tick_x_arr = computed(() => {
    const ticks = [mid_x.value]
    const count = Math.floor(props.max_quantity / props.tick_step)

    for (let i = 1; i <= count; i++) {
        const offset = i * props.tick_step * pixels_per_vol.value
        ticks.push(mid_x.value - offset)
        ticks.push(mid_x.value + offset)
    }

    return ticks
})

const buy_ticks = computed(() => {
    const ticks = range(props.tick_step, props.max_quantity + 1, props.tick_step)
    return ticks.reverse().map(vol => ({
        vol,
        x: mid_x.value - to_pixels(vol)
    }))
})

const sell_ticks = computed(() => {
    const ticks = range(props.tick_step, props.max_quantity + 1, props.tick_step)
    return ticks.map(vol => ({
        vol,
        x: mid_x.value + to_pixels(vol)
    }))
})

// Order indicator
const order_width = computed(() => 3)
const order_x = computed(() => {
    switch (props.order_type) {
        case OrderType.BUY:
            return `${mid_x.value - order_width.value / 2 - to_pixels(Math.min(props.max_quantity, props.order_quantity || 0))}`
        case OrderType.SELL:
            return `${mid_x.value - order_width.value / 2 + to_pixels(Math.min(props.max_quantity, props.order_quantity || 0))}`
        default:
            return `${mid_x.value - order_width.value / 2}`
    }
})

// Animation triggers
watch(() => props.constraints, (newVal, oldVal) => {
    if (oldVal && newVal && props.price_change) {
        previousConstraints.value = oldVal
        isAnimating.value = true

        setTimeout(() => {
            isAnimating.value = false
            previousConstraints.value = null
        }, 1000)
    }
}, { deep: true })

</script>

<style lang="less" scoped>
.AuOrderConstraintsBar {
    position: relative;
    margin: 0;
    padding: 0;
    text-align: center;

    .tick-label {
        position: absolute;
    }

    // Order indicator transitions
    .order-indicator {
        transition: x 0.3s ease-out;
    }

    // Constraint animations
    .constraint-rect {
        &.buy-constraint {
            &.animating-increase {
                transition: x 0.5s ease-in-out, width 0.5s ease-in-out;
            }
            &.animating-decrease {
                transition: width 0.5s ease-in-out;
                transition-delay: 0.5s;
            }
        }

        &.sell-constraint {
            &.animating {
                transition: width 0.5s ease-in-out;
            }
            &.animating-increase {
                transition: x 0.5s ease-in-out, width 0.5s ease-in-out;
                transition-delay: 0.5s;
            }
        }
    }
}
</style>
