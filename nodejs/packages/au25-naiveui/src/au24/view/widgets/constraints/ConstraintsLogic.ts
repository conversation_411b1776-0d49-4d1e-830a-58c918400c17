import {DeBidConstraints, OrderType} from "@/au24/types/generated";

type PriceChange = 'INCREASE' | 'DECREASE';

interface ConstraintUpdateParams {
    currentConstraints: DeBidConstraints;
    orderType: OrderType | null;
    orderQuantity: number;
    priceChange: PriceChange;
}

const INITIAL_CONSTRAINTS: DeBidConstraints = {
    max_buy_quantity: 50,
    min_buy_quantity: 0,
    max_sell_quantity: 50,
    min_sell_quantity: 0
};

function updateConstraints({
                               currentConstraints,
                               orderType,
                               orderQuantity,
                               priceChange
                           }: ConstraintUpdateParams): DeBidConstraints {
    // If no order, return current constraints
    if (!orderType) {
        return currentConstraints;
    }

    // Make a copy to modify
    const newConstraints = { ...currentConstraints };

    if (orderType === 'BUY') {
        if (priceChange === 'INCREASE') {
            // Shrink max buy to order quantity
            newConstraints.max_buy_quantity = orderQuantity;
        } else { // DECREASE
            // Zero out sell side
            newConstraints.min_sell_quantity = 0;
            newConstraints.max_sell_quantity = 0;
            // Set min buy to order quantity
            newConstraints.min_buy_quantity = orderQuantity;
        }
    } else { // SELL
        if (priceChange === 'INCREASE') {
            // Zero out buy side
            newConstraints.max_buy_quantity = 0;
            newConstraints.min_buy_quantity = 0;
            // Set min sell to order quantity
            newConstraints.min_sell_quantity = orderQuantity;
        } else { // DECREASE
            // Shrink max sell to order quantity
            newConstraints.max_sell_quantity = orderQuantity;
        }
    }

    return newConstraints;
}

export {
    type PriceChange,
    type ConstraintUpdateParams,
    INITIAL_CONSTRAINTS,
    updateConstraints
};
