<template>
	<div class="demo-container">
		<div class="controls">
			<!-- Order Controls -->
			<div class="order-controls">
				<h3>Order:</h3>
				<select v-model="orderType">
					<option :value="null">None</option>
					<option value="BUY">Buy</option>
					<option value="SELL">Sell</option>
				</select>
				<input v-if="orderType" v-model.number="orderQuantity" :max="50" :min="0" type="range" />
				<span v-if="orderType">Quantity: {{ orderQuantity }}</span>
			</div>

			<!-- Price Change Buttons -->
			<div class="price-controls">
				<button :disabled="!orderType" @click="handlePriceChange('INCREASE')">Price Increase</button>
				<button :disabled="!orderType" @click="handlePriceChange('DECREASE')">Price Decrease</button>
				<button @click="undoConstraints">Undo</button>
				<button @click="resetConstraints">Reset</button>
			</div>
		</div>

		<!-- Constraints Bar -->
		<Au24OrderConstraintsBar
			:constraints="constraints"
			:height="30"
			:order_quantity="orderQuantity"
			:order_type="orderType"
			:price_change="lastPriceChange"
			:show_labels="true"
			:tick_font_size="12"
			:tick_step="10"
			:width="200"
		/>

		<!-- Debug Info -->
		<div class="debug-info">
			<pre>{{ constraints }}</pre>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { ref } from "vue"
import { DeBidConstraints, OrderType } from "@/au24/types/generated"
import { INITIAL_CONSTRAINTS, PriceChange, updateConstraints } from "@/au24/view/widgets/constraints/ConstraintsLogic"
import Au24OrderConstraintsBar from "@/au24/view/widgets/constraints/Au24OrderConstraintsBar.vue"

// State
const orderType = ref<OrderType | null>(null)
const orderQuantity = ref(25)
const constraints = ref<DeBidConstraints>({ ...INITIAL_CONSTRAINTS })
const lastPriceChange = ref<"up" | "down" | null>(null)

let prior_constraints: DeBidConstraints = { ...INITIAL_CONSTRAINTS }

// Handlers
function handlePriceChange(change: PriceChange) {
	lastPriceChange.value = change === "INCREASE" ? "up" : "down"

	prior_constraints = constraints.value
	constraints.value = updateConstraints({
		currentConstraints: constraints.value,
		orderType: orderType.value,
		orderQuantity: orderQuantity.value,
		priceChange: change
	})

	// Reset lastPriceChange after animation duration
	setTimeout(() => {
		lastPriceChange.value = null
	}, 1000)
}

function resetConstraints() {
	constraints.value = { ...INITIAL_CONSTRAINTS }
	prior_constraints = { ...INITIAL_CONSTRAINTS }
	lastPriceChange.value = null
}

function undoConstraints() {
	constraints.value = { ...prior_constraints }
	lastPriceChange.value = null
}
</script>

<style scoped>
.demo-container {
	padding: 20px;
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.controls {
	display: flex;
	gap: 20px;
	align-items: center;
}

.order-controls,
.price-controls {
	display: flex;
	gap: 10px;
	align-items: center;
}

.debug-info {
	margin-top: 20px;
	padding: 10px;
	border-radius: 4px;
}

button {
	padding: 8px 16px;
	border-radius: 4px;
	border: 1px solid #ccc;
	cursor: pointer;
}

button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

select,
input[type="range"] {
	padding: 4px;
}
</style>
