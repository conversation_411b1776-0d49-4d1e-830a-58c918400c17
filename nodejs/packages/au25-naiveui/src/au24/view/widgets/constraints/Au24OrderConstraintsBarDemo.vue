<template>
	<div style="height: 600px">
		<br />

		<DeOrderConstraintsBar
			:constraints="constraints"
			:height="height"
            :max_quantity="max_quantity"
			:order_quantity="order_quantity"
			:order_type="order_type"
			:show_labels="show_labels"
			:tick_font_size="tick_font_size"
            :tick_step="tick_step"
            :width="width"
		/>

		<br />

		<DefineInputSlider v-slot="{ label, min, max, step, value, 'onUpdate:value': updateValue }">
			<n-form-item
				:label="label"
				label-align="right"
				:label-width="120"
				label-placement="left"
				style="padding: 5px"
			>
				<NInputNumber :value="value" style="width: 100px" @update:value="updateValue" />
				<NSlider :max="max" :min="min" :step="step" :value="value" class="slider" @update:value="updateValue" />
			</n-form-item>
		</DefineInputSlider>

		<NForm :show-feedback="false">
			<ReuseInputSlider :max="500" :min="0" :step="50" :value="width" label="Width"
                              @update:value="v => (width = v)" />

			<ReuseInputSlider :max="50" :min="5"  :step="1" :value="height" label="Height" @update:value="v => (height = v)" />

			<ReuseInputSlider
				:max="100"
				:min="20"
                :step="5"
                :value="max_quantity"
				label="max quantity"
				@update:value="v => (max_quantity = v)"
			/>

			<ReuseInputSlider
				:max="50"
				:min="0"
                :step="1"
                :value="constraints.max_buy_quantity"
				label="max buy"
				@update:value="v => (constraints.max_buy_quantity = v)"
			/>

			<ReuseInputSlider
				:max="max_quantity"
				:min="0"
                :step="1"
                :value="constraints.min_buy_quantity"
				label="min buy"
				@update:value="v => (constraints.min_buy_quantity = v)"
			/>

			<ReuseInputSlider
				:max="max_quantity"
				:min="0"
                :step="1"
                :value="constraints.max_sell_quantity"
				label="max sell"
				@update:value="v => (constraints.max_sell_quantity = v)"
			/>

			<ReuseInputSlider
				:max="max_quantity"
				:min="0"
                :step="1"
                :value="constraints.min_sell_quantity"
				label="min sell"
				@update:value="v => (constraints.min_sell_quantity = v)"
			/>

			<ReuseInputSlider
				:max="15"
				:min="0"
                :step="1"
                :value="tick_font_size"
				label="tick font size"
				@update:value="v => (tick_font_size = v)"
			/>

            <ReuseInputSlider
                    :max="max_quantity"
                    :min="5"
                    :step="5"
                    :value="tick_step"
                    label="tick step"
                    @update:value="v => (tick_step = v)"
            />


            <ReuseInputSlider
				:max="max_quantity"
				:min="0"
                :step="1"
                :value="order_quantity"
				label="order quantity"
				@update:value="v => (order_quantity = v)"
			/>

			<n-form-item :label-width="120" label="order side" label-align="right" label-placement="left">
				<NButton class="control" size="small" style="width: 50px" @click="order_type = OrderType.NONE">
					None
				</NButton>
				<NButton class="control" size="small" style="width: 50px" @click="order_type = OrderType.BUY">
					Buy
				</NButton>
				<NButton class="control" size="small" style="width: 50px" @click="order_type = OrderType.SELL">
					Sell
				</NButton>
			</n-form-item>

			<n-form-item :label-width="120" label="show labels" label-align="right" label-placement="left">
				<NSwitch v-model:value="show_labels" />
			</n-form-item>
		</NForm>
	</div>
</template>

<script lang="ts" setup>
import { ref } from "vue"
import DeOrderConstraintsBar from "./Au24OrderConstraintsBar.vue"
import { type DeBidConstraints, OrderType } from "@/au24/types/generated.js"
import { createReusableTemplate } from "@vueuse/core"

const [DefineInputSlider, ReuseInputSlider] = createReusableTemplate<{
	label: string
	min: number
	max: number
    step: number
	value: number
	"onUpdate:value": (val: number) => void
}>()

const width = ref(300)
const height = ref(30)
const max_quantity = ref(50)
const tick_step = ref(10)
const tick_font_size = ref(12)
const order_type = ref<OrderType>(OrderType.BUY)
const order_quantity = ref<number>(10)
const show_labels = ref(true)

const constraints = ref<DeBidConstraints>({
	max_buy_quantity: 40,
	max_sell_quantity: 20,
	min_buy_quantity: 0,
	min_sell_quantity: 0
})
</script>

<style lang="less" scoped>
.label {
	text-align: right;
	width: 120px;
}

.control {
	width: 400px;
}

.slider {
	display: inline-block;
	top: -5px;
	width: 100px;
}
</style>
