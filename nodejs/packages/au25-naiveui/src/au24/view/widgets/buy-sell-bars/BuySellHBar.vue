<template>
	<div class="BuySellHBar">
		<div :style="volStyleComputed"></div>
		<div :style="matchStyleComputed"></div>
		<div class="_match" :style="{ color: colors.au_match(), right: matchTextRight }">
			<Blinker :value="`(${match})`" />
		</div>
		<div class="_vol" :style="{ color: volTextColor, right: volTextRight }">
			<Blinker :value="orderQuantityStrWithSubmissionType" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, CSSProperties } from "vue"
import { auColors, AuColors } from "@/au24/au-styles/AuColors.ts"

import { OrderSubmissionType, OrderType } from "@/au24/types/generated.js"
import Blinker from "@/au24/view/ui-component/blinker/Blinker.vue"

const props = defineProps({
	width: { type: Number, default: 50 },
	height: { type: Number, default: 26 },
	buyMax: { type: Number, required: true },
	sellMax: { type: Number, required: true },
	match: { type: Number, required: true },
	order_quantity_int: { type: Number, required: true },
	order_quantity_str: { type: String, required: true },
	order_type: {
		type: String as () => keyof typeof OrderType,
		required: true,
		validator: (value: any) => Object.values(OrderType).includes(value)
	},
	order_submission_type: {
		type: String as () => keyof typeof OrderSubmissionType,
		required: true,
		validator: (value: any) => Object.values(OrderSubmissionType).includes(value)
	}
})

const colors: AuColors = auColors

const isSell = computed(() => props.order_type == OrderType.SELL)

const volBarColor = computed(() => (isSell.value ? colors.au_sell_dimmedX2() : colors.au_buy_dimmedX2()))

const volBarWidth = computed(
	() => (props.width * props.order_quantity_int) / (isSell.value ? props.sellMax : props.buyMax)
)

const matchBarWidth = computed(() => (props.width * props.match) / (isSell.value ? props.sellMax : props.buyMax))

const volLeft = computed(() => (isSell.value ? 0 : props.width - volBarWidth.value))

const matchLeft = computed(() => (isSell.value ? 0 : props.width - matchBarWidth.value))

const volStyleComputed = computed<CSSProperties>(() => ({
	position: "absolute",
	left: `${volLeft.value}px`,
	width: `${volBarWidth.value}px`,
	height: `${props.height}px`,
	backgroundColor: `${volBarColor.value}`
}))

const matchStyleComputed = computed<CSSProperties>(() => ({
	position: "absolute",
	left: `${matchLeft.value}px`,
	width: `${matchBarWidth.value}px`,
	height: `${props.height}px`,
	backgroundColor: `${colors.au_match_dimmedX2()}`
}))

const volTextRight = computed(() => (isSell.value ? "-30px" : "-55px"))

const matchTextRight = computed(() => (isSell.value ? "-50px" : "-30px"))

const volTextColor = computed(() => (isSell.value ? colors.au_sell() : colors.au_buy()))

const barHeight = computed(() => props.height)

const orderQuantityStrWithSubmissionType = computed(() => {
	switch (props.order_submission_type) {
		case OrderSubmissionType.MANUAL:
			return props.order_quantity_str
		case OrderSubmissionType.DEFAULT:
			return `${props.order_quantity_str}*`
		case OrderSubmissionType.MANDATORY:
			return `${props.order_quantity_str}!`
		default:
			return "---"
	}
})
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.BuySellHBar {
	._match {
		font-size: 10px;
		position: absolute;
		text-align: right;
		top: 3px;
	}

	._vol {
		position: absolute;
		text-align: right;
		top: 3px;
	}
}
</style>
