<template>
	<VbDemo>
		<VbCard title="Manual SELL 20">
			<BuySellVBar
				:width="60"
				:height="20"
				:buy-max="50"
				:sell-max="50"
				:match="20"
				:order_quantity_int="20"
				order_quantity_str="`20`"
				:order_type="OrderType.SELL"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</VbCard>
		<VbCard title="Manual BUY 20">
			<BuySellVBar
				:width="60"
				:height="20"
				:buy-max="50"
				:sell-max="50"
				:match="10"
				:order_quantity_int="20"
				order_quantity_str="`20`"
				:order_type="OrderType.BUY"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</VbCard>
		<VbCard title="Manual 0 quantity">
			<BuySellVBar
				:width="60"
				:height="20"
				:buy-max="50"
				:sell-max="50"
				:match="30"
				:order_quantity_int="0"
				order_quantity_str="`0`"
				:order_type="OrderType.NONE"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</VbCard>
	</VbDemo>
</template>

<script setup lang="ts">
import BuySellVBar from "./BuySellVBar.vue"
import { OrderSubmissionType, OrderType } from "@/au24/types/generated.js"
import VbCard from "@/helpers/vuebook/VbCard.vue"
import VbDemo from "@/helpers/vuebook/VbDemo.vue"
</script>

<style scoped>
.VbCard {
	background-color: #252525;
}
</style>
