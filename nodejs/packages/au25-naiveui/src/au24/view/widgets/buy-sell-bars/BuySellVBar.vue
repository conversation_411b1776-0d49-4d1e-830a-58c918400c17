<template>
	<div class="BuySellVBar" :style="{ width: `${width + widthAdjust}px`, height: `${height}px` }">
		<svg
			class="_bar"
			:viewBox="`0 0 ${width + widthAdjust} ${height}`"
			:width="width + widthAdjust"
			:height="height"
		>
			<rect
				class="_rect"
				:y="volBarTop"
				:x="0"
				:height="volBarHeight"
				:width="width + widthAdjust"
				:fill="barColor"
			/>
			<rect
				class="_rect"
				:y="matchBarTop"
				:x="0"
				:height="matchBarHeight"
				:width="width + widthAdjust"
				:fill="colors.au_match_dimmed()"
			/>
		</svg>

		<div class="_match" :style="{ color: colors.au_match() }">
			<Blinker :value="`(${match}) `" />
		</div>

		<div class="_quantity" :style="{ color: auTextColor }">
			<Blinker :value="orderQuantity" />
		</div>

		<div class="_submission_label" :style="{ color: auTextColor }">
			<Blinker :value="orderSubmissionLabel" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { auColors, AuColors } from "@/au24/au-styles/AuColors.ts"

import { OrderSubmissionType, OrderType } from "@/au24/types/generated.js"
import Blinker from "@/au24/view/ui-component/blinker/Blinker.vue"

const props = defineProps({
	width: { type: Number, default: 65 },
	height: { type: Number, default: 20 },
	buyMax: { type: Number, required: true },
	sellMax: { type: Number, required: true },
	match: { type: Number, required: true },
	order_quantity_int: { type: Number, required: true },
	order_quantity_str: { type: String, required: true },
	order_type: {
		type: String as () => keyof typeof OrderType,
		required: true,
		validator: (value: any) => Object.values(OrderType).includes(value)
	},
	order_submission_type: {
		type: String as () => keyof typeof OrderSubmissionType,
		required: true,
		validator: (value: any) => Object.values(OrderSubmissionType).includes(value)
	}
})

const colors: AuColors = auColors

const barWidth = 10
const widthAdjust = 0

const isSell = computed(() => props.order_type == OrderType.SELL)

const center = computed(() => props.width / 2)

const volBarHeight = computed(
	() => (props.order_quantity_int * props.height) / (isSell.value ? props.sellMax : props.buyMax)
)

const volBarTop = computed(() => props.height - volBarHeight.value)

const matchBarHeight = computed(() => (props.match * props.height) / (isSell.value ? props.sellMax : props.buyMax))

const matchBarTop = computed(() => props.height - matchBarHeight.value)

const barColor = computed(() => (isSell.value ? "hsla(0, 92%, 65%, 0.15)" : "hsla(120, 47%, 64%, 0.15)"))

const textColor = computed(() => (isSell.value ? colors.au_sell() : colors.au_buy()))

const orderQuantity = computed(() => props.order_quantity_str)

const orderSubmissionLabel = computed(() => {
	switch (props.order_submission_type) {
		case OrderSubmissionType.MANUAL:
			return ""
		case OrderSubmissionType.DEFAULT:
			return "d"
		case OrderSubmissionType.MANDATORY:
			return "m"
		default:
			return "---"
	}
})

const auTextColor = computed(() => colors.order_quantity_text_color(props.order_type as OrderType))
</script>

<style lang="less" scoped>
@import "@/au24/au-styles/variables.less";

.BuySellVBar {
	border: 0;
	margin: 0;
	padding: 0;
	position: relative;

	._bar {
		border: 0;
		overflow: hidden;
		position: absolute;
		padding: 0;
		margin: 0;
	}

	._quantity {
		border: 0;
		display: inline-block;
		font-size: 13px;
		margin: 0;
		padding: 0;
		position: absolute;
		right: 22px;
		text-align: right;
	}

	._submission_label {
		display: inline-block;
		font-size: 11px;
		position: relative;
		left: 55px;
	}

	._match {
		display: inline-block;
		font-size: 10px;
		position: absolute;
		right: 47px;
		text-align: right;
	}
}
</style>
