<template>
	<div>
		<button @click="toggle_order">toggle order</button>
		<br />
		<div>
			title="Manual SELL 20"
			<br />
			<BuySellHBar
				:width="60"
				:height="20"
				:buy-max="50"
				:sell-max="50"
				:match="10"
				:order_quantity_int="order_quantity"
				:order_quantity_str="order_quantity + ''"
				:order_type="OrderType.SELL"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</div>
		<div>
			title="Manual BUY 20"
			<br />
			<BuySellHBar
				:width="40"
				:height="30"
				:buy-max="50"
				:sell-max="50"
				:match="0"
				:order_quantity_int="order_quantity"
				:order_quantity_str="order_quantity + ''"
				:order_type="OrderType.BUY"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</div>
		<div>
			title="Manual 0 quantity"
			<br />
			<BuySellHBar
				:width="60"
				:height="20"
				:buy-max="50"
				:sell-max="50"
				:match="20"
				:order_quantity_int="order_quantity"
				:order_quantity_str="order_quantity + ''"
				:order_type="OrderType.NONE"
				:order_submission_type="OrderSubmissionType.MANUAL"
			/>
		</div>
	</div>
</template>

<script setup lang="ts">
import BuySellHBar from "./BuySellHBar.vue"
import { OrderSubmissionType, OrderType } from "@/au24/types/generated.js"
import { ref } from "vue"

const order_quantity = ref(20)

function toggle_order() {
	if (order_quantity.value == 0) {
		order_quantity.value = 20
	} else {
		order_quantity.value = 0
	}
}
</script>

<style scoped></style>
