<template>
	<div
		class="page page-wrapped page-mobile-full flex flex-col page-without-footer"
		:style="{ height: chatHeight + 'px' }"
		style="border: 1px solid blue"
	>
		<div class="main" ref="main" style="border: 1px solid yellow">
			<div class="chat-view grow" v-if="chatStore.activeChat">
				<n-scrollbar style="max-height: 100%" ref="chatViewList">
					<div
						v-for="conversation of chatStore.activeChat.conversation"
						:key="conversation.id"
						class="conversation item-appear item-appear-bottom item-appear-010 flex"
						:class="{ mine: conversation.isMine }"
					>
						<div class="avatar">
							<n-avatar round size="large" :src="conversation.userObj.avatar" />
						</div>
						<div class="messages-group flex flex-col">
							<div class="message" v-for="message of conversation.messages" :key="message.text">
								{{ message.text }}
							</div>
							<div class="date">
								<n-time :time="conversation.date" format="d MMM @ HH:mm" />
							</div>
						</div>
					</div>
				</n-scrollbar>
			</div>

			<div class="empty-view grow flex flex-col items-center justify-center" v-if="!chatStore.activeChat">
				<Icon :name="ChatIcon" :size="48" />
				<div class="text-xl mt-4">Select a Contact</div>
			</div>
		</div>
		<div class="message-editor flex" v-if="chatStore.activeChat">
			<div class="text-input grow">
				<n-input
					placeholder="Message..."
					type="textarea"
					size="small"
					@blur="resetWindowScroll()"
					:autosize="{
						minRows: 1,
						maxRows: 5
					}"
				/>
			</div>
			<div class="actions-group flex items-center">
				<n-button strong ghost circle type="primary">
					<Icon :name="SendIcon" :size="20" />
				</n-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, type RendererElement, type RendererNode, type VNode } from "vue"
import { NAvatar, NButton, NInput, NScrollbar, NTime } from "naive-ui"
import { useWindowSize } from "@vueuse/core"
import { useThemeStore } from "@/stores/theme"
import { type Contact } from "@/mock/chat"
import { useHideLayoutFooter } from "@/composables/useHideLayoutFooter"
import Icon from "@/components/common/Icon.vue"
import { type CtxSegmentedPage } from "@/components/common/SegmentedPage.vue"
import { useChatStore } from "@/stores/useChatStore.ts"

const ChatIcon = "carbon:chat"
const TrashIcon = "carbon:trash-can"
const MenuHorizontalIcon = "carbon:overflow-menu-horizontal"
const SearchIcon = "carbon:search"
const VideoIcon = "carbon:video"
const PhoneIcon = "carbon:phone"
const InfoIcon = "carbon:information"
const MuteIcon = "fluent:alert-off-16-regular"
const BlockUserIcon = "tabler:user-off"
const MicrophoneIcon = "carbon:microphone"
const AttachmentIcon = "carbon:attachment"
const SendIcon = "carbon:send"

interface MenuItem {
	label: string
	key: string
	icon: () => VNode<RendererNode, RendererElement, { [key: string]: any }>
}

const themeStore = useThemeStore()
const chatStore = useChatStore()
const main = ref(null)
const ctxPage = ref<CtxSegmentedPage | null>(null)
const chatViewList = ref<typeof NScrollbar | null>(null)
const menuOptions = ref<MenuItem[]>([])

function setCtx(ctx: CtxSegmentedPage) {
	ctxPage.value = ctx
	chatViewList.value = ctx.mainScrollbar
}

const { height: pageHeight, width: pageWidth } = useWindowSize()
const chatHeight = computed(() => pageHeight.value - 100)

function setChat(user: Contact) {
	ctxPage.value?.closeSidebar && ctxPage.value.closeSidebar()
	chatStore.setActiveChat(user)
	resetChatScroll()
}

function resetChatScroll() {
	setTimeout(() => {
		chatViewList.value?.scrollTo(100000)
	}, 50)
}

function resetWindowScroll() {
	window.scrollTo(0, 0)
}

onMounted(() => {
	resetChatScroll()

	nextTick(() => {
		const duration = 1000 * themeStore.routerTransitionDuration
		const gap = 500

		// TIMEOUT REQUIRED BY PAGE ANIMATION
		setTimeout(() => {
			resetChatScroll()
		}, duration + gap)
	})
})

// :has() CSS relational pseudo-class not yet supported by Firefox
// (https://caniuse.com/css-has)
// at the moment this worker around permit to hide Layout Footer
useHideLayoutFooter()
</script>

<style lang="scss" scoped>
.page {
	.main {
		position: relative;
		container-type: inline-size;

		.chat-view {
			.conversation {
				padding: 20px 0px;
				gap: 14px;

				.messages-group {
					width: fit-content;
					max-width: 60%;

					.message {
						background-color: var(--bg-secondary-color);
						margin-bottom: 5px;
						padding: 5px 10px;
						border-radius: var(--border-radius);
						width: fit-content;
						font-size: 14px;
					}

					.date {
						opacity: 0.8;
						font-size: 12px;
						padding: 0 3px;
					}
				}

				&.mine {
					flex-direction: row-reverse;

					.messages-group {
						align-items: end;

						.message {
							background-color: var(--primary-color);
							color: var(--bg-color);
						}
					}
				}
			}

			@container (max-width: 500px) {
				.conversation {
					.messages-group {
						max-width: 90%;
					}
				}
			}
		}
	}

	.message-editor {
		padding: 10px 0px;
		gap: 20px;
		width: 100%;

		.text-input {
			display: flex;
			align-items: center;

			.n-input--textarea {
				background-color: transparent;

				:deep() {
					.n-input__border,
					.n-input__state-border {
						display: none;
					}
				}
			}
		}

		.actions-group {
			gap: 20px;
		}
	}
}
</style>
