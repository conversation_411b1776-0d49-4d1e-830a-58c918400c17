<template>
	<div>
		<NButton @click="resetChatScroll">reset</NButton>
		<NScrollbar ref="chatViewList" style="max-height: 100%" content-style="overflow: hidden;" trigger="none">
			content-style="overflow: hidden;"
			<p style="margin-bottom: 90px">margin-bottom: 90px</p>
			<p style="margin-bottom: 90px">margin-bottom: 90px</p>
		</NScrollbar>
		<div class="message-editor flex" v-if="chatStore.activeChat">
			<div class="text-input grow">
				<n-input
					placeholder="Message..."
					type="textarea"
					size="small"
					@blur="resetWindowScroll()"
					:autosize="{
						minRows: 1,
						maxRows: 5
					}"
				/>
			</div>
			<div class="actions-group flex items-center">
				<n-button strong ghost circle type="primary">
					<Icon :name="SendIcon" :size="20" />
				</n-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { NButton, NInput, NScrollbar } from "naive-ui"
import Icon from "@/components/common/Icon.vue"
import { useChatStore } from "@/stores/apps/useChatStore.ts"
import { ref } from "vue"

const SendIcon = "carbon:send"
const chatStore = useChatStore()

const chatViewList = ref<typeof NScrollbar | null>(null)

function resetChatScroll() {
	setTimeout(() => {
		chatViewList.value?.scrollTo(100000)
	}, 50)
}
</script>

<style></style>
