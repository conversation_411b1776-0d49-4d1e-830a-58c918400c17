<template>
	<div class="chart-container" :style="{ width: `${width}px`, height: `${height}px` }">
		<svg ref="svg" :height="height">
			<g class="y-axis"></g>
			<g class="scroll-content" ref="scrollContent"></g>
		</svg>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue"
import * as d3 from "d3"
import { hsl } from "d3-color"

export type OrderSide = "BUY" | "SELL"

export type TradingHistory = {
	trader_name: string
	initial_buy_quantity: number
	initial_sell_quantity: number
	rounds: Array<{
		round_num: number
		price: number
		order: {
			quantity: number
			side: OrderSide
		}
		quantity_constraints: {
			max_buy: number
			min_buy: number
			max_sell: number
			min_sell: number
		}
	}>
}

const props = defineProps<{
	data: TradingHistory
	initial_buy_quantity: number
	initial_sell_quantity: number
	width?: number
	height?: number
	marginTop?: number
	marginRight?: number
	marginBottom?: number
	marginLeft?: number
	barWidth?: number
}>()

const svg = ref<SVGSVGElement | null>(null)
const scrollContent = ref<SVGGElement | null>(null)

const sell_hue = 0 // Red hue
const buy_hue = 120 // Green hue

const getColor = (side: OrderSide, level: "initial_constraint" | "round_constraint" | "order") => {
	const baseHue = side === "SELL" ? sell_hue : buy_hue

	switch (`${side}-${level}`) {
		case "SELL-initial_constraint":
			return hsl(baseHue, 0.7, 0.8).toString()
		case "SELL-round_constraint":
			return hsl(baseHue, 0.8, 0.6).toString()
		case "SELL-order":
			return hsl(baseHue, 0.9, 0.5).toString()
		case "BUY-initial_constraint":
			return hsl(baseHue, 0.5, 0.8).toString()
		case "BUY-round_constraint":
			return hsl(baseHue, 0.6, 0.6).toString()
		case "BUY-order":
			return hsl(baseHue, 0.7, 0.5).toString()
		default:
			console.log("Unexpected color combination:", side, level)
			return "white"
	}
}

const drawChart = () => {
	if (!svg.value || !scrollContent.value) return

	const width = props.width || 400
	const height = props.height || 400
	const marginTop = props.marginTop || 20
	const marginRight = props.marginRight || 20
	const marginBottom = props.marginBottom || 40
	const marginLeft = props.marginLeft || 60
	const barWidth = props.barWidth || 40

	const rounds = props.data.rounds.map(d => d.round_num.toString())
	const totalWidth = rounds.length * barWidth + marginLeft + marginRight

	// Clear previous content
	d3.select(svg.value).selectAll("*").remove()

	// Set up scales
	const x = d3
		.scaleBand()
		.domain(rounds)
		.range([0, totalWidth - marginLeft - marginRight])
		.padding(0.1)

	const y = d3
		.scaleLinear()
		.domain([-props.initial_buy_quantity, props.initial_sell_quantity])
		.range([height - marginTop - marginBottom, 0])

	// Create a group for the scrollable content
	const scrollGroup = d3.select(scrollContent.value).attr("transform", `translate(${marginLeft},${marginTop})`)

	// Add x-axis
	scrollGroup
		.append("g")
		.attr("class", "x-axis")
		.attr("transform", `translate(0,${height - marginTop - marginBottom})`)
		.call(d3.axisBottom(x).tickFormat(d => `Round ${d}`))

	// Add fixed y-axis
	d3.select(svg.value)
		.select(".y-axis")
		.attr("transform", `translate(${marginLeft},${marginTop})`)
		.call(d3.axisLeft(y).tickFormat(d => Math.abs(Number(d)).toString()))

	// Add center line
	scrollGroup
		.append("line")
		.attr("x1", 0)
		.attr("x2", totalWidth - marginLeft - marginRight)
		.attr("y1", y(0))
		.attr("y2", y(0))
		.attr("stroke", "#555")
		.attr("stroke-width", 1)

	// Initial quantity bars
	const initialQuantityGroup = scrollGroup.append("g")

	initialQuantityGroup
		.selectAll(".initial-buy-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "initial-buy-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", y(0))
		.attr("width", x.bandwidth())
		.attr("height", y(-props.initial_buy_quantity) - y(0))
		.attr("fill", () => getColor("BUY", "initial_constraint"))

	initialQuantityGroup
		.selectAll(".initial-sell-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "initial-sell-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", y(props.initial_sell_quantity))
		.attr("width", x.bandwidth())
		.attr("height", y(0) - y(props.initial_sell_quantity))
		.attr("fill", () => getColor("SELL", "initial_constraint"))

	// Round constraint bars
	const barGroup = scrollGroup.append("g")

	barGroup
		.selectAll(".sell-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "sell-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", d => y(d.quantity_constraints.max_sell))
		.attr("width", x.bandwidth())
		.attr("height", d => Math.abs(y(d.quantity_constraints.max_sell) - y(d.quantity_constraints.min_sell)))
		.attr("fill", () => getColor("SELL", "round_constraint"))

	barGroup
		.selectAll(".buy-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "buy-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", d => y(-d.quantity_constraints.min_buy))
		.attr("width", x.bandwidth())
		.attr("height", d => Math.abs(y(-d.quantity_constraints.min_buy) - y(-d.quantity_constraints.max_buy)))
		.attr("fill", () => getColor("BUY", "round_constraint"))

	// Order bars
	const orderGroup = scrollGroup.append("g")

	orderGroup
		.selectAll(".order-bar")
		.data(props.data.rounds)
		.join("rect")
		.attr("class", "order-bar")
		.attr("x", d => x(d.round_num.toString()) || 0)
		.attr("y", d => (d.order.side === "SELL" ? y(d.order.quantity) : y(0)))
		.attr("width", x.bandwidth())
		.attr("height", d => Math.abs(y(d.order.side === "SELL" ? d.order.quantity : -d.order.quantity) - y(0)))
		.attr("fill", d => getColor(d.order.side, "order"))

	// Set the viewBox to enable scrolling
	d3.select(svg.value).attr("viewBox", `0 0 ${width} ${height}`).attr("width", width).attr("height", height)

	// Set the width of the scroll content
	d3.select(scrollContent.value).attr("width", totalWidth)
}

onMounted(() => {
	drawChart()
})

watch(
	() => props.data,
	() => {
		drawChart()
	},
	{ deep: true }
)
</script>

<style scoped>
.chart-container {
	overflow-x: auto;
	overflow-y: hidden;
}

svg {
	display: block;
}

.scroll-content {
	will-change: transform;
}
</style>
