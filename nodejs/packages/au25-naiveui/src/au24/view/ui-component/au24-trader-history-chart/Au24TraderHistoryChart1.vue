<template>
	<div ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue"
import * as d3 from "d3"

export interface DataPoint {
	price: number
	quantity: number
}

const props = defineProps<{
	data: DataPoint[]
	width?: number
	height?: number
	marginTop?: number
	marginRight?: number
	marginBottom?: number
	marginLeft?: number
}>()

const chartContainer = ref<HTMLElement | null>(null)

const drawChart = () => {
	if (!chartContainer.value) return

	const width = props.width || 800
	const height = props.height || 400
	const marginTop = props.marginTop || 20
	const marginRight = props.marginRight || 20
	const marginBottom = props.marginBottom || 40
	const marginLeft = props.marginLeft || 40

	const svg = d3.select(chartContainer.value).append("svg").attr("width", width).attr("height", height)

	const x = d3
		.scaleBand()
		.domain(props.data.map((_, i) => i.toString()))
		.range([marginLeft, width - marginRight])
		.padding(0.1)

	const y = d3
		.scaleLinear()
		.domain([0, d3.max(props.data, d => d.quantity) || 0])
		.nice()
		.range([height - marginBottom, marginTop])

	const xAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
		g
			.attr("transform", `translate(0,${height - marginBottom})`)
			.call(d3.axisBottom(x).tickFormat((_, i) => props.data[i].price.toString()))

	const yAxis = (g: d3.Selection<SVGGElement, unknown, null, undefined>) =>
		g
			.attr("transform", `translate(${marginLeft},0)`)
			.call(d3.axisLeft(y).ticks(null, "s"))
			.call(g => g.select(".domain").remove())
			.call(g =>
				g
					.selectAll(".tick line")
					.clone()
					.attr("x2", width - marginLeft - marginRight)
					.attr("stroke-opacity", 0.1)
			)

	svg.append("g").call(xAxis)
	svg.append("g").call(yAxis)

	svg.append("g")
		.attr("fill", "steelblue")
		.selectAll("rect")
		.data(props.data)
		.join("rect")
		.attr("x", (_, i) => x(i.toString()) || 0)
		.attr("y", d => y(d.quantity))
		.attr("height", d => y(0) - y(d.quantity))
		.attr("width", x.bandwidth())

	svg.append("text")
		.attr("x", width / 2)
		.attr("y", height - 5)
		.attr("text-anchor", "middle")
		.text("Price")

	svg.append("text")
		.attr("transform", "rotate(-90)")
		.attr("x", -height / 2)
		.attr("y", 15)
		.attr("text-anchor", "middle")
		.text("Quantity")
}

onMounted(() => {
	drawChart()
})

watch(
	() => props.data,
	() => {
		if (chartContainer.value) {
			chartContainer.value.innerHTML = ""
			drawChart()
		}
	},
	{ deep: true }
)
</script>
