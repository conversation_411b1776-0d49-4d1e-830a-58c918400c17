<template>
	<Au24TraderHistoryChart3
		:data="sampleTradingData2"
		:initial_buy_quantity="50"
		:initial_sell_quantity="50"
		:height="200"
		:width="400"
	/>
	<Au24TraderHistoryChart
		:data="sampleTradingData"
		:initial_buy_quantity="50"
		:initial_sell_quantity="50"
		:height="200"
		:width="400"
	/>
	<!--	<Au24TraderHistoryChart1-->
	<!--		:data="chartData"-->
	<!--		:height="200"-->
	<!--		:width="400"/>-->
</template>

<script setup lang="ts">
import Au24TraderHistoryChart, {
	TradingHistory
} from "@/au24/view/ui-component/au24-trader-history-chart/Au24TraderHistoryChart.vue"
import Au24TraderHistoryChart3 from "@/au24/view/ui-component/au24-trader-history-chart/Au24TraderHistoryChart3.vue"

// const chartData = [
// 	{ price: 10, quantity: 5 },
// 	{ price: 20, quantity: 8 },
// 	{ price: 30, quantity: 12 },
// 	{ price: 40, quantity: 3 },
// 	{ price: 50, quantity: 7 }
// ]

const sampleTradingData: TradingHistory = {
	trader_name: "trader one",
	initial_buy_quantity: 50,
	initial_sell_quantity: 50,
	rounds: [
		{
			price: 100,
			round_num: 1,
			order: {
				side: "BUY",
				quantity: 15
			},
			quantity_constraints: {
				max_sell: 50,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 105,
			round_num: 2,
			order: {
				side: "SELL",
				quantity: 10
			},
			quantity_constraints: {
				max_sell: 50,
				min_sell: 0,
				min_buy: 0,
				max_buy: 15
			}
		},
		{
			price: 110,
			round_num: 3,
			order: {
				side: "SELL",
				quantity: 20
			},
			quantity_constraints: {
				max_sell: 50,
				min_sell: 10,
				min_buy: 0,
				max_buy: 0
			}
		},
		{
			price: 115,
			round_num: 4,
			order: {
				side: "SELL",
				quantity: 30
			},
			quantity_constraints: {
				max_sell: 50,
				min_sell: 20,
				min_buy: 0,
				max_buy: 0
			}
		}
	]
}

const sampleTradingData2: TradingHistory = {
	trader_name: "trader one",
	initial_buy_quantity: 50,
	initial_sell_quantity: 50,
	rounds: [
		{
			price: 100,
			round_num: 1,
			order: {
				side: "SELL",
				quantity: 45
			},
			quantity_constraints: {
				max_sell: 50,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 99,
			round_num: 2,
			order: {
				side: "SELL",
				quantity: 40
			},
			quantity_constraints: {
				max_sell: 45,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 98,
			round_num: 3,
			order: {
				side: "SELL",
				quantity: 35
			},
			quantity_constraints: {
				max_sell: 40,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 97,
			round_num: 4,
			order: {
				side: "SELL",
				quantity: 30
			},
			quantity_constraints: {
				max_sell: 35,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 96,
			round_num: 5,
			order: {
				side: "SELL",
				quantity: 25
			},
			quantity_constraints: {
				max_sell: 30,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 95,
			round_num: 6,
			order: {
				side: "BUY",
				quantity: 5
			},
			quantity_constraints: {
				max_sell: 25,
				min_sell: 0,
				min_buy: 0,
				max_buy: 50
			}
		},
		{
			price: 94,
			round_num: 7,
			order: {
				side: "BUY",
				quantity: 10
			},
			quantity_constraints: {
				max_sell: 0,
				min_sell: 0,
				min_buy: 5,
				max_buy: 50
			}
		},
		{
			price: 94.25,
			round_num: 8,
			order: {
				side: "BUY",
				quantity: 10
			},
			quantity_constraints: {
				max_sell: 0,
				min_sell: 0,
				min_buy: 0,
				max_buy: 10
			}
		},
		{
			price: 94.5,
			round_num: 9,
			order: {
				side: "BUY",
				quantity: 5
			},
			quantity_constraints: {
				max_sell: 0,
				min_sell: 0,
				min_buy: 0,
				max_buy: 10
			}
		},
		{
			price: 94.75,
			round_num: 10,
			order: {
				side: "BUY",
				quantity: 0
			},
			quantity_constraints: {
				max_sell: 0,
				min_sell: 0,
				min_buy: 0,
				max_buy: 5
			}
		}
	]
}
</script>

<style scoped></style>
