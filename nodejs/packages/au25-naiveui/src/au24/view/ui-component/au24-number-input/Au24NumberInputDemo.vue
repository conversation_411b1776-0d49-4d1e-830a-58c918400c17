<template>
	<div>
		<n-space vertical>
			show button: {{ show_button }}
			<button @click="show_button = !show_button">toggle show button</button>
			<br />
			<Au24NumberInput :value="input_value" :decimal_places :suffix :show_button />
			<br />
			Decimal places: {{ decimal_places }}
			<input type="number" v-model="decimal_places" />
			suffix: {{ suffix }}
			<input type="text" v-model="suffix" />
			<br />
			input value:{{ input_value }}
			<input type="number" v-model="input_value" />
		</n-space>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import Au24NumberInput from "@/au24/view/ui-component/au24-number-input/Au24NumberInput.vue"

const show_button = ref(false)
const input_value = ref(20)
const decimal_places = ref(0)
const suffix = ref("")
</script>

<style scoped>
/* Add your styles here */
</style>
