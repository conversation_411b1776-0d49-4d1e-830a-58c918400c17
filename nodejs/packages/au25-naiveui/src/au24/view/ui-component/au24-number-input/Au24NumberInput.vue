<template>
	<n-input-number
		:value="internalValue"
		:format="formatNumber"
		:parse="parseNumber"
		:min="0"
		:step="1"
		:precision="0"
		:show-button="show_button"
		style="text-align: right"
	>
		<template #prefix>
			{{ prefix }}
		</template>
		<template #suffix>&nbsp; {{ suffix }} decimal places: {{ decimal_places }}</template>
	</n-input-number>
</template>

<script setup lang="ts">
import { nextTick, ref, watch } from "vue"
import { NInputNumber } from "naive-ui"

const prefix = "" // "$"

const value = defineModel("value", { required: true })

const {
	show_button = false,
	suffix = "",
	decimal_places = 0
} = defineProps<{
	show_button?: boolean
	suffix?: string
	decimal_places?: number
}>()

const internalValue = ref()

const _formatNumber = (num, decimalPlaces) => {
	const clampedDecimalPlaces = Math.min(Math.max(decimalPlaces, 0), 100)
	const [integerPart, decimalPart] = num.toFixed(clampedDecimalPlaces).split(".")
	const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
	return decimalPart ? `${formattedInteger}.${decimalPart}` : formattedInteger
}

const formatNumber = (value: number | null) => {
	return _formatNumber(value ?? 0, decimal_places)
}

const parseNumber = (value: string) => {
	const parsedValue = Number(value.replace(/\$\s?|(,*)/g, ""))
	// Ensure the parsed value is non-negative and trim decimals
	return Math.floor(Math.max(0, parsedValue) * 100) / 100
}

watch([() => decimal_places, value], ([]) => {
	internalValue.value = model.value
	// Force a small change in the value to trigger the format function
	if (Number.isFinite(internalValue.value)) {
		const epsilon = Math.pow(10, -decimal_places - 1)
		internalValue.value = internalValue.value + epsilon
		console.log("epsilon", epsilon)

		// Use nextTick to revert the value back after the DOM has updated
		nextTick(() => {
			internalValue.value = internalValue.value - epsilon
		})
	}
})
</script>

<style scoped></style>
