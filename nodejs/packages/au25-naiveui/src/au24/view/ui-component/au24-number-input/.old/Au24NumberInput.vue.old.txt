<template>
	<n-input
		ref="input"
		:class="classComputed"
		:style="{ fontSize: fontSize + 'px', height: height + 'px' }"
		v-model:value="inputValue"
		:disabled="disabled || output"
		@blur="onBlur"
		@keyup.enter="onEnter"
	/>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted, onBeforeUnmount } from "vue"
import { formatNumeral, NumeralThousandGroupStyles } from "cleave-zen"

const props = defineProps({
	size: { type: String, default: "small" },
	decimalPlaces: { type: Number, default: 2 },
	disabled: { type: Boolean, default: false },
	output: { type: Boolean, default: false },
	fontSize: { type: Number, default: 13 },
	height: { type: Number, default: 30 },
	value: { type: [Number, String], default: '' }
})

const emit = defineEmits(["update:value", "blur", "enter", "escape"])

const input = ref(null)
const internalValue = ref(props.value)
const formatValueWithDecimals = (value: string): string => {

	const numericValue = parseFloat(value.replace(/,/g, ''))
	if (isNaN(numericValue)) {
		return ''
	}
	return numericValue.toFixed(props.decimalPlaces)
}
const inputValue = ref<string>(props.value !== null ? formatValueWithDecimals(props.value.toString()) : "")

const onBlur = () => {
	emit("blur")
	const formattedValue = formatValueWithDecimals(inputValue.value)
	inputValue.value = formattedValue
	emit("update:value", formattedValue)
}

const onEnter = () => {
	const formattedValue = formatValueWithDecimals(inputValue.value)
	inputValue.value = formattedValue
	emit("update:value", formattedValue)
	emit("enter")
}

const onInput = (event: Event) => {
	const target = event.target as HTMLInputElement
	if (!target) return
	const value = target.value.replace(/,/g, "") // Remove commas for proper parsing
	inputValue.value = value
	emit("update:value", value)
}

const onKeyup = (event: KeyboardEvent) => {
	if (event.code === "Escape") {
		emit("escape")
	}
}

const classComputed = computed(() => ({
	"au-input--output": props.output,
	"au-input--disabled": props.disabled
}))

watch(() => props.value, (newValue) => {
	internalValue.value = newValue
	inputValue.value = newValue !== null ? formatValueWithDecimals(newValue.toString()) : ""
})

onMounted(() => {
	const inputElement = input.value?.$el?.querySelector("input")
	if (inputElement) {
		inputElement.addEventListener("input", onInput)
		inputElement.addEventListener("keyup", onKeyup)
	}
})

onBeforeUnmount(() => {
	const inputElement = input.value?.$el?.querySelector("input")
	if (inputElement) {
		inputElement.removeEventListener("input", onInput)
		inputElement.removeEventListener("keyup", onKeyup)
	}
})
</script>

<style lang="less" scoped>
.NumberInput {
	border-radius: 4px;
	padding: 0 5px;
	text-align: right;

	// That's for IE11:
	line-height: 0.6;
}
</style>
