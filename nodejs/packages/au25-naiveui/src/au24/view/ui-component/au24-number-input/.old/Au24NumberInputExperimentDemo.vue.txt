<template>
	<div>
		<!--		<Au24NumberInput-->
		<!--			v-model:value="numericValue"-->
		<!--			format="0,0.00"-->
		<!--		/>-->

		Cleave 3: &nbsp;
		<Cleave3 v-model:modelValue="numericValue3" />
		<div>Formatted Value: {{ numericValue3 }}</div>
		<br>

		Cleave 2: &nbsp;
		<Cleave2 v-model:modelValue="numericValue2" />
		<div>Formatted Value: {{ numericValue2 }}</div>

		NInputNumber:
		<n-input-number
			:format="(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
			:parse="(value) => Number(value.replace(/\$\s?|(,*)/g, ''))"
			v-model:value="input3"
		/>
		input3: {{input3}}

		<n-input-number
			v-model:value="numberValue"
			:format="formatNumber"
			:parse="parseNumber"
		/>
		numberValue: {{ numberValue}}

		<n-input-number
			v-model:value="numberValue2"
			:format="formatNumber2"
			:parse="parseNumber2"
			:min="0"
			:step="1"
			:precision="0"
		/>
		<n-space vertical>
			<span><strong>numberValue2:</strong> {{ numberValue2 }}</span>
		</n-space>

	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import Cleave2 from "@/au24/view/ui-component/au24-number-input/Cleave2.vue"
import Cleave3 from "@/au24/view/ui-component/au24-number-input/Cleave3.vue"
import Cleave4 from "@/au24/view/ui-component/au24-number-input/Cleave4.vue"

const numericValue2 = ref<number | string>(0)
const numericValue3 = ref<number | string>(0)
const numericValue4 = ref<number | string>(0)

const input3 = ref(0)

// ---

const numberValue = ref(0)

const formatNumber = (value: number) => {
	return `$ ${value.toFixed(0)}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const parseNumber = (value: string) => {
	return Number(value.replace(/\$\s?|(,*)/g, ''))
}

// ----

const numberValue2 = ref(0)

const formatNumber2 = (value: number) => {
	// Ensure the value is non-negative
	const nonNegativeValue = Math.max(0, value)
	// Trim to two decimal places without rounding
	const trimmedValue = Math.floor(nonNegativeValue * 100) / 100
	return `$ ${trimmedValue.toFixed(0)}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

const parseNumber2 = (value: string) => {
	const parsedValue = Number(value.replace(/\$\s?|(,*)/g, ''))
	// Ensure the parsed value is non-negative and trim decimals
	return Math.floor(Math.max(0, parsedValue) * 100) / 100
}

</script>

<style scoped>
/* Add your styles here */
</style>
