<template>
	<n-input
		ref="inputRef"
		:value="modelValue"
		@blur="handleBlur"
	/>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { NInput } from 'naive-ui';
import Cleave from 'cleave.js';

const props = defineProps({
	modelValue: {
		type: String,
		required: true,
		default: null,
	},
	options: {
		type: Object,
		default: () => ({}),
	},
	raw: {
		type: Boolean,
		default: true,
	},
});

const emit = defineEmits(['update:modelValue', 'blur']);
const inputRef = ref(null);
let cleaveInstance: any = null;
let onValueChangedFn: Function | null = null;

const initializeCleave = () => {
	if (cleaveInstance) return;

	cleaveInstance = new Cleave(inputRef.value.$el.querySelector('input'), getOptions(props.options));
};

const getOptions = (options: any) => {
	// Preserve original callback
	onValueChangedFn = options.onValueChanged;

	return Object.assign({}, options, {
		onValueChanged: handleValueChange,
	});
};

const handleValueChange = (event: any) => {
	const value = props.raw ? event.target.rawValue : event.target.value;
	emit('update:modelValue', value);

	// Call original callback method
	if (typeof onValueChangedFn === 'function') {
		onValueChangedFn.call(null, event);
	}
};

watch(() => props.modelValue, (newValue) => {
	if (!cleaveInstance) return;

	if (props.raw && newValue === cleaveInstance.getRawValue()) return;
	if (!props.raw && newValue === inputRef.value.$el.value) return;

	cleaveInstance.setRawValue(newValue);
});

watch(() => props.options, (newOptions) => {
	if (cleaveInstance) {
		cleaveInstance.destroy();
		cleaveInstance = new Cleave(inputRef.value.$el.querySelector('input'), getOptions(newOptions));
		cleaveInstance.setRawValue(props.modelValue);
	}
}, { deep: true });

onMounted(() => {
	initializeCleave();
});

onBeforeUnmount(() => {
	if (cleaveInstance) {
		cleaveInstance.destroy();
		cleaveInstance = null;
	}
});

const handleBlur = () => {
	emit('blur', props.modelValue);
};
</script>
