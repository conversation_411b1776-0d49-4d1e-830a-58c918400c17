<template>
	<input
		class="Cleave2"
		ref="el"
		type="text"
		:value="formattedValue"
		@blur="onBlur"
		@keydown.enter="onEnter"
	/>
</template>

<script setup lang="ts">
import Cleave from "cleave.js"
import { ref, onMounted, watch, onBeforeUnmount, computed } from "vue"

interface Props {
	modelValue: string | number | null
	options?: Cleave.Options
	raw?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	modelValue: null,
	options: () => ({
		numeral: true,
		numeralThousandsGroupStyle: "thousand",
		numeralDecimalScale: 2,
		numeralPositiveOnly: true
	}),
	raw: true
})

const emit = defineEmits<{
	(event: "update:modelValue", value: string): void
	(event: "blur", value: string | number | null): void
}>()

const cleave = ref<Cleave | null>(null)
let onValueChangedFn: Function | null = null

const el = ref<HTMLInputElement | null>(null)

onMounted(() => {
	cleave.value = new Cleave(el.value!, getOptions(props.options))
})

function getOptions(options: Cleave.Options) {
	onValueChangedFn = options.onValueChanged

	return {
		...options,
		onValueChanged: onValueChanged
	}
}

function onValueChanged(event: any) {
	const value = props.raw ? event.target.rawValue : event.target.value
	emit("update:modelValue", value)

	if (typeof onValueChangedFn === "function") {
		onValueChangedFn.call(null, event)
	}
}

function onBlur() {
	if (cleave.value) {
		emit("update:modelValue", cleave.value.getRawValue())
		emit("blur", cleave.value.getRawValue())
	}
}

function onEnter(event: KeyboardEvent) {
	if (event.key === "Enter") {
		onBlur()
	}
}

const formattedValue = computed(() => {
	if (cleave.value) {
		return cleave.value.getFormattedValue()
	}
	return props.modelValue
})

watch(
	() => props.options,
	(newOptions) => {
		cleave.value?.destroy()
		cleave.value = new Cleave(el.value!, getOptions(newOptions))
		cleave.value.setRawValue(props.modelValue)
	},
	{ deep: true }
)

watch(
	() => props.modelValue,
	(newValue) => {
		if (!cleave.value) return

		if (props.raw && newValue === cleave.value.getRawValue()) return
		if (!props.raw && newValue === el.value?.value) return

		cleave.value.setRawValue(newValue)
	}
)

onBeforeUnmount(() => {
	cleave.value?.destroy()
	cleave.value = null
	onValueChangedFn = null
})
</script>

<style scoped>
.Cleave2 {
	background-color: black;
	color: white;
}
</style>
