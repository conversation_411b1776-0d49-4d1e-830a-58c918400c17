<template>
	<VueCleaveComponent
		ref="input"
		class="NumberInput"
		:class="classComputed"
		:style="{ 'font-size': fontSize + 'px', height: height + 'px' }"
		v-model="valueProxy"
		:raw="false"
		:options="cleaveOptions"
		:disabled="disabled || output"
		@blur="onBlur"
	/>
</template>

<script setup lang="ts">
import { computed, onMounted, onBeforeUnmount, ref } from 'vue';
import VueCleaveComponent from 'vue-cleave-component';
import { forceDecimals } from "@/au24/utils"

const props = defineProps({
	size: {
		type: String,
		default: 'small',
	},
	decimalPlaces: {
		type: [Number, String],
		default: undefined,
	},
	disabled: {
		type: Boolean,
		default: false,
	},
	output: {
		type: Boolean,
		default: false,
	},
	fontSize: {
		type: Number,
		default: 13,
	},
	height: {
		type: Number,
		default: 30,
	},
	value: {
		type: [String, Number],
		default: undefined,
	},
});

const emit = defineEmits(['input', 'blur', 'enter', 'escape']);

const input = ref<VueCleaveComponent | null>(null);

const valueProxy = computed({
	get: () => (props.value === null ? '' : props.value),
	set: (value: string) => {
		if (valueProxy.value === value) {
			return;
		}
		emit('input', value);
	},
});

const classComputed = computed(() => ({
	'au-input--output': props.output,
	'au-input--disabled': props.disabled,
}));

const cleaveOptions = computed(() => ({
	numeral: true,
	numeralThousandsGroupStyle: 'thousand',
	numeralDecimalScale: props.decimalPlaces || 0,
	numeralPositiveOnly: true,
	//swapHiddenInput: true,
}));

const onBlur = () => {
	emit('blur');
	const result = forceDecimals(valueProxy.value, props.decimalPlaces);
	valueProxy.value = result;
};

const onKeyup = (event: KeyboardEvent) => {
	if (event.code === 'Enter') {
		emit('enter');
	}
	if (event.code === 'Escape') {
		emit('escape');
	}
};

onMounted(() => {
	input.value?.$el.addEventListener('keyup', onKeyup);
});

onBeforeUnmount(() => {
	input.value?.$el.removeEventListener('keyup', onKeyup);
});

const select = () => {
	input.value?.$el.select();
};

const focus = () => {
	input.value?.$el.focus();
};

const blur = () => {
	input.value?.$el.blur();
};
</script>

<style lang="less" scoped>
.NumberInput {
	background-color: black;
	color: white;
	border-radius: 4px;
	padding: 0 5px;
	text-align: right;

	// That's for IE11:
	line-height: 0.6;
}
</style>
