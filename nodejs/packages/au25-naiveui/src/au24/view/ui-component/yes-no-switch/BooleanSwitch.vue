<template>
	<n-switch v-model="model">
		<template #checked>
			{{ checkedLabel }}
		</template>
		<template #unchecked>
			{{ uncheckedLabel }}
		</template>
	</n-switch>
</template>

<script setup lang="ts">
import { NSwitch } from "naive-ui"

const props = defineProps({
	checkedLabel: {
		type: String,
		default: "yes"
	},
	uncheckedLabel: {
		type: String,
		default: "no"
	}
})

const model = defineModel()
</script>
