<template>
	<div class="AuAgGridCenteredHeader">
		{{ displayName }}
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"

const params = ref<any>(null) // Specify the appropriate type if known

const displayName = computed(() => (params.value ? params.value.displayName : ""))

function refresh(params: any): boolean {
	// Specify the appropriate type for params if known
	return true
}
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.AuAgGridCenteredHeader {
	text-align: center;
	width: 100%;
	white-space: normal;
}
</style>
