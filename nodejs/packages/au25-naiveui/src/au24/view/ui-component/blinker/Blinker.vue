<template>
	<div
		class="Blinker"
		:class="{
			'Blinker--background': background,
			'Blinker--active': isBlinking
		}"
		@click="handleClick"
	>
		<slot>{{ value }}</slot>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from "vue"

const BLINK_TIMEOUT = 400
const props = defineProps<{
	value: any
	background?: boolean
	disabled?: boolean
	initial?: boolean
}>()

const emit = defineEmits(["click"])

const isBlinking = ref(false)

watch(
	() => props.value,
	(newValue, oldValue) => {
		if (newValue !== oldValue) {
			blink()
		}
	},
	{ deep: true }
)

onMounted(() => {
	if (props.initial) {
		blink()
	}
})

function blink() {
	if (props.disabled) {
		return
	}
	isBlinking.value = true
	setTimeout(() => {
		isBlinking.value = false
	}, BLINK_TIMEOUT)
}

function handleClick() {
	emit("click")
}
</script>

<style lang="less">
// this was copied from: global-style.less
// TODO: should we import that file?
// - but why wasn't it imported already??
.Blinker {
	transition:
		color,
		background-color 0.3s ease-out;

	&--background {
		&.Blinker--active {
			background-color: rgba(255, 0, 0, 0.2);
		}
	}

	&--active {
		color: red;
	}
}
</style>
