<template>
	<VbDemo>
		<VbCard title="default">
			<Blinker :value="value">{{ value }}</Blinker>
		</VbCard>
		<VbCard title="background">
			<Blinker :value="value" background>{{ value }}</Blinker>
		</VbCard>
		<VbCard title="disabled">
			<Blinker :value="value" disabled>{{ value }}</Blinker>
		</VbCard>
		<VbCard title="initial">
			<Blinker :value="value" initial>{{ value }}</Blinker>
		</VbCard>
		<VbCard>
			<button @click="refresh">Change value</button>
		</VbCard>
	</VbDemo>
</template>

<script setup lang="ts">
import { ref } from "vue"
import Blinker from "./Blinker.vue"
import VbCard from "@/helpers/vuebook/VbCard.vue"
import VbDemo from "@/helpers/vuebook/VbDemo.vue"

const value = ref(Math.floor(Math.random() * 100000))

function refresh() {
	value.value = Math.floor(Math.random() * 100000)
}
</script>
