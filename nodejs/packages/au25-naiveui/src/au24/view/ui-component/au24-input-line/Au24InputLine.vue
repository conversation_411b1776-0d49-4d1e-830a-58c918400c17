<template>
	<n-form-item label="Quantity decrement:" path="quantityDecrement">
		<au24-number-input
			v-if="isNumber"
			class="input"
			v-model:value="settings.quantityDecrement"
			:suffix="settings.quantityLabel"
		/>
		<NInput v-else />
	</n-form-item>
</template>

<script setup lang="ts">
import { defineProps } from "vue"
import { NFormItem } from "naive-ui"
import Au24NumberInput from "@/au24/view/ui-component/au24-number-input/Au24NumberInput.vue"

const { isNumber = false } = defineProps<{
	isNumber?: boolean
}>()
</script>

<style scoped></style>
