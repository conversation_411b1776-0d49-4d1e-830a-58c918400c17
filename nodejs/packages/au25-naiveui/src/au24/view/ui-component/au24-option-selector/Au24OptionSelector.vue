<template>
	<n-radio-group :disabled="disabled" :class="groupClass" v-model:value="valueProxy">
		<n-radio v-for="option in options" :key="option.value" :value="option.value" :style="radioStyle">
			{{ option.label }}
		</n-radio>
	</n-radio-group>
</template>

<script setup lang="ts">
import { computed } from "vue"

const props = defineProps<{
	value: any
	options: { value: any; label: string }[]
	disabled?: boolean
	groupClass?: string
}>()

const emit = defineEmits(["update:value"])

const radioStyle = {
	display: "block",
	height: "30px",
	lineHeight: "30px"
}

const valueProxy = computed({
	get: () => props.value,
	set: (value: any) => {
		emit("update:value", value)
	}
})
</script>

<style lang="less" scoped>
/* Add your styles here */
</style>
