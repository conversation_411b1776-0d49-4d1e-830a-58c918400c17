<template>
	<AuBaseTable
		v-if="users.length"
		class="UserTable"
		:height="height"
		:width="width"
		:columnDefs="columnDefs"
		:rowData="users"
		:getRowHeight="() => row_height"
		:gridOptions="gridOptions"
		@row-selected="onRowSelected"
	/>
</template>

<script setup lang="ts">
import { computed, PropType, ref } from "vue"
import { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import { type UserElement } from "@/au24/types/generated.js"
import { getAuUserRoleName } from "@/au24/helpers/entity-helpers/AuUserRole.ts"
import { userController } from "@/au24/commands/outgoing"

const props = defineProps({
	users: {
		type: Array as PropType<UserElement[]>,
		required: true
	},
	height: {
		type: Number,
		required: true
	},
	width: {
		type: Number,
		required: true
	},
	onUserSelected: {
		// This allows the parent to pass in a stub:
		type: Function as PropType<(user: UserElement) => void>,
		default: userController.user_selected
	}
})

const row_height = 24

/*
TODO: Note it seems that either of these work:
1) set 		@row-selected="onRowSelected" on the grid and then have this function:
	const onRowSelected = (row: any) => {
		if (row.node.isSelected()) {
			userController.user_selected(row)
		}
	}
2) don't set @row-selected on the grid and instead add that function to the gridOptions object:
	const gridOptions = ref<GridOptions>({
		onRowSelected: (row: any) => {
			if (row.node.isSelected()) {
				userController.user_selected(row)
			}
		}
	})
 */

const gridOptions = ref<GridOptions>({
	headerHeight: 28,
	defaultColDef: {
		cellStyle: () => ({ padding: "0", border: "0" })
	},
	rowSelection: "single",
	suppressHorizontalScroll: true
	// onRowSelected: (row: any) => {
	// 	if (row.node.isSelected()) {
	// 		userController.user_selected(row)
	// 	}
	// }
})

const onRowSelected = (row: any) => {
	//console.log({ row })
	if (row.node.isSelected()) {
		props.onUserSelected(row.data)
	}
}

// const emit = defineEmits<{
// 	(e: "selected", user: UserElement): void;
// }>()

const getComparator = (valueGetter: (user: UserElement) => string) => {
	return (valueA: any, valueB: any, nodeA: any, nodeB: any, isInverted: boolean) => {
		const a: UserElement = nodeA.data
		const b: UserElement = nodeB.data
		return valueGetter(a).localeCompare(valueGetter(b))
	}
}

const columnDefs = computed<ColDef[]>(() => [
	{
		headerName: "Username",
		cellRenderer: (params: ICellRendererParams) => {
			const user: UserElement = params.data
			return `<div>${user.username}</div>`
		},
		sortable: true,
		comparator: getComparator(user => user.username),
		sort: "asc",
		width: 150
	},
	{
		headerName: "Company",
		cellRenderer: (params: ICellRendererParams) => {
			const user: UserElement = params.data
			return `<div>${user.company_longname}</div>`
		},
		sortable: true,
		comparator: getComparator(user => user.company_longname)
	},
	{
		headerName: "Role",
		cellRenderer: (params: ICellRendererParams) => {
			const user: UserElement = params.data
			return `<div>${getAuUserRoleName(user.role)}</div>`
		},
		sortable: true,
		comparator: getComparator(user => getAuUserRoleName(user.role)),
		width: 100
	}
])
</script>

<style lang="less" scoped>
.UserTable {
	:deep(.ag-cell) {
		margin: 0;
		padding: 0;
		border: 0;
	}
}
</style>
