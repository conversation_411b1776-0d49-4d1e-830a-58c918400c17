<template>
	<div>
		<div>Selected User: {{ selectedUser && selectedUser.username }}</div>
		<UserTable :users="users" :height="500" :width="500" :on-user-selected="onUserSelected"></UserTable>
		<pre>companies = {{ users }}</pre>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import UserTable from "@/au24/view/tables/au24-user-table/.old/UserTable.vue"
import { createDemo__UserElement_for_trader } from "@/au24/helpers/demo-helpers/UserElement.helper.ts"
import _ from "lodash"
import { type UserElement } from "@/au24/types/generated.js"

const users = _.times(30, createDemo__UserElement_for_trader)

const selectedUser = ref<UserElement | null>(null)

const onUserSelected = (user: UserElement) => {
	selectedUser.value = user
}
</script>

<style scoped></style>
