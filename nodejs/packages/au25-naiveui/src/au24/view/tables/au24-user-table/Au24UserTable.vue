<template>
	<Au24BaseTable :columns="columnDefs"
				   :data="users"
				   :height="200"
				   @rowClick="handleRowClick"></Au24BaseTable>
</template>

<script setup lang="ts">
import { computed, h, PropType } from "vue"
import { DataTableColumns, NButton, NIcon, NSpace } from "naive-ui"
import { getAuUserRoleName } from "@/au24/helpers/entity-helpers/AuUserRole"
import Au24BaseTable from "@/au24/view/tables/au24-base-table/Au24BaseTable.vue"
import { UserElement } from "@/au24/types/generated"
import { userController } from "@/au24/commands/outgoing"
import { PencilOutline, TrashOutline } from "@vicons/ionicons5"

const props = defineProps({
	users: {
		type: Array as PropType<UserElement[]>,
		required: true
	},
	height: {
		type: Number,
		required: true
	},
	width: {
		type: Number,
		required: true
	},
	onUserSelected: {
		// This allows the parent to pass in a stub:
		type: Function as PropType<(user: UserElement) => void>,
		default: userController.user_selected
	},
	onUserDelete: {
		// This allows the parent to pass in a stub:
		type: Function as PropType<(user: UserElement) => void>,
		default: userController.user_delete
	}
})

const columnDefs = computed<DataTableColumns<UserElement>>(() => [
	// {
	// 	type: 'selection',
	// 	disabled(row: UserElement) {
	// 		return row.age > 30
	// 	}
	// },
	{
		title: "Username",
		key: "username",
		defaultSortOrder: "ascend",
		sorter: "default",
		width: 150
	},
	{
		title: "Company",
		key: "company_longname",
		defaultSortOrder: "ascend",
		sorter: "default"
	},
	{
		title: "Role",
		key: "role",
		width: 150,
		defaultSortOrder: "ascend",
		sorter: "default",
		render: row => getAuUserRoleName(row.role)
	},
	{
		title: "Actions",
		key: "actions",
		render: (row: UserElement) => {
			return h(
				NSpace,
				{},
				{
					default: () => [
						h(
							NButton,
							{
								circle: true,
								onClick: (e) => {
									e.stopPropagation()
									props.onUserSelected(row)
								}
							},
							{
								icon: () =>
									h(NIcon, null, {
										default: () => h(PencilOutline)
									})
							}
						),
						h(
							NButton,
							{
								circle: true,
								onClick: (e) => {
									e.stopPropagation()
									props.onUserDelete(row)
								}
							},
							{
								icon: () =>
									h(NIcon, null, {
										default: () => h(TrashOutline)
									})
							}
						)
					]
				}
			)
		}
	}
])

const handleRowClick = (row: UserElement) => {
	//console.log('handleRowClick', { row })
	props.onUserSelected(row)
}
</script>

<style scoped></style>
