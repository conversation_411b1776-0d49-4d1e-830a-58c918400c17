<template>
	<Au24UserTable style="width: 95%" :users="users" :height="500" :width="500"></Au24UserTable>
</template>

<script setup lang="ts">
import { createDemo__UserElement_for_trader } from "@/au24/helpers/demo-helpers/UserElement.helper.ts"
import _ from "lodash"
import Au24UserTable from "@/au24/view/tables/au24-user-table/Au24UserTable.vue"

const users = _.times(30, createDemo__UserElement_for_trader)

// const selectedUser = defineModel<UserElement>()
//
// const onUserSelected = (user: UserElement) => {
// 	selectedUser.value = user
// }
</script>

<style scoped></style>
