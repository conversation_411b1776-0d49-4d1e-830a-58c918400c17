<template>
	<div>
		<div>Selected companies: {{ selectedCompanies }}</div>
		<Au24TraderSelectionTable
			:companies="companies"
			:height="tableHeight"
			:width="tableWidth"
			v-model="selectedCompanies"
		></Au24TraderSelectionTable>
		<pre>companies = {{ companies }}</pre>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue"
import { type CompanyElement } from "@/au24/types/generated.js"
import { times } from "lodash"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper.ts"
import Au24TraderSelectionTable from "@/au24/view/tables/au24-trader-select-table/Au24TraderSelectionTable.vue"

const companies = ref<Array<CompanyElement>>(times(30, createDemo__CompanyElement))
const tableHeight = 400
const tableWidth = 400

const selectedCompanies = ref<string[]>([])

watch(
	() => selectedCompanies.value,
	newSelectedCompanies => {
		console.log("Selected Companies:", newSelectedCompanies)
	}
)
</script>
