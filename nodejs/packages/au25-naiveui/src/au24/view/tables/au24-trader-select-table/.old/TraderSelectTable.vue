<template>
	<div>
		<AuBaseTable
			class="TraderSelectTable ag-theme-alpine"
			:columnDefs="columnDefs"
			:rowData="companies"
			:gridOptions="gridOptions"
			:height="height"
			:width="width"
			:rowSelection="'multiple'"
			@grid-ready="onGridReady"
			@selection-changed="onSelectionChanged"
			@row-clicked="onRowClicked"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { ColDef, GridOptions, ICellRendererParams, RowClickedEvent } from "ag-grid-community"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import { type CompanyElement } from "@/au24/types/generated.js"

/*
a nice thing about emits is that they can be thrown to parents of parents, etc.
so, we don't need to emit @grid-ready, or @row-clicked, etc
 */

/*
See TraderSelectTable for how to handle checkboxes
- and also how to enable clicking on the entire row to toggle the checkbox
- basically you have to both:
  - a) 	suppressRowClickSelection: true on the gridOptions
     - this is because clicking on the row will select only that row
     - and clear all other selections
  - b) set that row's isSelected property:
   function onRowClicked(event: RowClickedEvent) {
	if (gridApi) {
		event.node.setSelected(!node.isSelected()); // Toggle the row selection
	}
}

 */

const props = defineProps<{
	companies: CompanyElement[]
	height: number
	width: number
}>()

// we'll define an expose to get the selected companies.
// ie: no need for proxies, proxyIds, or emits!
const selectedCompanies = ref<CompanyElement[]>([])
defineExpose({
	getSelectedCompanies: () => selectedCompanies.value
})

const gridOptions: GridOptions = {
	headerHeight: 28,
	// defaultColDef: {
	// 	cellStyle: () => ({ padding: '0', border: '0' }),
	// },
	rowHeight: 24,
	suppressHorizontalScroll: true,
	rowSelection: "multiple",
	suppressRowClickSelection: true // Disable the default row selection behavior
}

function onRowClicked(event: RowClickedEvent) {
	if (gridApi) {
		event.node.setSelected(!node.isSelected()) // Toggle the row selection
	}
}

const gridApi = ref()

const onGridReady = params => {
	gridApi.value = params.api
	gridApi.value.sizeColumnsToFit()
}

const columnDefs = computed<ColDef[]>(() => [
	{
		headerName: "",
		width: 50,
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true
	},
	{
		headerName: "Company short name",
		sortable: true,
		comparator: makeComparatorForString(company => company.company_shortname),
		cellRenderer: (params: ICellRendererParams) => {
			const company: CompanyElement = params.data
			return `<div style="padding: 3px 5px;">${company.company_shortname}</div>`
		}
	},
	{
		headerName: "Company long name",
		sortable: true,
		comparator: makeComparatorForString(company => company.company_longname),
		cellRenderer: (params: ICellRendererParams) => {
			const company: CompanyElement = params.data
			return `<div style="padding: 3px 5px;">${company.company_longname}</div>`
		}
	}
])

function onSelectionChanged() {
	if (gridApi.value) {
		selectedCompanies.value = gridApi.value.getSelectedRows()
	}
}

const makeComparatorForString = (valueGetter: (company: CompanyElement) => string) => {
	return (valueA, valueB, nodeA, nodeB) => {
		const a: CompanyElement = nodeA.data
		const b: CompanyElement = nodeB.data
		return valueGetter(a).localeCompare(valueGetter(b))
	}
}
</script>

<style lang="less" scoped>
.TraderSelectTable {
	.ag-header-cell {
		padding: 3px 5px !important;
	}
}
</style>
