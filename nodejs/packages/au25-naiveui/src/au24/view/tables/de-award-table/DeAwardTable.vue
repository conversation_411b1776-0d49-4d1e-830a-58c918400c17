<!-- {'model': 'claude-3-sonnet-20240229', 'input_tokens': 3124, 'output_tokens': 1013, 'input_cost': '$0.009372', 'output_cost': '$0.015195', 'total_cost': '$0.024567'} -->
<template>
	<div class="DeAwardTable">
		<n-radio-group class="_radio" v-model:value="filter">
			<n-radio-button value="all">All</n-radio-button>
			<n-radio-button value="sellers">Sellers</n-radio-button>
			<n-radio-button value="buyers">Buyers</n-radio-button>
		</n-radio-group>
		<AuBaseTable
			:height="height"
			:width="width"
			:grid-options="grid_options"
			:column-defs="column_defs"
			:get-row-height="null"
			:row-data="row_data"
		/>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { ColDef, GridOptions } from "ag-grid-community"
import { type DeMatrixRoundElement, OrderType } from "@/au24/types/generated.js"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import DeAwardRow from "@/au24/view/tables/de-award-table/DeAwardRow.vue"
import AuAgGridCenteredHeader from "@/au24/view/ui-component/AuAgGridCenteredHeader.vue"
import { counterparties_to_rows, DeAwardRowModel, matrix_to_trader_counterparties } from "./DeAwardRowModel.ts"

const props = defineProps({
	matrix: {
		type: Object as () => DeMatrixRoundElement,
		required: true
	},
	height: {
		type: Number,
		required: true
	}
})

const width = 660
const filter = ref<"all" | "sellers" | "buyers">("all")

const grid_options: GridOptions = {
	defaultColDef: {
		cellRenderer: DeAwardRow,
		headerComponent: AuAgGridCenteredHeader
	},
	animateRows: false
}

const column_defs: ColDef[] = [
	{
		headerName: "Trader",
		width: 100
	},
	{
		headerName: "Side",
		width: 80
	},
	{
		headerName: "Counterparty",
		width: 160
	},
	{
		headerName: "Quantity",
		width: 80
	},
	{
		headerName: "Value",
		width: 120
	},
	{
		headerName: "Credit limit",
		width: 120
	}
]

const row_data = computed((): DeAwardRowModel[] => {
	const counterparties = matrix_to_trader_counterparties(props.matrix)
	const counterpartiesFiltered = counterparties.filter(counterparty => {
		if (filter.value === "all") {
			return true
		}
		if (filter.value === "sellers") {
			return counterparty.side === OrderType.SELL
		}
		if (filter.value === "buyers") {
			return counterparty.side === OrderType.BUY
		}
		return false
	})
	return counterparties_to_rows(counterpartiesFiltered)
})
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables";

.DeAwardTable {
	._radio {
		:deep(.n-radio-button-wrapper) {
			border-color: @au-primary-color !important;
			color: @au-text-color;
			background-color: @au-background-light;

			&.n-radio-button-wrapper--checked {
				color: @outputColor;
				background-color: @au-primary-color;
			}
		}

		:deep(.ag-cell-wrapper) {
			padding-right: 0;
		}

		margin-bottom: 1rem;
	}
}
</style>
