<template>
	<AwardTable :height="500" :matrix="matrix" />
</template>

<script setup lang="ts">
import AwardTable from "@/au24/view/tables/de-award-table/DeAwardTable.vue"

const matrix = {
	id: "Round.1",
	round_number: 1,
	nodes: [
		{
			id: "R_1_T_2425995",
			round: 1,
			cid: "2425995",
			shortname: "c-1",
			buy_match: 0,
			buy_max: 50,
			buy_min: 0,
			buy_vol: 0,
			sell_match: 32,
			sell_max: 50,
			sell_min: 0,
			sell_vol: 50
		},
		{
			id: "R_1_T_2425997",
			round: 1,
			cid: "2425997",
			shortname: "c-2",
			buy_match: 24,
			buy_max: 50,
			buy_min: 0,
			buy_vol: 12,
			sell_match: 12,
			sell_max: 50,
			sell_min: 0,
			sell_vol: 0
		},
		{
			id: "R_1_T_2425999",
			round: 1,
			cid: "2425999",
			shortname: "c-3",
			buy_match: 20,
			buy_max: 50,
			buy_min: 0,
			buy_vol: 20,
			sell_match: 0,
			sell_max: 50,
			sell_min: 0,
			sell_vol: 0
		},
		{
			id: "R_1_T_2426001",
			round: 1,
			cid: "2426001",
			shortname: "c-4",
			buy_match: 0,
			buy_max: 50,
			buy_min: 0,
			buy_vol: 0,
			sell_match: 0,
			sell_max: 50,
			sell_min: 0,
			sell_vol: 0
		}
	],
	edges: [
		{
			id: "R_1_B_2425997_S_2425995",
			r: 1,
			buyer_shortname: "c-2",
			buyer_cid: "2425997",
			seller_shortname: "c-1",
			seller_cid: "2425995",
			capacity: 0,
			match: 24,
			credit_str: "$0.00",
			round_credit_volume_limit: 0
		},
		{
			id: "R_1_B_2425999_S_2425995",
			r: 1,
			buyer_shortname: "c-3",
			buyer_cid: "2425999",
			seller_shortname: "c-1",
			seller_cid: "2425995",
			capacity: 3,
			match: 8,
			credit_str: "$3,200,000.00",
			round_credit_volume_limit: 3
		},
		{
			id: "R_1_B_2426001_S_2425995",
			r: 1,
			buyer_shortname: "c-4",
			buyer_cid: "2426001",
			seller_shortname: "c-1",
			seller_cid: "2425995",
			capacity: 2,
			match: 0,
			credit_str: "$2,200,000.00",
			round_credit_volume_limit: 2
		},
		{
			id: "R_1_B_2425995_S_2425997",
			r: 1,
			buyer_shortname: "c-1",
			buyer_cid: "2425995",
			seller_shortname: "c-2",
			seller_cid: "2425997",
			capacity: 5,
			match: 0,
			credit_str: "$5,800,000.00",
			round_credit_volume_limit: 5
		},
		{
			id: "R_1_B_2425999_S_2425997",
			r: 1,
			buyer_shortname: "c-3",
			buyer_cid: "2425999",
			seller_shortname: "c-2",
			seller_cid: "2425997",
			capacity: 9,
			match: 12,
			credit_str: "$9,000,000.00",
			round_credit_volume_limit: 9
		},
		{
			id: "R_1_B_2426001_S_2425997",
			r: 1,
			buyer_shortname: "c-4",
			buyer_cid: "2426001",
			seller_shortname: "c-2",
			seller_cid: "2425997",
			capacity: 1,
			match: 0,
			credit_str: "$1,200,000.00",
			round_credit_volume_limit: 1
		},
		{
			id: "R_1_B_2425995_S_2425999",
			r: 1,
			buyer_shortname: "c-1",
			buyer_cid: "2425995",
			seller_shortname: "c-3",
			seller_cid: "2425999",
			capacity: 2,
			match: 0,
			credit_str: "$2,600,000.00",
			round_credit_volume_limit: 2
		},
		{
			id: "R_1_B_2425997_S_2425999",
			r: 1,
			buyer_shortname: "c-2",
			buyer_cid: "2425997",
			seller_shortname: "c-3",
			seller_cid: "2425999",
			capacity: 2,
			match: 0,
			credit_str: "$2,100,000.00",
			round_credit_volume_limit: 2
		},
		{
			id: "R_1_B_2426001_S_2425999",
			r: 1,
			buyer_shortname: "c-4",
			buyer_cid: "2426001",
			seller_shortname: "c-3",
			seller_cid: "2425999",
			capacity: 4,
			match: 0,
			credit_str: "$4,300,000.00",
			round_credit_volume_limit: 4
		},
		{
			id: "R_1_B_2425995_S_2426001",
			r: 1,
			buyer_shortname: "c-1",
			buyer_cid: "2425995",
			seller_shortname: "c-4",
			seller_cid: "2426001",
			capacity: 3,
			match: 0,
			credit_str: "$3,600,000.00",
			round_credit_volume_limit: 3
		},
		{
			id: "R_1_B_2425997_S_2426001",
			r: 1,
			buyer_shortname: "c-2",
			buyer_cid: "2425997",
			seller_shortname: "c-4",
			seller_cid: "2426001",
			capacity: 9,
			match: 0,
			credit_str: "$9,700,000.00",
			round_credit_volume_limit: 9
		},
		{
			id: "R_1_B_2425999_S_2426001",
			r: 1,
			buyer_shortname: "c-3",
			buyer_cid: "2425999",
			seller_shortname: "c-4",
			seller_cid: "2426001",
			capacity: 2,
			match: 0,
			credit_str: "$2,500,000.00",
			round_credit_volume_limit: 2
		}
	]
}
</script>

<style scoped></style>
