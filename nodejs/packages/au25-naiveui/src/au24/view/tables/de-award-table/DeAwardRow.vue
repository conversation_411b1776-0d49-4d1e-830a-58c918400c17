<template>
	<div class="DeAwardRow" :style="{ color: computedColor }">
		<div v-if="params.colDef.headerName === 'Trader'" class="_trader">
			{{ row.trader_name }}
		</div>

		<div v-if="params.colDef.headerName === 'Side'" class="_side">
			{{ row.side_str }}
		</div>

		<div v-if="params.colDef.headerName === 'Quantity'" class="_quantity">
			<div style="width: 40px; margin-left: 10px; text-align: right">
				{{ row.quantity }}
			</div>
		</div>

		<div v-if="params.colDef.headerName === 'Counterparty'" class="_trader">
			<div v-if="row.counterparty_name">
				<div class="_counterparty" v-if="row.side === OrderType.BUY && row.counterparty_name">from:</div>
				<div class="_counterparty" v-if="row.side === OrderType.SELL && row.counterparty_name">to:</div>
				{{ row.counterparty_name }}
			</div>
		</div>

		<div v-if="params.colDef.headerName === 'Value'" class="_value">
			<div style="width: 98px; text-align: right">
				{{ row.value }}
			</div>
		</div>

		<div v-if="params.colDef.headerName === 'Credit limit'" class="_credit">
			<div style="width: 98px; text-align: right">
				{{ row.credit_limit }}
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { OrderType } from "@/au24/types/generated.js"
import { DeAwardRowModel } from "./DeAwardRowModel"
import { auColors, AuColors } from "@/au24/au-styles/AuColors"
import { computed } from "vue"

const props = defineProps<{
	params: any
}>()

const colors: AuColors = auColors

const computedColor = computed(() => colors.order_quantity_text_color(props.params.data.side))

const row = computed(() => props.params.data as DeAwardRowModel)
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables";

.DeAwardRow {
	//border: 1px solid red;
	overflow: hidden;

	//position: relative;

	._cell {
		display: inline-block;
	}

	._counterparty {
		display: inline-block;
		margin-right: 5px;
		text-align: right;
		width: 25px;
	}

	//._counterparty_heading {
	//  //    border: 1px solid yellow;
	//  color: hsl(200, 10%, 60%);
	//  font-size: 10px;
	//  font-weight: bold;
	//  line-height: 1em;
	//  margin: 0;
	//  padding: 0;
	//}

	//._row {
	//  border: 1px solid yellow;
	//  color: hsl(0, 0%, 80%);
	//  font-size: 11px;
	//  height: 15px;
	//  line-height: 1.5em;
	//  overflow: hidden;
	//  padding: 0;
	//  position: relative;
	//  top: 0;
	//
	//  &:nth-child(odd) {
	//    background-color: hsl(0, 0%, 18%);
	//  }
	//
	//  &:nth-child(even) {
	//    background-color: hsl(0, 0%, 14%);
	//  }
	//}

	._trader {
		//    border: 1px solid red;
		display: inline-block;
	}

	._side {
		//  border: 1px solid yellow;
		display: inline-block;
		text-align: center;
		width: 75px;
	}

	._quantity {
		display: inline-block;
		//border: 1px solid green;
		text-align: center;
	}

	._value {
		display: inline-block;
		//border: 1px solid blue;
		text-align: center;
	}

	._credit {
		//border: 1px solid pink;
		display: inline-block;
		text-align: center;
	}
}
</style>
