<template>
	<div>
		<BooleanSwitch v-model:value="isAuctioneer" label="Is Auctioneer"></BooleanSwitch>
		<div>
			<NButton @click="addAuction">Add auction</NButton>
		</div>
		<AuctionTable :auctions="auctionRowElements" :isAuctioneer="isAuctioneer"></AuctionTable>
		<NDataTable
			class="auction-table"
			:columns="createColumns"
			:data="auctionRowElements"
			:bordered="true"
			:bottom-bordered="true"
			:single-column="true"
			:max-height="100"
			:min-height="100"
		></NDataTable>
	</div>
</template>

<script setup lang="ts">
import AuctionTable from "@/au24/view/tables/au24-auction-table/AuctionTable.vue"
import { computed, h, onMounted, ref, VNodeChild } from "vue"
import { createMultipleByClosure } from "@/au24/utils"
import { createDemo__AuctionRowElement } from "@/au24/helpers/demo-helpers/AuctionRowElement.helper.ts"
import { DataTableColumns, NButton, NDataTable, NSpace } from "naive-ui"
import { AuctionRowElement } from "@/au24/types/generated.ts"
import BooleanSwitch from "@/au24/view/ui-component/yes-no-switch/BooleanSwitch.vue"

const isAuctioneer = ref(true)
const auctionRowElements = ref(
	createMultipleByClosure((id: number) => createDemo__AuctionRowElement(isAuctioneer.value, id), 1)
)
const count = ref(5)

onMounted(() => {
	const auction_one = auctionRowElements?.value[0]
	if (auction_one) {
		auction_one.auction_name =
			"loooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong"
	}
})

const play = (row: AuctionRowElement) => {
	// Implement the play functionality here
	console.log("Play", row)
}

const createColumns = computed(() => {
	const add_actions = isAuctioneer.value
	const columns: DataTableColumns<AuctionRowElement> = [
		{
			title: "Auction",
			key: "auction_name",
			minWidth: 200,
			fixed: "left"
		},
		{
			title: "Status",
			key: "auction_status",
			width: 100,
			fixed: "left"
		}
	]

	if (add_actions) {
		columns.push({
			title: "Actions",
			key: "address",
			width: 150,
			align: "center",
			render: row =>
				h(
					NSpace,
					{
						justify: "center"
					},
					{
						default: () => [
							h(
								NButton,
								{
									onClick: () => play(row),
									strong: true,
									secondary: true,
									size: "tiny"
								},
								{ default: () => "Play" }
							),
							h(
								NButton,
								{
									onClick: () => stop(row),
									strong: true,
									tertiary: true,
									size: "tiny"
								},
								{ default: () => "Stop" }
							)
						]
					}
				) as VNodeChild
		})
	}

	return columns
})

function addAuction() {
	auctionRowElements.value = [
		createDemo__AuctionRowElement(isAuctioneer.value, count.value++),
		...auctionRowElements.value
	]
}
</script>
<style scoped>
.auction-table {
	margin: 15px;
}
</style>
