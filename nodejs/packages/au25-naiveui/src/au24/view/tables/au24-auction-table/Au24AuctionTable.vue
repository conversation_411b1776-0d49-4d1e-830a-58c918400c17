<template>
	<Au24BaseTable
		class="auction-table"
		:striped="true"
		:columns="createColumns"
		:data="auctions"
		:height="height"
	></Au24BaseTable>
</template>

<script setup lang="ts">
import { computed, h } from "vue"
import { DataTableColumns } from "naive-ui"
import { AuctionRowElement } from "@/au24/types/generated"
import Au24AuctionTableRowRenderer from "@/au24/view/tables/au24-auction-table/Au24AuctionTableRowRenderer.vue"
import Au24BaseTable from "@/au24/view/tables/au24-base-table/Au24BaseTable.vue"



const props = defineProps({
	auctions: {
		type: Array<AuctionRowElement>, // Array as PropType<AuctionRowElement[]>,
		required: true
	},
	height: {
		type: Number | String,
		default: 300
	},
	width: {
		// not currently used
		type: Number | String,
		default: 990
	},
	isAuctioneer: {
		type: Boolean,
		default: false
	}
})

const createRenderer = (field: string | null, value?: string) => {
	return (rowData: any) =>
		h(Au24AuctionTableRowRenderer, {
			field,
			value: rowData[field] || value || ""
		})
}

const createColumns = computed(() => {
	const add_actions = props.isAuctioneer
	//console.log({add_actions})
	const columns: DataTableColumns<AuctionRowElement> = [
		{
			title: "Auction",
			key: "auction_name",
			minWidth: 200,
			render: createRenderer("auction_name")
		},
		{
			title: "Status",
			key: "common_state_text",
			width: 200,
			align: "center",
			fixed: "right",
			render: createRenderer("common_state_text")
		}
	]

	if (add_actions) {
		columns.push({
			title: "Actions",
			key: "actions",
			width: 150,
			align: "center",
			fixed: "right",
			render: createRenderer("actions")
		})
	}

	return columns
})

const rowProps = (item: any) => ({
	onClick: () => console.log("onClick", item) // emit('rowClick', item),
})

// function addAuction() {
// 	auctionRowElements.value = [
// 		createDemo__AuctionRowElement(props.isAuctioneer.value, count.value++),
// 		...auctions.value
// 	]
// }

// KEEP, this works
// render: (row) =>
// 	h(
// 		NSpace,
// 		{
// 			justify: 'center',
// 		},
// 		{
// 			default: () => [
// 				h(
// 					NButton,
// 					{
// 						onClick: () => play(row),
// 						strong: true,
// 						secondary: true,
// 						size: "tiny",
// 					},
// 					{ default: () => "Play" }
// 				),
// 				h(
// 					NButton,
// 					{
// 						onClick: () => stop(row),
// 						strong: true,
// 						tertiary: true,
// 						size: "tiny",
// 					},
// 					{ default: () => "Stop" }
// 				),
// 			],
// 		}
// 	) as VNodeChild,
</script>
<style scoped>
.auction-table {
	margin: 15px;
	width: 95%;
}
</style>
