<template>
	<div>
		<template v-if="field === 'actions'">
			<n-flex justify="center">
				<n-button @click="console.log" strong size="tiny">Hide</n-button>
				<n-button @click="console.log" strong size="tiny">Delete</n-button>
			</n-flex>
		</template>
		<template v-else-if="field === 'auction_name'">
			<!-- Template for 'age' field -->
			<span>{{ value }}</span>
		</template>
		<template v-else>
			<!-- Default template -->
			<span>{{ value }}</span>
		</template>
	</div>
	<!---->
</template>

<script setup lang="ts">
import { PropType } from "vue"

const props = defineProps({
	field: {
		type: String as PropType<string>,
		required: true
	},
	value: {
		type: [String, Number] as PropType<string | number>,
		required: true
	}
})
</script>
<style scoped></style>
