<template>
	<div>
		<BooleanSwitch v-model:value="isAuctioneer" label="Is Auctioneer"></BooleanSwitch>
		<div>
			<NButton @click="addAuction">Add auction</NButton>
		</div>
		<Au24AuctionTable :isAuctioneer="isAuctioneer" :auctions="auctionRowElements"></Au24AuctionTable>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue"
import { createMultipleByClosure } from "@/au24/utils"
import { createDemo__AuctionRowElement } from "@/au24/helpers/demo-helpers/AuctionRowElement.helper.ts"
import { NButton } from "naive-ui"
import BooleanSwitch from "@/au24/view/ui-component/yes-no-switch/BooleanSwitch.vue"
import Au24AuctionTable from "@/au24/view/tables/au24-auction-table/Au24AuctionTable.vue"

const isAuctioneer = ref(true)

const auctionRowElements = ref(
	createMultipleByClosure((id: number) => createDemo__AuctionRowElement(isAuctioneer.value, id), 1)
)

const count = ref(5)

onMounted(() => {
	const auction_one = auctionRowElements?.value[0]
	if (auction_one) {
		auction_one.auction_name =
			"loooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong" +
			"loooooooooooooooooooooooooooongloooooooooooooooooooooooooooong"
	}
})

function addAuction() {
	auctionRowElements.value = [
		createDemo__AuctionRowElement(isAuctioneer.value, count.value++),
		...auctionRowElements.value
	]
}
</script>
<style scoped>
.auction-table {
	margin: 15px;
	width: 95%;
}
</style>
