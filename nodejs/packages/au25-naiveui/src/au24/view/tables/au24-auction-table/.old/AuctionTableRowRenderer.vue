<template>
	<!--	<div class="_bottom_row w-full"-->
	<!--		 @click="auction_selected">	-->
	<div class="w-full">
		<div v-if="!auction" style="height: 24px; position: relative; top: -6px">NO AUCTION!</div>
		<div v-else @click="auctionController.auction_selected(auction)">
			<div class="_label" style="width: 450px">Start time:</div>
			<div class="_heading"></div>
			<div class="_label">Status:</div>
			<div class="_heading" style="width: 350px"></div>
			<div style="float: right; position: relative; display: inline-block; top: 6px">
				<NSpace justify="end">
					<span v-if="auction.isHidden">(hidden from traders)</span>
					<NButton
						v-if="is_auctioneer"
						size="tiny"
						class="mr-1"
						primary
						@click.stop="auctionController.toggleHidden(auction.auction_id)"
					>
						{{ auction.isHidden ? "Unhide" : "Hide" }}
					</NButton>
					<NButton
						v-if="is_auctioneer"
						size="tiny"
						primary
						@click.stop="auctionController.on_delete_click(auction.auction_id)"
					>
						Delete
					</NButton>
				</NSpace>
			</div>
			<div class="_row_text">
				{{ auction.auction_name }}
			</div>
		</div>
	</div>
</template>
<script setup lang="ts">
import { computed } from "vue"
import { AuctionRowElement } from "@/au24/types/generated.ts"
import { auctionController } from "@/au24/commands/outgoing"

const props = defineProps({
	params: {
		type: Object,
		required: true
	}
})

// console.log(object_keys((props.params.data)))
// console.log(props.params.data)
// console.log(props.params)

// Note: the params.data is an AuctionRowElement (or null)
const auction = computed<AuctionRowElement | null>(() => props.params.data ?? null)
// Note: is_auctioneer was added as a CellRendererParam, so it is available in the params itself
const is_auctioneer = computed<boolean>(() => props.params.isAuctioneer ?? false)

// const { app_width } = useAuScreen(is_auctioneer.value)
// const auction_col_width = computed(() => unref(app_width) - 60)
</script>
<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.AuctionTableRowRenderer {
	div:hover {
		background-color: hsl(0, 0%, 25%); //@au-background-light;
	}

	border-radius: 6px;
	font-size: 11px !important;
	height: 45px !important;
	margin: 5px;
	cursor: pointer;
	//&:hover {
	//  background-color: @au-background-light;
	//  //opacity: 0.1;
	//}

	._bottom_row {
		//border: 1px solid red;
		border: 0;
		border-radius: 5px;
		margin: 0;
		padding: 0 10px;
		overflow: hidden;
		// width: 100%;
		line-height: 1.5;

		&_text {
			white-space: normal;
			word-break: break-all;
		}
	}

	._label {
		color: @au-label-color; // hsl(77, 10%, 70%); //@au-label-color;
		display: inline-block;
		font-size: 12px;
		font-weight: bold;
		height: 20px;
		margin-right: 3px;
	}

	._heading {
		// border: 1px solid green;
		display: inline-block;
		height: 20px;
	}
}
</style>
