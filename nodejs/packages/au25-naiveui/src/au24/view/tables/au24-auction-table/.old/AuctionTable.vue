<template>
	<AuBaseTable
		:rowData="rows"
		:columnDefs="columnDefs"
		:getRowHeight="() => 90"
		:width="width"
		:height="table_height"
	></AuBaseTable>
</template>

<script setup lang="ts">
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import { computed } from "vue"
import { type AuctionRowElement } from "@/au24/types/generated.js"
import { ColDef } from "ag-grid-community"
import Au24AuctionTableRowRenderer from "./AuctionTableRowRenderer.vue"
import { WidthType } from "@/au24/plugins/screen-plugin/AuScreen.ts"
import { useWindowSize } from "@vueuse/core"

const props = defineProps({
	auctions: {
		type: Array<AuctionRowElement>, // Array as PropType<AuctionRowElement[]>,
		required: true
	},
	width: {
		type: Number | String,
		default: 990
	},
	isAuctioneer: {
		type: Boolean,
		default: false
	}
})

const { height } = useWindowSize()
const table_height = computed(() => height.value - 210)

const empty_row_count = computed(() => {
	const row_height = 70 // this seems to work even though the row only has a height of 55px.
	const empty_height = table_height.value - row_height * props.auctions.length
	return empty_height > 0 ? Math.floor(empty_height / row_height) : 0
})

const columnDefs = computed<ColDef[]>(() => [
	{
		colId: "0",
		headerName: "Auctions",
		flex: 1,
		cellRenderer: Au24AuctionTableRowRenderer,
		cellRendererParams: {
			//isAuctioneer: props.isAuctioneer
		}
	}
])

const rows = computed(() => {
	const rowsData = [...props.auctions]
	// range(empty_row_count.value + 3).forEach(index => {
	// 	rowsData.push({
	// 		id: "empty-row." + index,
	// 		auction_name: ""
	// 	} as AuctionRowElement)
	// })
	return rowsData
})
</script>
