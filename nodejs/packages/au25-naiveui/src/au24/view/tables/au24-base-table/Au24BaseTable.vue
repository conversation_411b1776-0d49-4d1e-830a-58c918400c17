<template>
	<NDataTable
		ref="Au24BaseTable"
		class="Au24BaseTable"
		:bordered="true"
		:bottom-bordered="true"
		:columns="columns"
		:data="data"
		:max-height="height"
		:min-height="height"
		:row-key="(row: T) => row.id"
		:row-props="mergedRowProps"
		:scrollbar-props="{ trigger: 'none' }"
		:single-column="true"
		:striped="true"
        :default-sort="defaultSort"
        @update:sorter="(sorter) => emit('update:sorter', sorter)"

    ></NDataTable>
</template>

<script setup lang="ts" generic="T extends { id: string }">
import { DataTableColumns, NDataTable } from "naive-ui"
import { computed, onMounted, ref } from "vue"
import {DataTableSortState} from "naive-ui/lib";

// Define the RowProps interface for better type safety
interface RowProps {
	style?: Record<string, string | number>;
	onClick?: () => void;
	// Add other properties as needed
}

// Define the component's props using destructuring and TypeScript generics
const props = defineProps<{
	data: T[];
	columns: DataTableColumns<T>;
	height: number;
	rowProps?: (row: T) => Partial<RowProps>;
    defaultSort?: DataTableSortState;
}>()

// Destructure props for easier access
const { data, columns, height, rowProps } = props

// Define the emits with TypeScript for better type safety
const emit = defineEmits<{
	(e: 'rowClick', row: T): void;
    (e: 'update:sorter', sorter: DataTableSortState | DataTableSortState[] | null): void;
}>()

// Define the default rowProps
const defaultRowProps = (row: T): RowProps => ({
	style: {
       cursor: 'pointer'
    },
	onClick: () => {
		emit('rowClick', row)
	}
})

// Compute the merged rowProps
const mergedRowProps = computed(() => {
	return (row: T): RowProps => {
		const customProps = rowProps?.(row) || {}
		const baseProps = defaultRowProps(row)

		// Merge styles, ensuring that customProps.style is an object
		const mergedStyle = {
			...baseProps.style,
			...(typeof customProps.style === 'object' ? customProps.style : {})
		}

		// Merge onClick handlers
		// TODO: do we need both click events??
		const mergedOnClick = () => {
			baseProps.onClick?.()
			customProps.onClick?.()
		}

		return {
			...baseProps,
			...customProps,
			style: mergedStyle,
			onClick: mergedOnClick
		}
	}
})
</script>
