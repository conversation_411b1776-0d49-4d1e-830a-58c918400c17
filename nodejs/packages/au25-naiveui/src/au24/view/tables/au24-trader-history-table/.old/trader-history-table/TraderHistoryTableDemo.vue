<template>
	<button @click="addRow()">Add row</button>
	<TraderHistoryTable
		:height="500"
		:width="590"
		:historyRows="trader_history_rows"
		:settings="settings"
	></TraderHistoryTable>
</template>

<script setup lang="ts">
import TraderHistoryTable from "@/au24/view/tables/au24-trader-history-table/.old/trader-history-table/TraderHistoryTable.vue"
import {
	createDemo__DeTraderHistoryRowElement,
	createDemo__DeTraderHistoryRowSequence
} from "@/au24/helpers/demo-helpers/DeTraderHistoryList.helper.ts"
import { ref } from "vue"
import { createDefault__DeSettingsValue } from "@/au24/helpers/demo-helpers/DeSettingsValue.helper.ts"

const settings = ref(createDefault__DeSettingsValue())
const trader_history_rows = ref(createDemo__DeTraderHistoryRowSequence(11))

const addRow = () => {
	trader_history_rows.value.push(createDemo__DeTraderHistoryRowElement(trader_history_rows.value.length + 1))
}
</script>

<style scoped></style>
