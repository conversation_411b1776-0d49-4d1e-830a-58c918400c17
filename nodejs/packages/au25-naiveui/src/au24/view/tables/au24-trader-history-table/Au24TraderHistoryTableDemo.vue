<template>
	<div style="margin: 10px">
		<NButton @click="addRow()">Add row</NButton>
		<Au24TraderHistoryTable
			:height="500"
			:width="590"
			:historyRows="trader_history_rows"
			:settings="settings"
		></Au24TraderHistoryTable>
	</div>
</template>

<script setup lang="ts">
import {
	createDemo__DeTraderHistoryRowElement,
	createDemo__DeTraderHistoryRowSequence
} from "@/au24/helpers/demo-helpers/DeTraderHistoryList.helper"
import { ref } from "vue"
import { createDefault__DeSettingsValue } from "@/au24/helpers/demo-helpers/DeSettingsValue.helper.ts"
import Au24TraderHistoryTable from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryTable.vue"

const settings = ref(createDefault__DeSettingsValue())
const trader_history_rows = ref(createDemo__DeTraderHistoryRowSequence(11))

const addRow = () => {
	trader_history_rows.value.push(createDemo__DeTraderHistoryRowElement(trader_history_rows.value.length + 1))
}
</script>
