<!-- Au24TraderHistoryRow.vue -->
<template>
	<div class="Au24TraderHistoryRow">
		<div v-if="field === 'round_number'" class="cell">
			<div class="_round">{{ rowData.round_number }}</div>
		</div>
		<div v-if="field === 'round_price'" class="cell">
			<div :style="{ color: priceColor }" class="_price">
				{{ rowData.round_price }}
			</div>
		</div>
		<div v-if="field === 'bid_constraints'" class="cell">
			<div class="_constraints">
				<n-tooltip
					:arrow-point-to-center="true"
					:arrow-style="{ border: `1px solid lightgoldenrodyellow` }"
					:disabled="bid_constraints === null"
					placement="bottom"
					style="border: 1px solid lightgoldenrodyellow"
					trigger="hover"
				>
					<template #trigger>
						<Au24OrderConstraintsBar
							:constraints="rowData.bid_constraints"
							:height="20"
							:order_quantity="orderQuantity"
							:order_type="rowData.order_type"
							:show_labels="false"
							:tick_font_size="9"
							:width="180"
						/>
					</template>
					<table style="text-align: left; font-size: 12px">
						<tbody>
							<tr>
								<td style="text-align: right">Buy range:</td>
								<td>
									{{ constraints_buy_range }}
								</td>
							</tr>
							<tr>
								<td style="text-align: right">Sell range:</td>
								<td>
									{{ constraints_sell_range }}
								</td>
							</tr>
							<tr>
								<td style="text-align: right">Order:</td>
								<td>
									{{ constraints_order }}
								</td>
							</tr>
						</tbody>
					</table>
				</n-tooltip>
			</div>
		</div>
		<div v-if="field === 'quantity'" class="cell">
			<div :style="{ color: priceColor }" class="_order">
				<div class="_order_type">{{ orderTypeLabel }}</div>
				<div class="_order_quantity">{{ rowData.quantity }}</div>
			</div>
		</div>
		<div v-if="field === 'value'" class="cell">
			<span class="_value" style="color: #ddd">{{ rowData.value }}</span>
		</div>
		<div v-if="field === 'order_submitted_by'" class="cell">
			<span class="_username" style="color: #ddd">{{ rowData.order_submitted_by }}</span>
		</div>
		<div v-if="field === 'excess_level'" class="cell">
			<span :style="{ color: excessLabelColor }" class="_excess-level">{{ rowData.excess_level }}</span>
			<span :style="{ color: excessLabelColor }" class="_excess-direction">{{ excessSide }}</span>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { computed, inject, ref } from "vue"
import { AuColors } from "@/au24/au-styles/AuColors.ts"
import Au24OrderConstraintsBar from "@/au24/view/widgets/constraints/Au24OrderConstraintsBar.vue"
import { DeTraderHistoryRowElement, OrderSubmissionType, OrderType } from "@/au24/types/generated.ts"

const props = defineProps<{
	field: keyof DeTraderHistoryRowElement
	rowData: DeTraderHistoryRowElement
}>()

const table = inject("n-data-table")

const colors = new AuColors()

const orderQuantity = computed(() => Number(props.rowData?.quantity) || 0)

const orderTypeLabel = computed(() => {
	switch (props.rowData?.order_type) {
		case OrderType.BUY:
			return "Buy"
		case OrderType.SELL:
			return "Sell"
		default:
			return "---"
	}
})

const excessLevel = computed(() => props.rowData?.excess_level)
const excessSide = computed(() => props.rowData?.excess_side)
const excessLabelColor = computed(() => colors.order_quantity_text_color(excessSide.value))
const priceColor = computed(() => colors.getPriceColor(props.rowData?.price_direction))

const bid_constraints = ref(props.rowData?.bid_constraints) // TODO: is this reactive?
const constraints_buy_range = computed(() => {
	const c = bid_constraints.value
	if (c == null) return ""
	if (c.max_buy_quantity == 0) return "Buy range: none"
	return `${c.min_buy_quantity} - ${c.max_buy_quantity}`
})

const constraints_sell_range = computed(() => {
	const c = bid_constraints.value
	if (c == null) return ""
	if (c.max_sell_quantity == 0) return "Sell range: none"
	return `${c.min_buy_quantity} - ${c.max_buy_quantity}`
})

const constraints_order = computed(() => {
	let s = ""
	switch (props.rowData.order_submission_type) {
		case OrderSubmissionType.MANUAL:
			break
		case OrderSubmissionType.MANDATORY:
			s = " (Mandatory)"
			break
		case OrderSubmissionType.DEFAULT:
			s = " (Default)"
			break
	}

	switch (props.rowData.order_type) {
		case OrderType.BUY:
			return `Buy ${props.rowData.quantity}` + s
		case OrderType.SELL:
			return `Sell ${props.rowData.quantity}` + s
		case OrderType.NONE:
			return "No Order" + s // TODO: not sure this makes sense
	}
})

// onMounted(() => {

// 	console.log(`Au24TraderHistoryRow mounted for field: ${props.field}, and round: ${props.rowData.round_number}`);
// });
</script>

<style lang="less" scoped>
.Au24TraderHistoryRow {
	font-size: 12px;
	margin: 0;
	padding: 0;
	width: 100%;

	.cell {
		align-items: center;
		//border: 1px solid red;
		display: flex;
		height: 100%;
		justify-content: center;
		overflow: hidden;
		text-align: center;
	}

	._round {
		//border: 1px solid blue;
		text-align: right;
		margin: 0;
		padding: 0;
		width: 30px;
	}

	._price {
		//border: 1px solid blue;
		text-align: right;
		margin: 0;
		padding: 0;
		width: 70px;
	}

	//._constraints {
	//	position: relative;
	//	text-align: center;
	//	top: 9px;
	//	left: -2px;
	//	width: 100%;
	//}

	._constraints {
		//border: 1px solid blue;
		//text-align: center;
		margin: 0;
		padding: 0;
		//width: 135px;
	}

	._order {
		overflow: hidden;
		text-align: right;
		padding-right: 2px;

		._order_type {
			display: inline-block;
			width: 35px;
		}

		._order_quantity {
			display: inline-block;
			text-align: right;
			width: 25px;
		}
	}

	._username {
		color: #ddd;
	}

	._excess-level {
		display: inline-block;
		left: -5px;
		position: relative;
		text-align: right;
		width: 30px;
	}

	._excess-direction {
		display: inline-block;
	}
}
</style>
