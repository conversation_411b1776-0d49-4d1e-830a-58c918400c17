<template>
    <Au24BaseTable ref="tableRef" :columns="columnDefs" :data="historyRows" :height="height"></Au24BaseTable>
</template>

<script lang="ts" setup>
import { h, nextTick, onMounted, ref, watch } from "vue"
import { DeSettingsValue, DeTraderHistoryRowElement } from "@/au24/types/generated"
import { Au24TraderHistoryParams } from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryParams"
import Au24TraderHistoryHeader from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryHeader.vue"
import Au24TraderHistoryRow from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryRow.vue"
import Au24BaseTable from "@/au24/view/tables/au24-base-table/Au24BaseTable.vue"
import { DataTableColumns, DataTableInst } from "naive-ui"
import { pretty } from "@/au24/utils"

const tableRef = ref<DataTableInst | null>(null)

const props = defineProps<{
    height: number
    historyRows: DeTraderHistoryRowElement[]
    settings: DeSettingsValue
}>()

const columnData: Array<{
    title: Au24TraderHistoryParams["headerName"]
    key: keyof DeTraderHistoryRowElement
    width: number
}> = [
    { title: "Round", key: "round_number", width: 60 },
    { title: "Price", key: "round_price", width: 75 },
    { title: "Constraints", key: "bid_constraints", width: 140 },
    { title: "Order", key: "quantity", width: 70 },
    { title: "Value", key: "value", width: 84 },
    { title: "Trader", key: "order_submitted_by", width: 85 },
    { title: "Excess", key: "excess_level", width: 70 }
]

// Track current sort order
const currentSortOrder = ref<'ascend' | 'descend'>('ascend')

const columnDefs: DataTableColumns<DeTraderHistoryRowElement> = columnData.map(({ title, key, width }) => ({
    title: () => h(Au24TraderHistoryHeader, { headerName: title }),
    key,
    width,
    ...(title === "Round" && {
        defaultSortOrder: "ascend",
        sorter: (rowA: DeTraderHistoryRowElement, rowB: DeTraderHistoryRowElement) => {
            const a = Number(rowA[key])
            const b = Number(rowB[key])
            return a - b || 1
        },
    }),
    render: (rowData: DeTraderHistoryRowElement) =>
            h(Au24TraderHistoryRow, {
                field: key,
                rowData
            })
}))

const scrollToLatestRound = () => {
    if (props.historyRows.length === 0) return

    // Get current sort order
    const isDescending = tableRef.value?.$el
            ?.querySelector('.n-data-table-sorter--desc') !== null

    // Find the row with the highest round number
    const maxRoundRow = [...props.historyRows].reduce((max, current) =>
            Number(current.round_number) > Number(max.round_number) ? current : max
    )

    // Find its index in the current sorted data
    const rowIndex = props.historyRows.findIndex(row => row.round_number === maxRoundRow.round_number)

    pretty({ maxRoundRow: maxRoundRow.round_number, rowIndex, isDescending })

    if (rowIndex !== -1) {
        const scrollContainer = tableRef.value?.$el
                ?.querySelector('.n-scrollbar-container') as HTMLElement

        if (scrollContainer) {
            const ROW_HEIGHT = 40
            const targetPosition = isDescending
                    ? 0  // In descending order, highest round is at top
                    : rowIndex * ROW_HEIGHT  // In ascending order, highest round is at bottom

            scrollContainer.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            })
        }
    }
}

// Watch for changes in historyRows
watch(
        () => props.historyRows,
        () => {
            nextTick(() => {
                scrollToLatestRound()
            })
        },
        { deep: true }
)

onMounted(() => {
    // Get the scrollable container
    const scrollContainer = tableRef.value?.$el
            ?.querySelector('.n-data-table-base-table-body') as HTMLElement

    if (scrollContainer) {
        // Now we can use the scrollContainer to scroll
        scrollContainer.scrollTo({
            top: 0,
            behavior: 'smooth'
        })
    }
})



// KEEP: example of how to create an inline component:
// NOTE: probably for the same amount of code in Au24HistoryRow, you could reimplement that with h()
// const MyActionComponent = defineComponent({
//   props: {
//     rowData: {
//       type: Object as PropType<DeTraderHistoryRowElement>,
//       required: true
//     }
//   },
//   setup(props) {
//     const handleClick = () => {
//       console.log("Clicked for row:", props.rowData)
//     }
//
//     return () => h(
//       NButton,
//       {
//         strong: true,
//         tertiary: true,
//         size: "small",
//         onClick: handleClick
//       },
//       { default: () => "Action" }
//     )
//   }
// })
</script>

<style scoped>
/* Style for header cells */
:deep(th.n-data-table-th) {
    font-size: 12px;
    margin: 0;
    overflow: hidden;
    padding: 0; /* Adjust the padding as needed */
    /*background-color: #f0f0f0; /* Set the background color for header cells */
    /*font-weight: bold; /* Make the header text bold */
    text-align: center; /* Center-align the header text */
    vertical-align: top;
}

/* Style for data cells */
:deep(td.n-data-table-td) {
    padding: 4px 0;
    overflow: hidden;
}
</style>
