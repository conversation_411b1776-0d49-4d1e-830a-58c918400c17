<template>
	<div class="Au24TraderHistoryHeader">
		<div>{{ top }}</div>
		<div style="font-size: 10px">{{ bottom }}</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { Au24TraderHistoryParams } from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryParams"

const props = defineProps<{
	headerName: Au24TraderHistoryParams["headerName"]
}>()

const top = computed(() => (props.headerName === "Value" ? "Cost" : props.headerName || ""))

const bottom = computed(() => {
	switch (props.headerName) {
		case "Round":
			return ""
		case "Price":
			return "(price label)"
		case "Order":
			return "(quantity label)"
		case "Constraints":
			return "(quantity label)"
		case "Excess":
			return "(indicator)"
		case "Trader":
			return ""
		default:
			return ""
	}
})
</script>

<style scoped>
.Au24TraderHistoryHeader {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 13px;
	padding: 4px 0;
	text-align: center;
	width: 100%;
	white-space: normal;
}
</style>
