<template>
	<AuBaseTable
		:height="height"
		:width="width"
		:row-data="historyRows"
		:column-defs="columnDefs"
		:grid-options="gridOptions"
	></AuBaseTable>
</template>

<script setup lang="ts">
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import { type DeSettingsValue, type DeTraderHistoryRowElement } from "@/au24/types/generated.js"
import { ColDef, GridOptions } from "ag-grid-community"
import DeTraderHistoryCell from "@/au24/view/tables/au24-trader-history-table/.old/trader-history-table/TraderHistoryCell.vue"
import Au24TraderHistoryTableHeader from "@/au24/view/tables/au24-trader-history-table/.old/trader-history-table/TraderHistoryTableHeader.vue"

const props = defineProps<{
	height: number
	historyRows: DeTraderHistoryRowElement[]
	settings: DeSettingsValue
}>()

const width = 590

const gridOptions: GridOptions = {
	defaultColDef: {
		suppressHeaderMenuButton: true, // TODO: this version of ag-grid doesn't have this property
		cellRendererParams: {
			price_label: props.settings?.price_label,
			quantity_label: props.settings?.quantity_label
		},
		headerComponentParams: {
			price_label: props.settings?.price_label,
			quantity_label: props.settings?.quantity_label
		}
	},
	suppressHorizontalScroll: true
}

const columnDefs: ColDef[] = [
	{
		headerName: "Round",
		width: 45,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Price",
		width: 75,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Constraints",
		width: 140,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Order",
		width: 70,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Value",
		width: 84,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Submitted by",
		width: 85,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	},
	{
		headerName: "Excess",
		width: 70,
		headerComponent: Au24TraderHistoryTableHeader,
		cellRenderer: DeTraderHistoryCell
	}
]
</script>

<style scoped></style>
