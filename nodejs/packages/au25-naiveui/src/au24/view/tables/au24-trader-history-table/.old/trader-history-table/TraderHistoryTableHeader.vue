<!-- {'model': 'claude-3-sonnet-20240229', 'input_tokens': 2051, 'output_tokens': 526, 'input_cost': '$0.006153', 'output_cost': '$0.007890', 'total_cost': '$0.014043'} -->
<template>
	<div class="DeTraderHistoryTableHeader">
		<div style="height: 15px">{{ top }}</div>
		<div style="height: 15px; font-size: 10px">{{ bottom }}</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"

const props = defineProps({
	params: {
		type: Object,
		required: true
	}
})

const settings = computed(() => (props.params?.$parent?.$parent?.$parent as DeTraderHistoryTable)?.settings)

const top = computed(() => props.params?.displayName || "")

const price_label = computed(() => settings?.value?.price_label)

const quantity_label = computed(() => settings?.value?.quantity_label)

const bottom = computed(() => {
	const display_name = props.params?.displayName
	if (display_name === "Round") return ""
	if (display_name === "Price") return `(${price_label.value})`
	if (display_name === "Order") return `(${quantity_label.value})`
	if (display_name === "Constraints") return `(${quantity_label.value})`
	if (display_name === "Excess") return "(indicator)"
	if (display_name === "Submitted by") return "(bidder)"
	else return ""
})

const refresh = (params: any) => {
	return true
}
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.DeTraderHistoryTableHeader {
	font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
	text-align: center;
	width: 100%;
	white-space: normal;
}
</style>
