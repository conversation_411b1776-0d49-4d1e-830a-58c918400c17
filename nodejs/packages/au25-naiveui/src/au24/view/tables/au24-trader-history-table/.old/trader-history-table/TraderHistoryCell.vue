<!-- {'model': 'claude-3-sonnet-20240229', 'input_tokens': 3071, 'output_tokens': 1427, 'input_cost': '$0.009213', 'output_cost': '$0.021405', 'total_cost': '$0.030618'} -->
<template>
	<div class="DeTraderHistoryCell">
		<template v-if="headerName === 'Round'">
			<div class="_round">
				{{ row.round_number }}
			</div>
		</template>

		<template v-if="headerName === 'Price'">
			<div class="_price" :style="{ color: priceColor }">
				{{ row.round_price }}
			</div>
		</template>

		<template v-if="headerName === 'Constraints'">
			<div class="_constraints">
				<DeOrderConstraintsBar
					style="left: -1px; top: -6px"
					:width="140"
					:height="18"
					:tick_font_size="9"
					:show_labels="false"
					:constraints="row!!.bid_constraints!!"
					:order_type="row.order_type"
					:order_quantity="orderQuantity"
				/>
			</div>
		</template>

		<div v-if="headerName === 'Order'" :style="{ color: priceColor }" class="_order">
			<div class="_order_type">
				{{ orderTypeLabel }}
			</div>
			<div class="_order_quantity">
				{{ row.quantity }}
			</div>
		</div>

		<template v-if="headerName === 'value'">
			<span class="_value" style="color: #ddd">
				{{ row.value }}
			</span>
		</template>

		<template v-if="headerName === 'Submitted by'">
			<span class="_username" style="color: #ddd">
				{{ row.order_submitted_by }}
			</span>
		</template>

		<template v-if="headerName === 'Excess'">
			<span class="_excess-level" :style="{ color: excessLabelColor }">
				{{ excessLevel }}
			</span>
			<span class="_excess-direction" :style="{ color: excessLabelColor }">
				{{ excessSide }}
			</span>
		</template>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { type DeTraderHistoryRowElement, OrderType, PriceDirection } from "@/au24/types/generated.js"
import DeOrderConstraintsBar from "../../../../au21-frontend/pages/De/DeTrader/constraints/DeOrderConstraintsBar.vue"
import { toNumber } from "lodash"
import { auColors, AuColors } from "@/au24/au-styles/AuColors.ts"

const props = defineProps({
	params: {
		type: Object,
		required: true
	}
})

const colors: AuColors = auColors

const headerName = computed(() => props.params?.column.colDef.headerName)
const colId = computed(() => props.params?.column.colId || "")
const row = computed(() => (props.params?.data as DeTraderHistoryRowElement) || null)

const orderQuantity = computed(() => toNumber(row.value?.quantity) || 0)

const orderTypeLabel = computed(() => {
	switch (row.value?.order_type) {
		case OrderType.BUY:
			return "Buy"
		case OrderType.SELL:
			return "Sell"
		default:
			return "---"
	}
})

const excessLabelText = computed(() => (row.value?.price_direction === PriceDirection.UP ? "Demand" : "Supply"))

const excessLevel = computed(() => row.value?.excess_level)
const excessSide = computed(() => row.value?.excess_side)
const excessLabelColor = computed(() => colors.order_quantity_text_color(excessSide.value))
const priceColor = computed(() => colors.getPriceColor(row.value?.price_direction))

const refresh = (params: any) => {
	return true
}
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.DeTraderHistoryCell {
	font-size: 13px;
	margin: 0;
	padding: 0;
	width: 100%;

	._round {
		padding-left: 10px;
		padding-right: 5px;
		text-align: right;
		width: 30px;
	}

	._price {
		display: inline-block;
		padding-right: 8px;
		text-align: right;
		width: 65px;
	}

	._constraints {
		position: relative;
		text-align: center;
		top: 9px;
		left: -2px;
		width: 100%;
	}

	._order {
		overflow: hidden;
		text-align: right;
		padding-right: 2px;
		width: 60px;

		._order_type {
			display: inline-block;
			width: 35px;
		}

		._order_quantity {
			display: inline-block;
			text-align: right;
			width: 25px;
		}
	}

	._username {
		color: @white;
	}

	._excess-level {
		display: inline-block;
		left: -5px;
		position: relative;
		text-align: right;
		width: 30px;
	}

	._excess-direction {
		display: inline-block;
	}
}
</style>
