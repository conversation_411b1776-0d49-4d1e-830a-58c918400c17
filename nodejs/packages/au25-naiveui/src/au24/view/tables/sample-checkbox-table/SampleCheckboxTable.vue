<template>
	<div>
		<AuBaseTable
			style="width: 500px; height: 300px"
			class="ag-theme-alpine"
			:columnDefs="columnDefs"
			:rowData="rowData"
			:rowSelection="'multiple'"
			@grid-ready="onGridReady"
		></AuBaseTable>
	</div>
</template>

<script setup>
import { onMounted, ref } from "vue"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"

const columnDefs = ref([
	{
		headerName: "",
		field: "selected",
		width: 50,
		checkboxSelection: true,
		headerCheckboxSelection: true,
		headerCheckboxSelectionFilteredOnly: true
	},
	{ field: "longName", headerName: "Company Name" },
	{ field: "shortName", headerName: "Short Name" }
])

const rowData = ref([
	{ longName: "Apple Inc.", shortName: "AAPL" },
	{ longName: "Microsoft Corporation", shortName: "MSFT" },
	{ longName: "Alphabet Inc.", shortName: "GOOGL" },
	{ longName: "Amazon.com, Inc.", shortName: "AMZN" },
	{ longName: "Facebook, Inc.", shortName: "FB" }
])

let gridApi = null

onMounted(() => {
	if (gridApi) {
		gridApi.sizeColumnsToFit()
	}
})

function onGridReady(params) {
	gridApi = params.api
}
</script>
