<template>
	<Au24BaseTable
		style="width: 95%; margin: 10px"
		:columns="columnDefs"
		:data="companies"
		:height="200"
	></Au24BaseTable>
</template>

<script setup lang="ts">
import { DataTableColumns } from "naive-ui"
import { computed, PropType } from "vue"
import { CompanyElement, UserElement } from "@/au24/types/generated.ts"
import Au24BaseTable from "@/au24/view/tables/au24-base-table/Au24BaseTable.vue"

defineProps({
	companies: {
		type: Array as PropType<CompanyElement[]>,
		required: true
	},
	height: {
		type: Number,
		required: true
	},
	width: {
		type: Number,
		required: true
	}
	// onCompanySelected: {
	// 	type: Function as PropType<(company: CompanyElement) => void>,
	// 	default: companyController.company_selected
	// }
})

const columnDefs = computed<DataTableColumns<UserElement>>(() => [
	{
		title: "Long name",
		key: "company_longname"
		// defaultSortOrder: "ascend",
		// sorter: "default"
	},
	{
		title: "Short name",
		key: "company_shortname"
		// defaultSortOrder: "ascend",
		// sorter: "default",
	}
])
</script>

<style scoped></style>
