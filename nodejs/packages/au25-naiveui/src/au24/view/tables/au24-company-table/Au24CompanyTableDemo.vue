<template>
	<div>
		<Au24CompanyTable :companies="companies" :height="500" :width="500" />
		<!--		<pre>selected company = {{ selectedCompany }} </pre>-->
		<div>selected company = {{ selectedCompany }}</div>
		<pre>companies = {{ companies }}</pre>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { times } from "lodash"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper.ts"
import { type CompanyElement } from "@/au24/types/generated.js"
import Au24CompanyTable from "@/au24/view/tables/au24-company-table/Au24CompanyTable.vue"

const companies = times(30, createDemo__CompanyElement)
const selectedCompany = ref<CompanyElement | null>(null)

const handleCheck = rowKeys => {
	console.log("rowKeys", rowKeys)
	//selected_companies.value = rowKeys.map((key) => key as string)
}

// const onCompanySelected = (company: CompanyElement) => {
// 	console.log("company selected", company)
// 	selectedCompany.value = company
// }
</script>

<style scoped></style>
