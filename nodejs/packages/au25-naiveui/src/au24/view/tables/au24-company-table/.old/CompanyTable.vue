<template>
	<AuBaseTable
		v-if="companies.length"
		class="CompanyTable"
		:height="height"
		:width="width"
		:columnDefs="columnDefs"
		:rowData="companies"
		:getRowHeight="() => row_height"
		:gridOptions="gridOptions"
		@row-selected="onRowSelected"
	/>
</template>

<script setup lang="ts">
import { computed, PropType, ref } from "vue"
import { ColDef, GridOptions, ICellRendererParams } from "ag-grid-community"
import AuBaseTable from "@/au24/view/tables/au24-base-table/.old/AuBaseTable.vue"
import { type CompanyElement } from "@/au24/types/generated.js"
import { companyController } from "@/au24/commands/outgoing"

const props = defineProps({
	companies: {
		type: Array as PropType<CompanyElement[]>,
		required: true
	},
	height: {
		type: Number,
		required: true
	},
	width: {
		type: Number,
		required: true
	},
	onCompanySelected: {
		type: Function as PropType<(company: CompanyElement) => void>,
		default: companyController.company_selected
	}
})

const row_height = 24
const gridOptions = ref<GridOptions>({
	headerHeight: 28,
	defaultColDef: {
		cellStyle: () => ({ padding: "0", border: "0" })
	},
	rowSelection: "single",
	suppressHorizontalScroll: true
})

const getComparator = (valueGetter: (company: CompanyElement) => string) => {
	return (valueA: any, valueB: any, nodeA: any, nodeB: any, isInverted: boolean) => {
		const a: CompanyElement = nodeA.data
		const b: CompanyElement = nodeB.data
		return valueGetter(a).localeCompare(valueGetter(b))
	}
}

const onRowSelected = (row: any) => {
	if (row.node.isSelected()) {
		//emit('selected', row.data);
		//props.onCompanySelected(row.data)
	}
}

const columnDefs = computed<ColDef[]>(() => [
	{
		headerName: "Short name",
		cellRenderer: (params: ICellRendererParams) => {
			const company: CompanyElement = params.data
			return `<div>${company.company_shortname}</div>`
		},
		sortable: true,
		comparator: getComparator(company => company.company_shortname),
		sort: "asc",
		width: 100
	},
	{
		headerName: "Long name",
		cellRenderer: (params: ICellRendererParams) => {
			const company: CompanyElement = params.data
			return `<div>${company.company_longname || ""}</div>`
		},
		sortable: true,
		comparator: getComparator(company => company.company_longname)
	}
])
</script>

<style lang="less" scoped>
.CompanyTable {
	:deep(.ag-cell) {
		margin: 0;
		padding: 0;
		border: 0;
	}
}
</style>
