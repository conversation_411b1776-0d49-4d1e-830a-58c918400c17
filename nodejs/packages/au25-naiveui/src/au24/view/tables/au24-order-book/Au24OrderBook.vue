<template>
	<NDataTable
		class="auction-table"
		:columns
		:data
		:bordered="true"
		:bottom-bordered="true"
		:single-column="true"
		:max-height="height"
		:min-height="height"
		:scrollbar-props="{ trigger: 'none' }"
	></NDataTable>
</template>

<script setup lang="ts">
import { CompanyElement, DeRoundTraderElement, OrderType } from "@/au24/types/generated"
import { type DataTableColumns, NDataTable } from "naive-ui"
import { computed, h } from "vue"
import { Au24OrderBookModel } from "@/au24/view/tables/au24-order-book/Au24OrderBookModel"
import Au24OrderBookRow from "@/au24/view/tables/au24-order-book/Au24OrderBookRow.vue"

const props = defineProps<{
	height: number
	round_trader_elements: DeRoundTraderElement[]
	companies: CompanyElement[]
	quantity_label: string
}>()

const columnData: Array<{
	title: string
	key: keyof Au24OrderBookModel
	width: number
}> = [
	{ title: "Buyer", key: "buyCompanyShort", width: 110 },
	{
		title: `Buy (${props.quantity_label})`,
		key: "buyQuantityInt",
		width: 69
	},
	{
		title: `Sell (${props.quantity_label})`,
		key: "sellQuantityInt",
		width: 69
	},
	{ title: "Seller", key: "sellCompanyShort", width: 110 }
]

const columns: DataTableColumns<Au24OrderBookModel> = columnData.map(({ title, key, width }) => ({
	title,
	key,
	width,
	render: (rowData: Au24OrderBookModel) =>
		h(Au24OrderBookRow, {
			field: key,
			rowData
		})
}))

function getCompany(companyId: string): CompanyElement {
	const result = props.companies.find(c => c.company_id === companyId)
	if (!result) {
		throw new Error(`Company with id ${companyId} is not found`)
	}
	return result
}

const cells_by_side = computed(() => {
	const sellCells: DeRoundTraderElement[] = []
	const buyCells: DeRoundTraderElement[] = []
	props.round_trader_elements.forEach(cell => {
		if (cell.order_type === OrderType.SELL) {
			sellCells.push(cell)
		} else if (cell.order_type === OrderType.BUY) {
			buyCells.push(cell)
		}
	})
	return {
		sellCells,
		buyCells,
		maxCellList: sellCells.length > buyCells.length ? sellCells : buyCells
	}
})

// const empty_row_count = computed(() => {
// 	const body_height = props.height - 40
// 	const rows_height = cells_by_side.value.buyCells.length * row_height
// 	const empty_height = body_height - rows_height
// 	return empty_height > 0 ? Math.ceil(empty_height / row_height) : 0
// })

const data = computed(() => {
	const rows: Au24OrderBookModel[] = []

	cells_by_side.value.maxCellList.forEach((value, index) => {
		const row: Au24OrderBookRow = {
			buyCompanyShort: "",
			buySubmittedBy: "",
			buyTime: "",
			buyTimeStr: "",
			buyQuantityInt: 0,
			buyQuantityStr: "",
			hasBuy: false,
			hasSell: true,
			match: 0,
			id: index + "",
			sellCompanyShort: "",
			sellSubmittedBy: "",
			sellTime: "",
			sellQuantityInt: 0,
			sellQuantityStr: ""
		}

		const sellCell = cells_by_side.value.sellCells[index] as DeRoundTraderElement | undefined
		if (sellCell) {
			row.sellTime = sellCell.timestamp_formatted
			row.sellCompanyShort = getCompany(sellCell.cid).company_shortname
			row.sellSubmittedBy = sellCell.order_submitted_by
			row.sellQuantityInt = sellCell.quantity_int
			row.sellQuantityStr = sellCell.quantity_str
			row.match = 10
			row.hasSell = true
		}

		const buyCell = cells_by_side.value.buyCells[index] as DeRoundTraderElement | undefined
		if (buyCell) {
			row.buyTime = buyCell.timestamp_formatted
			row.buyCompanyShort = getCompany(buyCell.cid).company_shortname
			row.buySubmittedBy = buyCell.order_submitted_by
			row.buyQuantityInt = buyCell.quantity_int
			row.buyQuantityStr = buyCell.quantity_str
			row.match = 10
			row.hasBuy = true
		}

		rows.push(row)
	})

	return rows
})
</script>

<style scoped>
/* Style for header cells */
:deep(th.n-data-table-th) {
	font-size: 12px;
	margin: 0;
	overflow: hidden;
	padding: 0; /* Adjust the padding as needed */
	/*background-color: #f0f0f0; /* Set the background color for header cells */
	/*font-weight: bold; /* Make the header text bold */
	text-align: center; /* Center-align the header text */
	vertical-align: top;
}

/* Style for data cells */
:deep(td.n-data-table-td) {
	margin: 0;
	padding: 2px 0;
	overflow: hidden;
}
</style>
