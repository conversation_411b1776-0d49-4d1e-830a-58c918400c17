<template>
	<div class="DeOrderBookCell">
		<!-- -------------------- BUY SIDE -------------------->

		<!-- ------------ Column 1: BUYER -------------->
		<div v-if="row.hasBuy && columnName === 'buyCompanyShort'">
			<div :style="{ color: auColors.au_buy() }">
				<div class="_trader_cell">{{ row.buyCompanyShort }}</div>
				<div class="_trader_cell">({{ row.buySubmittedBy }})</div>
			</div>
		</div>

		<!-- ------------ Column 2: BUY QUANTITY -------------->
		<div v-else-if="row.hasBuy && columnName === 'buyQuantity'">
			<BuySellHBar
				:width="67"
				:height="32"
				:buy-max="50"
				:sell-max="50"
				:match="row.match"
				:order_submission_type="OrderSubmissionType.MANUAL"
				:order_quantity_int="row.buyQuantityInt"
				:order_quantity_str="row.buyQuantityStr"
				:order_type="OrderType.BUY"
			/>
		</div>

		<!-- -------------------- SELL SIDE -------------------->

		<!-- ------------ Column 3: SELL QUANTITY -------------->
		<div v-else-if="row.hasSell && columnName === 'sellQuantity'">
			<BuySellHBar
				:width="67"
				:height="32"
				:buy-max="50"
				:sell-max="50"
				:match="row.match"
				:order_submission_type="OrderSubmissionType.MANUAL"
				:order_quantity_int="row.sellQuantityInt"
				:order_quantity_str="row.sellQuantityStr"
				:order_type="OrderType.SELL"
			/>
		</div>

		<!-- ------------ Column 4: SELLER -------------->
		<div v-else-if="row.hasSell && columnName === 'sellCompanyShort'">
			<div :style="{ color: auColors.au_sell() }">
				<div class="_trader_cell">{{ row.sellCompanyShort }}</div>
				<div class="_trader_cell" v-if="row.sellSubmittedBy">({{ row.sellSubmittedBy }})</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { OrderSubmissionType, OrderType } from "@/au24/types/generated.ts"
import { DeOrderBookRow } from "@/au24/view/tables/au24-order-book/.old/DeOrderBookRow.ts"
import BuySellHBar from "@/au24/view/widgets/buy-sell-bars/BuySellHBar.vue"
import { auColors } from "@/au24/au-styles/AuColors.ts"

const props = defineProps<{
	params: {
		data: DeOrderBookRow
		column: {
			colId: string
		}
	}
}>()

const row = computed(() => props.params.data)
const columnName = computed(() => props.params.column.colId)
</script>

<style lang="less" scoped>
@import (reference) "@/au24/au-styles/variables.less";

.DeOrderBookCell {
	color: #222222;
	text-align: center;
	position: absolute;

	._trader_cell {
		line-height: 1.1em;
		margin: 5px;
	}

	._quantity {
		color: #222;
		font-size: 14px;
		margin-right: 20px;
		margin-top: 5px;
		text-align: right;
		width: 40px;
	}
}
</style>
