<template>
	<div class="Au24OrderBookRow">
		<!-- -------------------- BUY SIDE -------------------->

		<!-- ------------ Column 1: BUYER -------------->
		<div v-if="rowData.hasBuy && field === 'buyCompanyShort'" class="cell">
			<div class="_buyer" :style="{ color: colors.au_buy() }">
				<div class="_trader_cell">{{ rowData.buyCompanyShort }}</div>
				<div class="_trader_cell">({{ rowData.buySubmittedBy }})</div>
			</div>
		</div>

		<!-- ------------ Column 2: BUY QUANTITY -------------->
		<div v-else-if="rowData.hasBuy && field === 'buyQuantityInt'" class="cell">
			<div class="_buyerQuantity">
				<Au24BuySellHBar
					:width="67"
					:height="32"
					:buy-max="50"
					:sell-max="50"
					:match="rowData.match"
					:order_submission_type="OrderSubmissionType.MANUAL"
					:order_quantity_int="rowData.buyQuantityInt"
					:order_quantity_str="rowData.buyQuantityStr"
					:order_type="OrderType.BUY"
				/>
			</div>
		</div>

		<!-- -------------------- SELL SIDE -------------------->

		<!-- ------------ Column 3: SELL QUANTITY -------------->
		<div v-else-if="rowData.hasSell && field === 'sellQuantityInt'" class="cell">
			<div class="_seller" style="position: absolute">
				<Au24BuySellHBar
					:width="67"
					:height="20"
					:buy-max="50"
					:sell-max="50"
					:match="rowData.match"
					:order_submission_type="OrderSubmissionType.MANUAL"
					:order_quantity_int="rowData.sellQuantityInt"
					:order_quantity_str="rowData.sellQuantityStr"
					:order_type="OrderType.SELL"
				/>
			</div>
		</div>

		<!-- ------------ Column 4: SELLER -------------->
		<div v-else-if="rowData.hasSell && field === 'sellCompanyShort'" class="cell">
			<div :style="{ color: colors.au_sell() }">
				<div class="_trader_cell">{{ rowData.sellCompanyShort }}</div>
				<div class="_trader_cell" v-if="rowData.sellSubmittedBy">({{ rowData.sellSubmittedBy }})</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { Au24OrderBookModel } from "@/au24/view/tables/au24-order-book/Au24OrderBookModel.ts"
import { AuColors, auColors } from "@/au24/au-styles/AuColors.ts"
import { OrderSubmissionType, OrderType } from "@/au24/types/generated.ts"
import Au24BuySellHBar from "@/au24/view/tables/au24-order-book/Au24BuySellHBar.vue"

const colors: AuColors = auColors

const props = defineProps<{
	field: keyof Au24OrderBookModel
	rowData: Au24OrderBookModel
}>()
</script>
<style scoped>
.Au24OrderBookRow {
	font-size: 12px;
	margin: 0;
	padding: 0;
	width: 100%;

	.cell {
		border: 1px solid red;
		//height: 20px;
	}

	._buyer {
		border: 1px solid blue;
		text-align: right;
		margin: 0;
		padding: 0;
	}

	._buyerQuantity {
		border: 1px solid yellow;
		position: absolute;
		text-align: right;
		margin: 0;
		padding: 0;
		width: 50px;
	}

	._seller {
		border: 1px solid blue;
		text-align: left;
		margin: 0;
		padding: 0;
	}
}
</style>
<!--<style scoped>-->

<!--.AuOrderBook {-->
<!--	color: #222222;-->
<!--	text-align: center;-->
<!--	position: absolute;-->

<!--	._trader_cell {-->
<!--		line-height: 1.1em;-->
<!--		margin: 5px;-->
<!--	}-->

<!--	._quantity {-->
<!--		color: #222;-->
<!--		font-size: 14px;-->
<!--		margin-right: 20px;-->
<!--		margin-top: 5px;-->
<!--		text-align: right;-->
<!--		width: 40px;-->
<!--	}-->
<!--}-->
<!--</style>-->
