<template>
	<div>
		<table>
			<tbody>
				<tr>
					<td>
						<Au24OrderBook
							:companies="companies"
							:height
							:quantity_label="deSettings.quantity_label"
							:round_trader_elements="cells"
						/>
					</td>
					<td>
						<Au24OrderBook
							:companies="companies"
							:height
							:quantity_label="deSettings.quantity_label"
							:round_trader_elements="cells"
						/>
					</td>
				</tr>
			</tbody>
		</table>
		<pre>{{ cells }}</pre>
	</div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue"
import { createMultipleByClosure } from "@/au24/utils"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper"
import { createDemo__DeRoundTraderElement } from "@/au24/helpers/demo-helpers/DeRoundTable.helper"
import { createDefault__DeSettingsValue } from "@/au24/helpers/demo-helpers/DeSettingsValue.helper"
import Au24OrderBook from "@/au24/view/tables/au24-order-book/Au24OrderBook.vue"

const companies = ref(createMultipleByClosure(createDemo__CompanyElement, 10))
const cells = computed(() => companies.value.map(company => createDemo__DeRoundTraderElement(1, company)))
const deSettings = createDefault__DeSettingsValue()

const height = 200
</script>

<style scoped></style>
