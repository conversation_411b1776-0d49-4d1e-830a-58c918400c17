<template>
	<div>
		<DeOrderBook
			:height="500"
			:round_trader_elements="cells"
			:companies="companies"
			:quantity_label="deSettings.quantity_label"
		/>
		<pre>{{ cells }}</pre>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import De<PERSON>rderB<PERSON> from "./DeOrderBook.vue"
import { createMultipleByClosure } from "@/au24/utils"
import { createDemo__CompanyElement } from "@/au24/helpers/demo-helpers/CompanyElement.helper.ts"
import { createDemo__DeRoundTraderElement } from "@/au24/helpers/demo-helpers/DeRoundTable.helper.ts"
import { createDefault__DeSettingsValue } from "@/au24/helpers/demo-helpers/DeSettingsValue.helper.ts"

const companies = ref(createMultipleByClosure(createDemo__CompanyElement, 10))
const cells = computed(() => companies.value.map(company => createDemo__DeRoundTraderElement(1, company)))
const deSettings = createDefault__DeSettingsValue()
</script>
