<template>
    <n-card title="Auction Settings" class="w-full max-w-4xl mx-auto">
        <n-form
                ref="formRef"
                :model="formModel"
                :rules="rules"
                :show-feedback="false"
                label-placement="left"
                :label-width="labelWidth"
                require-mark-placement="right"
        >
            <!-- Basic Settings -->
            <n-h3>Basic Settings</n-h3>
            <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <n-grid-item>
                    <n-form-item label="Auction Name" path="auction_name">
                        <n-input v-model:value="formModel.auction_name" placeholder="Enter auction name" />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Use Counterparty Credits">
                        <n-checkbox v-model:checked="formModel.use_counterparty_credits" />
                    </n-form-item>
                </n-grid-item>
            </n-grid>

            <n-divider />

            <!-- Price Settings -->
            <n-h3>Price Settings</n-h3>
            <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <n-grid-item>
                    <n-form-item label="Price Label" path="price_label">
                        <n-input v-model:value="formModel.price_label" />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Decimal Places" path="price_decimal_places">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.price_decimal_places"
                                    :min="0"
                                    :max="10"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">places</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Initial Price Change" path="price_change_initial">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.price_change_initial"
                                    :step="0.01"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">{{ formModel.price_label }}</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Post-Reversal Price Change" path="price_change_post_reversal">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.price_change_post_reversal"
                                    :step="0.01"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">{{ formModel.price_label }}</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
            </n-grid>

            <n-divider />

            <!-- Quantity Settings -->
            <n-h3>Quantity Settings</n-h3>
            <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <n-grid-item>
                    <n-form-item label="Quantity Label" path="quantity_label">
                        <n-input v-model:value="formModel.quantity_label" />
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Cost Multiplier" path="cost_multiplier">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.cost_multiplier"
                                    :step="0.01"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">multiplier</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Minimum Quantity" path="quantity_minimum">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.quantity_minimum"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">{{ formModel.quantity_label }}</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Quantity Step" path="quantity_step">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.quantity_step"
                                    :step="0.01"
                                    :min="0.01"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">{{ formModel.quantity_label }}</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
            </n-grid>

            <n-divider />

            <!-- Excess Levels -->
            <n-h3>Excess Levels</n-h3>
            <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <template v-for="level in 5" :key="level - 1">
                    <n-grid-item>
                        <n-form-item :label="`Level ${level - 1} Label`" :path="`excess_level_${level - 1}_label`">
                            <n-input v-model:value="formModel[`excess_level_${level - 1}_label`]" />
                        </n-form-item>
                    </n-grid-item>
                    <n-grid-item v-if="level > 1">
                        <n-form-item :label="`Level ${level - 1} Quantity`" :path="`excess_level_${level - 1}_quantity`">
                            <div class="flex items-center gap-2">
                                <n-input-number
                                        v-model:value="formModel[`excess_level_${level - 1}_quantity`]"
                                        :min="0"
                                        :show-button="false"
                                        style="width: 160px"
                                />
                                <span class="inline-block w-[150px] text-sm text-right text-gray-500">{{ formModel.quantity_label }}</span>
                            </div>
                        </n-form-item>
                    </n-grid-item>
                </template>
            </n-grid>

            <n-divider />

            <!-- Timing Settings -->
            <n-h3>Timing Settings</n-h3>
            <n-grid :cols="2" :x-gap="12" :y-gap="8">
                <n-grid-item>
                    <n-form-item label="Round Open Duration" path="round_open_min_secs">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.round_open_min_secs"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">seconds</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Round Closed Duration" path="round_closed_min_secs">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.round_closed_min_secs"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">seconds</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Orange Warning" path="round_orange_secs">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.round_orange_secs"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">seconds</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item>
                    <n-form-item label="Red Warning" path="round_red_secs">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.round_red_secs"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">seconds</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
                <n-grid-item :span="2">
                    <n-form-item label="Starting Price Announcement" path="starting_price_announcement_mins">
                        <div class="flex items-center gap-2">
                            <n-input-number
                                    v-model:value="formModel.starting_price_announcement_mins"
                                    :min="0"
                                    :show-button="false"
                                    style="width: 160px"
                            />
                            <span class="inline-block w-[150px] text-sm text-right text-gray-500">minutes</span>
                        </div>
                    </n-form-item>
                </n-grid-item>
            </n-grid>

            <div class="mt-6">
                <n-button type="primary" block @click="handleSubmit">Save Settings</n-button>
            </div>
        </n-form>
    </n-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInst, FormRules } from 'naive-ui'
import {
    NCard,
    NButton,
    NForm,
    NFormItem,
    NInput,
    NInputNumber,
    NCheckbox,
    NDivider,
    NH3,
    NGrid,
    NGridItem
} from 'naive-ui'

interface DateTimeValue {
    date: Date | null
    time: Date | null
}

interface DeSettingsValue {
    auction_name: string
    cost_multiplier: string
    excess_level_0_label: string
    excess_level_1_label: string
    excess_level_1_quantity: string
    excess_level_2_label: string
    excess_level_2_quantity: string
    excess_level_3_label: string
    excess_level_3_quantity: string
    excess_level_4_label: string
    excess_level_4_quantity: string
    price_change_initial: string
    price_change_post_reversal: string
    price_decimal_places: number
    price_label: string
    quantity_label: string
    quantity_minimum: string
    quantity_step: string
    round_closed_min_secs: number
    round_open_min_secs: number
    round_orange_secs: number
    round_red_secs: number
    starting_price_announcement_mins: number
    starting_time: DateTimeValue | null
    use_counterparty_credits: boolean
}

const labelWidth = ref(200)
const formRef = ref<FormInst | null>(null)
const formModel = ref<DeSettingsValue>({
    auction_name: '',
    cost_multiplier: '1.0',
    excess_level_0_label: 'None',
    excess_level_1_label: 'Low',
    excess_level_1_quantity: '0',
    excess_level_2_label: 'Medium',
    excess_level_2_quantity: '0',
    excess_level_3_label: 'High',
    excess_level_3_quantity: '0',
    excess_level_4_label: 'Very High',
    excess_level_4_quantity: '0',
    price_change_initial: '0',
    price_change_post_reversal: '0',
    price_decimal_places: 2,
    price_label: 'Price',
    quantity_label: 'Quantity',
    quantity_minimum: '0',
    quantity_step: '1',
    round_closed_min_secs: 60,
    round_open_min_secs: 300,
    round_orange_secs: 30,
    round_red_secs: 10,
    starting_price_announcement_mins: 30,
    starting_time: null,
    use_counterparty_credits: false
})

const rules: FormRules = {
    auction_name: {
        required: true,
        message: 'Please enter auction name',
        trigger: ['blur', 'input']
    },
    price_decimal_places: {
        type: 'number',
        min: 0,
        max: 10,
        message: 'Decimal places must be between 0 and 10',
        trigger: ['blur', 'change']
    }
}

const handleSubmit = async () => {
    try {
        await formRef.value?.validate()
        console.log('Form validated:', formModel.value)
    } catch (errors) {
        console.error('Validation failed:', errors)
    }
}
</script>
