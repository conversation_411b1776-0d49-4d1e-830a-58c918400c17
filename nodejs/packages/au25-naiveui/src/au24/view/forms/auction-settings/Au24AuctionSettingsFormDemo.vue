<template>
	<Au24AuctionSettingsForm
		:settings="settings"
		@update:settings="updateSettings"
		:crud="crud"
	></Au24AuctionSettingsForm>
</template>

<script setup lang="ts">
import { Crud } from "@/au24/types/generated.ts"
import { createDefault__DeSettingsValue } from "@/au24/helpers/demo-helpers/DeSettingsValue.helper.ts"
import { ref } from "vue"
import Au24AuctionSettingsForm from "@/au24/view/forms/auction-settings/Au24AuctionSettingsForm.vue"
import { DeSettingsFormModel, settingsValueToFormModel } from "@/au24/helpers/entity-helpers/de_settings_helpers.ts"

const settings = ref<DeSettingsFormModel>(settingsValueToFormModel(createDefault__DeSettingsValue()))

const crud = ref<keyof typeof Crud>(Crud.CREATE)

const updateSettings = (updatedSettings: DeSettingsFormModel) => {
	settings.value = updatedSettings
	console.log({ updatedSettings })
}
// watch(settings, () =>{
// 	console.log("settings changed", settings.value)
// })
</script>
<style scoped></style>
