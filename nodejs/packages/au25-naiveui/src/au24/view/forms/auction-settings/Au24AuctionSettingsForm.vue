c<template>
	<div class="Au24AuctionSettingsForm">
		<NButton @click="updateSettings">Update</NButton>
		<n-form
			ref="formRef"
			:model="settings"
			:rules="rules"
			label-width="auto"
			label-placement="left"
			require-mark-placement="right-hanging"
			size="tiny"
            :show-feedback="false"
	>
			<n-card title="Auction settings">
				<n-card title="Auction Name" embedded size="small">
					<n-form-item path="auctionName" size="small">
						<n-input
							v-model:value="settings.auctionName"
							placeholder="Enter auction name"
							type="textarea"
							:autosize="{ minRows: 2 }"
						/>
					</n-form-item>
				</n-card>

				<n-card title="Round Timer" embedded size="small">
					<!--					<n-form-item label="Starting time:" path="startingTime" size="small">-->
					<!--					<n-date-picker  value="startingTimeDate" type="datetime" style="width: 100px" />-->
					<!--					</n-form-item>-->
					<!--				<n-form-item label="Mins before start to announce price:" path="minsBeforeStart">-->
					<!--					<n-input-number v-model:value="settings.minsBeforeStart" style="width: 100px" />-->
					<!--				</n-form-item>-->
					<!--					<n-form-item label="Show orange status after:" size="small" path="showOrangeAfter">-->
					<!--						<n-input-number-->
					<!--							v-model:value="settings.showOrangeAfter"-->
					<!--							:show-button="true"-->
					<!--							:precision="0"-->
					<!--							:min="0"-->
					<!--							:step="1"-->
					<!--							size="small"-->
					<!--							:style="`width: ${input_width}px; text-align:right`"-->
					<!--						/>-->
					<!--						<span class="ml-2">seconds</span>-->
					<!--					</n-form-item>-->
					<!--					<n-form-item label="Show red status after:" path="showRedAfter">-->
					<!--						<n-input-number-->
					<!--							v-model:value="settings.showRedAfter"-->
					<!--							:min="0"-->
					<!--							:step="1"-->
					<!--							style="width: 100px"-->
					<!--							size="small"-->
					<!--							:style="`width: ${input_width}px; text-align:right`"-->
					<!--						/>-->
					<!--						<span class="ml-2">seconds</span>-->
					<!--					</n-form-item>-->
				</n-card>

				<!--			PRICE          -->
				<n-card title="Price settings" embedded size="small">
					<n-form-item label="Price label:" path="priceLabel">
						<n-input v-model:value="settings.priceLabel" class="input" />
					</n-form-item>
					<n-form-item label="Decimal places:" path="decimalPlaces">
						<au24-number-input class="input" v-model:value="settings.decimalPlaces" />
					</n-form-item>
					<n-form-item label="Initial price change:" path="initialPriceChange">
						<au24-number-input
							class="input"
							v-model:value="settings.initialPriceChange"
							:suffix="settings.priceLabel"
						/>
					</n-form-item>
					<n-form-item label="Post reversal price change:" path="postReversalPriceChange">
						<au24-number-input
							v-model:value="settings.postReversalPriceChange"
							class="input"
							:suffix="settings.priceLabel"
						/>
					</n-form-item>
				</n-card>

				<!--					<n-divider :vertical="true" />-->

				<!--			QUANTITY        -->

				<n-card title="Quantity Settings" embedded size="small">
					<!--				<n-form-item label="Category:" path="category">-->
					<!--					<n-select v-model:value="settings.category" :options="categoryOptions" style="width: 100px" />-->
					<!--				</n-form-item>-->
					<n-form-item label="Quantity label:" path="quantityLabel">
						<n-input class="input" v-model:value="settings.quantityLabel" />
					</n-form-item>
					<n-form-item label="Quantity decrement:" path="quantityDecrement">
						<au24-number-input
							class="input"
							v-model:value="settings.quantityDecrement"
							:suffix="settings.quantityLabel"
						/>
					</n-form-item>
					<n-input-group>
						<n-input-group-label class="input-label">Minimum Quantity:</n-input-group-label>
						<Au24NumberInput class="input" v-model:value="settings.minimumQuantity" suffix="" />
						<n-input-group-label style="width: 150px">
							{{ settings.quantityLabel }}
						</n-input-group-label>
					</n-input-group>
					<n-form-item label="Minimum quantity:" path="minimumQuantity">
						<Au24NumberInput
							class="input"
							v-model:value="settings.minimumQuantity"
							:suffix="settings.quantityLabel"
						/>
					</n-form-item>
				</n-card>
				<n-card title="Default Initial Trader Limits" embedded size="small">
					<n-form-item label="Default seller quantity limit:" path="sellerQuantityLimit">
						<!--					<n-input-number v-model:value="settings.el" :min="0" :step="1"-->
						<!--									style="width: 100px" />-->
						<!--					<span class="ml-2">{{ settings.quantity_label }}</span>-->
					</n-form-item>
					<!--				<n-form-item label="Default buyer credit limit:" path="buyerCreditLimit">-->
					<!--					<n-input-number v-model:value="settings.buyerCreditLimit" :min="0" :step="1"-->
					<!--									style="width: 100px" />-->
					<!--					<span class="ml-2">($) determines max buy</span>-->
					<!--				</n-form-item>-->
				</n-card>

				<n-card :title="'Excess (' + settings.quantityLabel + ') -> Label'" embedded size="small">
					<!--				<div v-for="(item, index) in settings.excess_level_0_label" :key="index" class="flex items-center mb-2 ml-10">-->
					<!--					<n-select v-model:value="item.excess" :options="operatorOptions" style="width: 60px"-->
					<!--							  class="mr-2" />-->
					<!--					<n-input-number v-model:value="item.excess" :min="0" :step="1" style="width: 100px" />-->
					<!--					<n-input v-model:value="item.label" placeholder="Label" style="width: 100px" class="ml-2" />-->
					<!--				</div>-->
				</n-card>

				<div class="flex justify-end space-x-4 mt-4">
					<n-button round @click="handleCancel">Cancel</n-button>
					<n-button round type="primary" @click="handleSubmit">Submit</n-button>
				</div>
			</n-card>
		</n-form>
	</div>
</template>

<script setup lang="ts">
import { computed, PropType, reactive, ref, watch } from "vue"
import { FormInst, FormItemRule, FormRules, NInput, useMessage } from "naive-ui"
import { Crud } from "@/au24/types/generated.ts"
import { DeSettingsFormModel } from "@/au24/helpers/entity-helpers/de_settings_helpers.ts"
import Au24NumberInput from "@/au24/view/ui-component/au24-number-input/Au24NumberInput.vue"

const input_width = 120

const props = defineProps({
	settings: {
		type: Object as PropType<DeSettingsFormModel>,
		required: true
	},
	crud: {
		type: String as () => keyof typeof Crud,
		required: true,
		validator: (value: any) => Object.values(Crud).includes(value)
	}
})

const formatValue = computed(() => (value: number | null) => {
	if (value === null) return ""
	return value.toFixed(props.settings.decimalPlaces)
})

const thousands_formatter = (value: number | null, precision: number) => {
	if (value === null) return ""
	return value.toLocaleString(undefined, {
		minimumFractionDigits: precision,
		maximumFractionDigits: precision
	})
}

const formattedMinimumQuantity = ref("")

watch(
	() => props.settings.minimumQuantity,
	value => {
		formattedMinimumQuantity.value = thousands_formatter(value, 0)
	},
	{ immediate: true }
)

const minimumQuantityProxy = computed({
	get() {
		console.log("getting", formattedMinimumQuantity.value)
		return formattedMinimumQuantity.value
	},
	set(value) {
		console.log("setting")
		const numericValue = parseFloat(value.replace(/,/g, ""))
		props.settings.minimumQuantity = isNaN(numericValue) ? 0 : numericValue
	}
})

const stepValue = computed(() => {
	return Math.pow(10, -props.settings.decimalPlaces)
})

const emit = defineEmits<{
	(event: "update:settings", value: DeSettingsFormModel): void
}>()

// Example function to handle settings update
const updateSettings = () => {
	emit("update:settings", props.settings)
}

// const starting_time = computed<Date | null>(() =>
// 	starting_time.value? && dateTimeValue_to_date(settings.value.starting_time)
// )

const message = useMessage()

interface ExcessData {
	operator: string
	excess: number
	label: string
}

const operatorOptions = [
	{ label: ">", value: ">" },
	{ label: ">=", value: ">=" }
]

const formRef = ref<FormInst | null>(null)

const formModel = reactive<FormModel>({
	auctionName: "",
	startingTime: null,
	minsBeforeStart: null,
	showOrangeAfter: 15,
	showRedAfter: 30,
	initialPriceChange: 0.5,
	postReversalPriceChange: 0.125,
	priceLabel: "cpp",
	decimalPlaces: 3,
	category: null,
	quantityLabel: "MMLB",
	quantityDecrement: null,
	minimumQuantity: 1,
	sellerQuantityLimit: null,
	buyerCreditLimit: null,
	excessData: [
		{ operator: ">", excess: 50, label: "5+" },
		{ operator: ">", excess: 30, label: "4+" },
		{ operator: ">", excess: 20, label: "3+" },
		{ operator: ">", excess: 10, label: "2+" },
		{ operator: ">", excess: 0, label: "1+" }
	]
})

const isNonNegativeIntegerValidator = (label: string) => ({
	required: true,
	validator(rule: FormItemRule, value: number) {
		if (!Number.isInteger(value) || value < 0) {
			return new Error(`${label} should be an integer >= 0`)
		}
		return true
	},
	trigger: ["input", "blur"]
})

const rules: FormRules = {
	auctionName: {
		required: true,
		message: "Please enter the auction name",
		trigger: "blur"
	},
	minsBeforeStart: isNonNegativeIntegerValidator("Mins before start")
	// Add more rules for other fields as needed
}

const categoryOptions = [
	{ label: "Option 1", value: "option1" },
	{ label: "Option 2", value: "option2" }
	// Add more category options
]

const handleCancel = () => {
	message.error("cancel")
	// Handle cancel button click
}

const handleSubmit = () => {
	message.info("submit")
}

// const CustomNumberInput = defineComponent({
// 	props: {
// 		modelValue: [Number, String],
// 		decimalPlaces: { type: Number, default: 0 }
// 	},
// 	emits: ["update:modelValue"],
// 	setup(props, { emit }) {
// 		const updateValue = (value: any) => {
// 			emit("update:modelValue", value)
// 		}
// 		return () =>
// 			h(Au24NumberInput, {
// 				value: props.modelValue,
// 				"onUpdate:value": updateValue,
// 				decimalPlaces: props.decimalPlaces, // Use the decimalPlaces prop
// 				format: "", // Adjust the format prop as needed
// 				disabled: false,
// 				output: false,
// 				fontSize: 13,
// 				height: 28,
// 				width: 100,
// 				class: "_number_input"
// 			})
// 	}
// })
</script>
<style scoped>
.Au24AuctionSettingsForm {
	.input {
		width: 170px;
	}

	.input-label {
		text-align: right;
		width: 180px;
	}
}
</style>
<!--CONTROL THESE IN THEMESTORE-->
<!--:deep(.n-card-header) {-->
<!--font-size: 14px;-->
<!--padding: 5px;-->
<!--margin: 5px;-->
<!--}-->

<!--:deep(.n-form-item-label) {-->
<!--font-size: 13px;-->
<!--}-->

<!--:deep(.n-input__input-el) {-->
<!--font-size: 14px;-->
<!--}-->

<!--:deep(.n-input-number-wrapper ) {-->
<!--text-align: right !important;-->
<!--}-->
<!--//:deep(.n-input-number) {-->
<!--//	width: 100px !important;-->
<!--//}-->
<!--//-->
<!--//:deep(.n-input-wrapper) {-->
<!--//	width: 100px !important;-->
<!--//}-->
