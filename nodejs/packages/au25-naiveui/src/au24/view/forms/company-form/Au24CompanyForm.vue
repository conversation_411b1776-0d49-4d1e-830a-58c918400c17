<template>
	<n-form
		ref="formRef"
		:model="formValue"
		:rules="rules"
		label-placement="left"
		label-width="auto"
		require-mark-placement="right-hanging"
		@submit.prevent="handleSubmit"
	>
		<n-form-item label="Company Full Name" path="company_longname">
			<n-input v-model:value="formValue.company_longname" placeholder="Enter full company name" />
		</n-form-item>
		<n-form-item label="Company Short Name" path="company_shortname">
			<n-input v-model:value="formValue.company_shortname" placeholder="Enter company short name" />
		</n-form-item>
		<n-space justify="end">
			<n-button @click="$emit('cancel')">Cancel</n-button>
			<n-button type="primary" attr-type="submit">Save</n-button>
		</n-space>
	</n-form>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { FormInst, FormRules, NButton, NForm, NFormItem, NInput, NSpace } from "naive-ui"

interface CompanyElement {
	company_longname: string
	company_shortname: string
}

const props = defineProps<{
	company: CompanyElement
}>()

const emit = defineEmits<{
	(e: "save", company: CompanyElement): void
	(e: "cancel"): void
}>()

const formRef = ref<FormInst | null>(null)
const formValue = ref({ ...props.company })

const rules: FormRules = {
	company_longname: [
		{ required: true, message: "Please input the full company name" },
		{ min: 2, message: "Company name should be at least 2 characters" }
	],
	company_shortname: [
		{ required: true, message: "Please input the company short name" },
		{
			min: 2,
			max: 10,
			message: "Short name should be between 2 and 10 characters"
		}
	]
}

const handleSubmit = (e: MouseEvent) => {
	e.preventDefault()
	formRef.value?.validate(errors => {
		if (!errors) {
			emit("save", formValue.value as CompanyElement)
		} else {
			console.log(errors)
		}
	})
}
</script>
