<template>
	<n-form
		ref="formRef"
		:model="formValue"
		:rules="rules"
		label-placement="left"
		label-width="auto"
		require-mark-placement="right-hanging"
		@submit.prevent="handleSubmit"
	>
		<n-form-item label="Username" path="username">
			<n-input v-model:value="formValue.username" placeholder="Enter username" />
		</n-form-item>
		<n-form-item label="Company" path="company_longname">
			<n-input v-model:value="formValue.company_longname" placeholder="Enter company name" />
		</n-form-item>
		<n-form-item label="Role" path="role">
			<n-select v-model:value="formValue.role" :options="roleOptions" />
		</n-form-item>
		<n-space justify="end">
			<n-button @click="$emit('cancel')">Cancel</n-button>
			<n-button type="primary" attr-type="submit">Save</n-button>
		</n-space>
	</n-form>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { FormInst, FormRules, NButton, NForm, NFormItem, NInput, NSelect, NSpace } from "naive-ui"
import { UserElement } from "@/au24/types/generated.ts"

enum AuUserRole {
	AUCTIONEER = "AUCTIONEER",
	TRADER = "TRADER"
}

const props = defineProps<{
	user: UserElement
}>()

const emit = defineEmits<{
	(e: "save", user: UserElement): void
	(e: "cancel"): void
}>()

const formRef = ref<FormInst | null>(null)
const formValue = ref({ ...props.user })

const roleOptions = computed(() => [
	{ label: "Auctioneer", value: AuUserRole.AUCTIONEER },
	{ label: "Trader", value: AuUserRole.TRADER }
])

const rules: FormRules = {
	username: [
		{ required: true, message: "Please input the username" },
		{ min: 3, message: "Username should be at least 3 characters" }
	],
	company_longname: [
		{
			required: true,
			message: "Please input the company name"
		}
	],
	role: [{ required: true, message: "Please select a role" }]
}

const handleSubmit = (e: MouseEvent) => {
	e.preventDefault()
	formRef.value?.validate(errors => {
		if (!errors) {
			emit("save", formValue.value as UserElement)
		} else {
			console.log(errors)
		}
	})
}
</script>
