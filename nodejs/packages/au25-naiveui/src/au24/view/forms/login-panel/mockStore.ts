import { ref } from 'vue'

export const useStore = () => {
  const isAuthenticated = ref(false)
  const currentUser = ref(null)

  const login = async (username: string, password: string) => {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Accept any credentials for demo purposes
    isAuthenticated.value = true
    currentUser.value = { username }
    return true
  }

  const logout = () => {
    isAuthenticated.value = false
    currentUser.value = null
  }

  return {
    isAuthenticated,
    currentUser,
    login,
    logout
  }
}