<template>
  <div class="demo-container">
    <Au24LoginPanel />
    <div v-if="store.isAuthenticated" class="status-panel">
      <n-alert type="success" title="Login Status">
        Logged in as: {{ store.currentUser?.username }}
        <template #action>
          <n-button size="small" @click="store.logout">
            Logout
          </n-button>
        </template>
      </n-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NAlert, NButton } from 'naive-ui'
import Au24LoginPanel from './Au24LoginPanel.vue'
import { useStore } from './mockStore.ts'

const store = useStore()
</script>

<style scoped>
.demo-container {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  min-height: 100vh;
  background: var(--n-color);
}

.status-panel {
  width: 300px;
  margin-top: 1rem;
}
</style>
