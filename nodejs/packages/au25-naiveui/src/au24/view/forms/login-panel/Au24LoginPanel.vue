<template>
  <n-card style="width: 300px; margin: 0 auto;">
    <n-h1>Login</n-h1>
    <n-form
      ref="formRef"
      :model="formValue"
      :rules="rules"
      @submit.prevent="handleSubmit"
    >
      <n-form-item path="username" label="Username">
        <n-input v-model:value="formValue.username" placeholder="Enter your username" />
      </n-form-item>
      <n-form-item path="password" label="Password">
        <n-input
          v-model:value="formValue.password"
          type="password"
          placeholder="Enter your password"
          show-password-on="click"
        />
      </n-form-item>
      <n-form-item>
        <n-button type="primary" attr-type="submit" :loading="isLoading" block>
          {{ isLoading ? 'Logging in...' : 'Login' }}
        </n-button>
      </n-form-item>
    </n-form>
    <n-space justify="space-between">
      <n-button @click="login('admin')" size="small">Login as Admin</n-button>
      <n-button @click="login('trader')" size="small"><PERSON>gin as Trader</n-button>
    </n-space>
    <n-alert v-if="error" type="error" :title="error" style="margin-top: 16px;" />
  </n-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { FormInst } from 'naive-ui'
import { NCard, NH1, NForm, NFormItem, NInput, NButton, NSpace, NAlert } from 'naive-ui'
import { useStore } from './mockStore.ts'

const store = useStore()

const formRef = ref<FormInst | null>(null)
const formValue = ref({
  username: '',
  password: ''
})
const isLoading = ref(false)
const error = ref('')

const rules = {
  username: {
    required: true,
    message: 'Please enter your username',
    trigger: 'blur'
  },
  password: {
    required: true,
    message: 'Please enter your password',
    trigger: 'blur'
  }
}

const login = async (userRole: string) => {
  formValue.value.username = userRole
  formValue.value.password = "password"
  handleSubmit()
}

const handleSubmit = async () => {
  isLoading.value = true
  error.value = ''
  try {
    await formRef.value?.validate()
    const success = await store.login(formValue.value.username, formValue.value.password)
    if (!success) {
      error.value = 'Invalid username or password'
    }
  } catch (e) {
    error.value = 'An error occurred during login'
  } finally {
    isLoading.value = false
  }
}
</script>
