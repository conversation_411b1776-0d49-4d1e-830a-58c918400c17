<template>
	<div>
		<NButton @click="login">login</NButton>
		<div>{{ isAuctioneer }}</div>
		<div>last message received: {{ seconds_ago }} seconds ago</div>
		<Au24AuctionTable :auctions="auctions" :isAuctioneer="isAuctioneer"></Au24AuctionTable>
	</div>
</template>

<script setup lang="ts">
import { login_command } from "@/au24/types/generated.ts"
import { provide } from "vue"
import { createClientConnector } from "@/au24/connector/connector.ts"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"
import { createDemo_SessionUser_Auctioneer } from "@/au24/helpers/demo-helpers/SessionUserValue.helper.ts"
import { Au24AuctionTable } from "@/au24/view/tables/au24-auction-table/Au24AuctionTable.vue"

const connector = createClientConnector({
	url: "ws://localhost:4040/socket/1", //websocket_url,
	show_connector_log: true // or false, depending on your needs
})

provide("connector", connector)

const { auctions, isAuctioneer, seconds_ago } = useLiveStore()

// window.authStore = useAuthStore()
// window.liveStore = useLiveClientStore()

function login() {
	connector.publish(login_command({ username: "a", password: "1" }))
	liveClientStore.session_user = createDemo_SessionUser_Auctioneer()
}
</script>

<style scoped></style>
