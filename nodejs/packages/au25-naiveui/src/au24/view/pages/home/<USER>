<template>
	<div>
		<AuctionTable :auctions="auction_rows" :isAuctioneer="isAuctioneerOrAdmin"></AuctionTable>
	</div>
</template>
<script setup lang="ts">
import AuctionTable from "@/au24/view/tables/au24-auction-table/.old/AuctionTable.vue"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"

const { auction_rows, isAuctioneerOrAdmin } = useLiveStore()
</script>
<style scoped></style>
