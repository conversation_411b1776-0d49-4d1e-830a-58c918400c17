<template>
	<div>
		<div>
			<NButton @click="addTestAuctionRow">Add auction</NButton>
			<
		</div>
		<HomePage></HomePage>
	</div>
</template>

<script setup lang="ts">
import HomePage from "@/au24/view/pages/home/<USER>"
import { onMounted } from "vue"
import { useLiveStore } from "@/au24/stores/live-client-store.js"

const { addTestAuctionRow } = useLiveStore()

onMounted(() => {
	console.log("HomePageDemo onMounted")
	setInterval(() => {
		addTestAuctionRow()
	}, 1000)
})
</script>

<style scoped></style>
