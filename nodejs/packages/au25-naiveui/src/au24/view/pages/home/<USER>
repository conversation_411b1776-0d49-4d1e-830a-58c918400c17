<template>
	<PageLayout>
		<template #header>
			<NFlex justify="space-between">
				<n-h4>Auctions</n-h4>
				<n-button-group>
					<n-button ghost size="small">
						<!--						<template #icon>-->
						<!--							<n-icon>-->
						<!--								<log-in-icon />-->
						<!--							</n-icon>-->
						<!--						</template>-->
						All
					</n-button>
					<n-button size="small">
						<!--						<template #icon>-->
						<!--							<n-icon>-->
						<!--								<log-in-icon />-->
						<!--							</n-icon>-->
						<!--						</template>-->
						Open
					</n-button>
					<n-button size="small">
						<!--						<template #icon>-->
						<!--							<n-icon>-->
						<!--								<log-in-icon />-->
						<!--							</n-icon>-->
						<!--						</template>-->
						Closed
					</n-button>
				</n-button-group>
				<NButton type="primary" size="small">New Auction</NButton>
			</NFlex>
		</template>

		<!-- Your main content goes here -->
		<div>Main Content</div>

		<template #footer>
			<!-- Your custom footer content goes here -->
			<p>Custom Footer</p>
		</template>
	</PageLayout>
</template>

<script setup lang="ts">
import PageLayout from "@/au24/view/layouts/PageLayout.vue"

const LogInIcon = "ion:log-in-outline"
</script>

<style scoped></style>
