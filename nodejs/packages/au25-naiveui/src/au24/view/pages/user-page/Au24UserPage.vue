<template>
	<div class="user-page">
		<h1>User Management</h1>
		<Au24UserTable :users="users" :height="400" :width="800" @rowClick="handleUserClick" />
		<n-modal v-model:show="showModal">
			<n-card
				style="width: 600px"
				title="Edit User"
				:bordered="false"
				size="huge"
				role="dialog"
				aria-modal="true"
			>
				<Au24UserForm
					v-if="selectedUser"
					:user="selectedUser"
					@save="handleSaveUser"
					@cancel="showModal = false"
				/>
			</n-card>
		</n-modal>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { NCard, NModal } from "naive-ui"
import { UserElement } from "@/au24/types/generated.ts"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"
import { userController } from "@/au24/commands/outgoing"
import Au24UserForm from "@/au24/view/forms/user-form/Au24UserForm.vue"
import Au24UserTable from "@/au24/view/tables/au24-user-table/Au24UserTable.vue"

const { users } = useLiveStore()
const showModal = ref(false)
const selectedUser = ref<UserElement | null>(null)

const handleUserClick = (user: UserElement) => {
	selectedUser.value = { ...user }
	showModal.value = true
	userController.user_selected(user.id)
}

const handleSaveUser = async (updatedUser: UserElement) => {
	try {
		await userController.update_user(updatedUser)
		showModal.value = false
	} catch (error) {
		console.error("Failed to update company:", error)
		// Error handling is now managed by the ErrorDialog component
	}
}
</script>

<style scoped>
.user-page {
	padding: 20px;
}
</style>
