<template>
	<Au24UserPage></Au24UserPage>
</template>

<script setup lang="ts">
import Au24UserPage from "@/au24/view/pages/user-page/Au24UserPage.vue"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"
import { onMounted } from "vue"
import _ from "lodash"
import { createDemo__UserElement_for_trader } from "@/au24/helpers/demo-helpers/UserElement.helper.ts"

const store = useLiveStore()

onMounted(() => {
	store.users.value = _.times(30, createDemo__UserElement_for_trader)
})
</script>

<style scoped></style>
