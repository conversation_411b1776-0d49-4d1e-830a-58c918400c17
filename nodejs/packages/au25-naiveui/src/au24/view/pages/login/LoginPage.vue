<template>
	<div class="page-auth">
		<!--		<AuSettings v-model:align="align" v-model:activeColor="activeColor" v-if="!isLogged" />-->
		<!--		<NetworkPanel style="border: 1px solid blue"></NetworkPanel>-->
		<div>
			{{ isLogged }}
			<NButton @click="login_a1">login: a1</NButton>
		</div>

		<div class="flex wrapper justify-center" v-if="!isLogged">
			<div class="image-box basis-2/3" v-if="align === 'right'"></div>
			<div class="form-box basis-1/3 flex items-center justify-center" :class="{ centered: align === 'center' }">
				<AuthForm :type="type" />
			</div>
			<div class="image-box basis-2/3" v-if="align === 'left'"></div>
		</div>
	</div>
</template>

<script lang="ts" setup>
import { inject, onBeforeMount, ref, toRefs } from "vue"
import { useRoute } from "vue-router"
import AuthForm, { FormType } from "@/au24/view/pages/login/AuthForm/index.vue"
import { Align } from "@/au24/view/pages/login/AuthForm/AuSettings.vue"
import { ClientConnectorType } from "@/au24/connector/connector.ts"
import { login_command } from "@/au24/types/generated.ts"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"

const props = defineProps<{
	formType?: FormType
}>()
const { formType } = toRefs(props)

const route = useRoute()
const align = ref<Align>("left")
const activeColor = ref("")
const type = ref<FormType | undefined>(formType.value || undefined)

// const authStore = useAuthStore()
// const isLogged = computed(() => {
// 	//console.log('isLogged', authStore.isLogged)
// 	//return authStore.isLogged
// 	return false
// })

// const liveClientStore = useLiveClientStore()
// const { isLogged } = storeToRefs(liveClientStore)
// const { setLoggedIn } = liveClientStore

const { isLogged } = useLiveStore()

/**/
const connector: ClientConnectorType | undefined = inject("connector")

const login_a1 = () => {
	connector?.publish(login_command({ username: "a1", password: "1" }))
	// useLiveClientStore().session_user = { ...createDemo__SessionUserValue(), isAuctioneer: true, role:AuUserRole.AUCTIONEER }
	//setLoggedIn()
}

onBeforeMount(() => {
	if (route.query.step) {
		const step = route.query.step as FormType
		type.value = step
	}
})
</script>

<style lang="scss" scoped>
@import "./main.scss";

.page-auth {
	.wrapper {
		.image-box {
			background-color: v-bind(activeColor);
		}
	}
}
</style>
