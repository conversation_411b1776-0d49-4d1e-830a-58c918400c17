<template>
	<n-form ref="formRef" :model="model" :rules="rules">
		<n-form-item path="email" label="Email">
			<n-input
				v-model:value="model.email"
				@keydown.enter="signIn"
				placeholder="<EMAIL>"
				size="large"
				autocomplete="on"
			/>
		</n-form-item>
		<n-form-item path="password" label="Password">
			<n-input
				v-model:value="model.password"
				type="password"
				show-password-on="click"
				placeholder="At least 8 characters"
				@keydown.enter="signIn"
				autocomplete="on"
				size="large"
			/>
		</n-form-item>
		<div class="flex flex-col items-end gap-6">
			<div class="flex justify-between w-full">
				<n-checkbox size="large">Remember me</n-checkbox>
				<n-button text type="primary" @click="emit('forgot-password')">Forgot Password?</n-button>
			</div>
			<div class="w-full">
				<n-button type="primary" @click="signIn" class="!w-full" size="large">Sign in</n-button>
			</div>
		</div>
	</n-form>
</template>

<script lang="ts" setup>
import { ref } from "vue"

import {
	type FormInst,
	type FormRules,
	type FormValidationError,
	NButton,
	NCheckbox,
	NForm,
	NFormItem,
	NInput,
	useMessage
} from "naive-ui"
import { useRouter } from "vue-router"
import { useAuAuthStore } from "@/au24/stores/au-auth-store"
import { PageName } from "@/au24/types/generated.js"

interface ModelType {
	email: string | null
	password: string | null
}

const router = useRouter()
const formRef = ref<FormInst | null>(null)
const message = useMessage()
const model = ref<ModelType>({
	email: "<EMAIL>",
	password: "password"
})

const emit = defineEmits<{
	(e: "forgot-password"): void
}>()

const rules: FormRules = {
	email: [
		{
			required: true,
			trigger: ["blur"],
			message: "Email is required"
		}
	],
	password: [
		{
			required: true,
			trigger: ["blur"],
			message: "Password is required"
		}
	]
}

const auAuthStore = useAuAuthStore()

function signIn(e: Event) {
	e.preventDefault()
	if (auAuthStore.is_demo) {
		alert("is demo")
		return
	}
	formRef.value?.validate((errors: Array<FormValidationError> | undefined) => {
		if (!errors) {
			if (model.value.email === "a" && model.value.password === "a") {
				liveStore.setPage(PageName.HOME_PAGE)
				//router.push({ path: "/home", replace: false })
			} else {
				message.error("Invalid credentials")
			}
		} else {
			message.error("Invalid credentials")
		}
	})
}
</script>
