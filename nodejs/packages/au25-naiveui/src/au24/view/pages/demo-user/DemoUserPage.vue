<template>
	<div>
		<h2>User Management</h2>
		<form @submit.prevent="addUser">
			<input v-model="newUser.name" placeholder="Name" />
			<input v-model="newUser.email" placeholder="Email" />
			<button type="submit">Add User</button>
		</form>
		<table>
			<tr v-for="user in userStore.users" :key="user.id">
				<td>{{ user.name }} - {{ user.email }}</td>
				<td>
					<NButton @click="editUser(user)">Edit</NButton>
				</td>
				<td>
					<NButton @click="userStore.deleteUser(user.id)">Delete</NButton>
				</td>
			</tr>
		</table>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { useUserStore } from "@/au24/stores/demo-user-store.js"

interface User {
	id: number
	name: string
	email: string
}

const userStore = useUserStore()
const newUser = ref<Omit<User, "id">>({ name: "", email: "" })

function addUser() {
	userStore.addUser({ ...newUser.value })
	newUser.value = { name: "", email: "" }
}

function editUser(user: User) {
	// Implement the logic to edit the user, e.g., show a modal or form
	console.log("Editing user:", user)
}
</script>
