<template>
	<n-layout>
		<n-layout-header>
			<div>
				<span>Auction Name</span>
				<span>CATEGORY_NAME - Thu Apr 25 2024 01:15:20 GMT-0400 (Eastern Daylight Time)</span>
			</div>
			<div>
				<n-button>Auction Rules</n-button>
				<n-button>Auction Settings</n-button>
			</div>
		</n-layout-header>
		<n-layout-content>
			<n-grid :cols="3">
				<n-gi>
					<n-card title="Round">
						<div>{{ roundStatus }}</div>
						<div>Round Price: {{ roundPrice }}</div>
						<div>Current Order: {{ currentOrder }}</div>
						<div>
							<n-input-number v-model:value="newOrder" />
							<n-button type="primary">Buy</n-button>
							<n-button>Sell</n-button>
						</div>
						<div>Round Constraints: {{ roundConstraints }}</div>
					</n-card>
				</n-gi>
				<n-gi :span="2">
					<n-card title="Round History">
						<n-data-table :data="roundHistory" :columns="columns" />
					</n-card>
				</n-gi>
			</n-grid>
		</n-layout-content>
		<n-layout-footer>
			<n-input v-model:value="message" placeholder="Type message to auctioneer" />
		</n-layout-footer>
	</n-layout>
</template>

<script setup lang="ts">
import { ref } from "vue"

const roundStatus = ref("Waiting for starting price")
const roundPrice = ref("waiting")
const currentOrder = ref(0)
const newOrder = ref(33)
const roundConstraints = ref("Buy: none Sell: 0 to 50 MMLB")
const roundHistory = ref([
	{
		round: 1,
		price: "waiting",
		constraints: "||||||||",
		order: "---",
		value: 0,
		submittedBy: "(default)",
		excess: ""
	}
])
const columns = [
	{ title: "Round", key: "round" },
	{ title: "Price (cpd)", key: "price" },
	{ title: "Constraints (MMLb)", key: "constraints" },
	{ title: "Order (MMLb)", key: "order" },
	{ title: "Value", key: "value" },
	{ title: "Submitted by (bidder)", key: "submittedBy" },
	{ title: "Excess (indicator)", key: "excess" }
]
const message = ref("")
</script>
