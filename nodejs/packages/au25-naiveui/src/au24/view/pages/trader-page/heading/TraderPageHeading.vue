<template>
	<n-layout-header>
		<div>
			<span>Round</span>
			<span>Status</span>
			<span>Round Price</span>
			<span>Current Order</span>
			<span>New Order</span>
			<span>Round Constraints</span>
		</div>
		<div>
			<span>1</span>
			<span>Waiting for starting price</span>
			<span>waiting cpp</span>
			<span>--- 0 Side MMLB</span>
			<span>
				<n-input-number v-model:value="newOrder" :step="1" :min="0" :max="50" :precision="0" />
				<!--				:show-button />-->

				<n-button type="primary">Buy</n-button>
				<n-button>Sell</n-button>
			</span>
			<span>Buy: none Sell: 0 to 50 MMLB</span>
			<!--			<NProgress-->
			<!--				:percentage="100"-->
			<!--				:rail-style="{ background: '#333' }"-->
			<!--				:fill-style="{ background: '#f00' }"-->
			<!--				:height="10"-->
			<!--				:border-radius="5"-->
			<!--				:processing="true"-->
			<!--				:show-indicator="false"-->
			<!--			></NProgress>-->
		</div>
	</n-layout-header>
</template>

<script setup lang="ts">
import { ref } from "vue"

const newOrder = ref(33)
</script>
