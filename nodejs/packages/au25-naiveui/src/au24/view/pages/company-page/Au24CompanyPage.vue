<!-- Au24CompaniesPage.vue -->
<template>
	<div class="company-page">
		<h1>Company Management</h1>
		<Au24CompanyTable :companies="companies" :height="400" :width="800" @rowClick="handleCompanyClick" />
		<n-modal v-model:show="showModal" class="au24-dialog">
			<n-card title="Edit Company" :bordered="false" size="huge" role="dialog" aria-modal="true">
				<Au24CompanyForm
					v-if="selectedCompany"
					:company="selectedCompany"
					@save="handleSaveCompany"
					@cancel="showModal = false"
				/>
			</n-card>
		</n-modal>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { NCard, NModal } from "naive-ui"
import { CompanyElement } from "@/au24/types/generated.ts"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"
import { companyController } from "@/au24/commands/outgoing"
import Au24CompanyForm from "@/au24/view/forms/company-form/Au24CompanyForm.vue"
import Au24CompanyTable from "@/au24/view/tables/au24-company-table/Au24CompanyTable.vue"

const { companies } = useLiveStore()
const showModal = ref(false)
const selectedCompany = ref<CompanyElement | null>(null)

const handleCompanyClick = (company: CompanyElement) => {
	selectedCompany.value = { ...company }
	showModal.value = true
	companyController.company_selected(company.id)
}

const handleSaveCompany = async (updatedCompany: CompanyElement) => {
	try {
		await companyController.update_company(updatedCompany)
		showModal.value = false
	} catch (error) {
		console.error("Failed to update company:", error)
		// Error handling is now managed by the ErrorDialog component
	}
}
</script>

<style scoped>
.company-page {
	padding: 20px;
}
</style>
