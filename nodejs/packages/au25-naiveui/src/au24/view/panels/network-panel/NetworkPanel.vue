<template>
	<div>
		<h1>Network Panel</h1>
		<p>isOnline: {{ isOnline }}</p>
		<p>offlineAt: {{ offlineAt }}</p>
		<p>downlink: {{ downlink }}</p>
		<p>downlinkMax: {{ downlinkMax }}</p>
		<p>effectiveType: {{ effectiveType }}</p>
		<p>saveData: {{ saveData }}</p>
		<p>type: {{ type }}</p>
	</div>
</template>

<script setup lang="ts">
import { useNetwork } from "@vueuse/core"

const { isOnline, offlineAt, downlink, downlinkMax, effectiveType, saveData, type } = useNetwork()

console.log(isOnline.value)
</script>

<style scoped></style>
