<template>
	<!--	<CardCombo1></CardCombo1>-->
	<div>
		<NInput value="Auction: ..."></NInput>
		<NInput value="Status: Waiting for starting price"></NInput>
		<NSpace>
			<CardCombo21 title="Round"></CardCombo21>
			<CardCombo21 title="Price"></CardCombo21>
			<CardCombo21 title="Clock"></CardCombo21>
			<CardCombo21 title="Order"></CardCombo21>
			<CardCombo21 title="Enter"></CardCombo21>
			<CardCombo21 title="Constraints"></CardCombo21>
		</NSpace>
	</div>
	<!--	<CardCombo3></CardCombo3>-->
	<CardCombo4 title="combo4"></CardCombo4>
	<!--	<CardCombo5></CardCombo5>-->
	<CardCombo6></CardCombo6>
	<CardCombo7></CardCombo7>
	<CardCombo8></CardCombo8>
</template>

<script setup lang="ts">
import CardCombo21 from "@/components/cards/combo/CardCombo21.vue"
import { NInput } from "naive-ui"
import CardCombo4 from "@/components/cards/combo/CardCombo4.vue"
import CardCombo6 from "@/components/cards/combo/CardCombo6.vue"
import CardCombo7 from "@/components/cards/combo/CardCombo7.vue"
import CardCombo8 from "@/components/cards/combo/CardCombo8.vue"
</script>

<style scoped></style>
