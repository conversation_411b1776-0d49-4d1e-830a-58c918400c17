<template>
	<NCard :bordered="false" class="round-status">
		<NGrid :cols="7" :x-gap="4">
			<NGi>
				<div class="round-number">{{ round }}</div>
			</NGi>
			<NGi>
				<NTag type="info" size="small">
					{{ status }}
				</NTag>
			</NGi>
			<NGi>
				<div class="price">
					<div>{{ roundPrice }}</div>
					<div class="currency">cpp</div>
				</div>
			</NGi>
			<NGi>
				<div class="current-order">
					<div>{{ currentOrderSide }}</div>
					<div>{{ currentOrderAmount }}</div>
				</div>
			</NGi>
			<NGi>
				<NInputNumber v-model:value="newOrderAmount" :min="0" :max="maxOrderAmount" placeholder="MMlb" />
			</NGi>
			<NGi>
				<NButtonGroup vertical>
					<NButton @click="placeOrder('Buy')" :disabled="isOrderDisabled">Buy</NButton>
					<NButton @click="placeOrder('Sell')" :disabled="isOrderDisabled">Sell</NButton>
				</NButtonGroup>
			</NGi>
			<NGi>
				<NSlider
					:min="0"
					:max="50"
					:marks="{
						0: '0',
						10: '10',
						20: '20',
						30: '30',
						40: '40',
						50: '50'
					}"
					:default-value="0"
					:disabled="true"
				>
					<template #thumb>
						<div class="custom-thumb">
							<div class="indicator buy" :style="{ width: `${buyWidth}px` }"></div>
							<div class="indicator sell" :style="{ width: `${sellWidth}px` }"></div>
						</div>
					</template>
				</NSlider>
				<div class="constraints">
					<span class="buy">Buy: 0 to 227 MMlb</span>
					<span class="sell">Sell: 0 to 50 MMlb</span>
				</div>
			</NGi>
		</NGrid>
	</NCard>
</template>

<script setup lang="ts">
import { computed, ref } from "vue"
import { NButton, NButtonGroup, NCard, NGi, NGrid, NInputNumber, NSlider, NTag } from "naive-ui"

const round = ref(1)
const status = ref("Round 1 closed")
const roundPrice = ref(22.0)
const currentOrderSide = ref("---")
const currentOrderAmount = ref(0)
const newOrderAmount = ref<number | null>(null)

const maxOrderAmount = computed(() => 227) // This could be dynamic based on buy/sell constraints

const isOrderDisabled = computed(() => status.value.includes("closed"))

const buyWidth = ref(40) // Example width, adjust as needed
const sellWidth = ref(10) // Example width, adjust as needed

const placeOrder = (side: "Buy" | "Sell") => {
	// Implement order placement logic here
	console.log(`Placing ${side} order for ${newOrderAmount.value} MMlb`)
}
</script>

<style scoped>
.round-status {
	background-color: #2b2b2b;
	color: white;
}

.round-number {
	font-size: 2em;
	font-weight: bold;
}

.price {
	font-weight: bold;
}

.currency {
	font-size: 0.8em;
	color: #888;
}

.current-order {
	display: flex;
	justify-content: space-between;
}

.custom-thumb {
	width: 100%;
	height: 4px;
	background-color: #444;
	position: relative;
}

.indicator {
	height: 100%;
	position: absolute;
}

.indicator.buy {
	background-color: green;
	left: 0;
}

.indicator.sell {
	background-color: red;
	right: 0;
}

.constraints {
	display: flex;
	justify-content: space-between;
	font-size: 0.8em;
	margin-top: 4px;
}

.constraints .buy {
	color: green;
}

.constraints .sell {
	color: red;
}
</style>
