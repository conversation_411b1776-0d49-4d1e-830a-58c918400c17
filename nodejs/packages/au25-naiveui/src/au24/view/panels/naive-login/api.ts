
// Simulated delay for API calls
import type { Auction, User } from "@/au24/view/panels/naive-login/login-types"

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// Mock user data
const users: User[] = [
  { id: '1', username: 'admin', email: '<EMAIL>', role: 'ADMIN' },
  { id: '2', username: 'trader', email: '<EMAIL>', role: 'TRADER' },
]

// Mock auction data
let auctions: Auction[] = [
  { id: 1, name: 'Auction 1', status: 'Active' },
  { id: 2, name: 'Auction 2', status: 'Upcoming' },
  { id: 3, name: 'Auction 3', status: 'Closed' },
]

export const api = {
  async login(username: string, password: string): Promise<User | null> {
    await delay(500) // Simulate API delay
    const user = users.find(u => u.username === username)
    if (user && password === 'password') { // Simple password check
      return user
    }
    return null
  },

  async getAuctions(): Promise<Auction[]> {
    await delay(500)
    return auctions
  },

  async createAuction(auction: Omit<Auction, 'id'>): Promise<Auction> {
    await delay(500)
    const newAuction = { ...auction, id: Date.now() }
    auctions.push(newAuction)
    return newAuction
  },

  async updateAuction(auction: Auction): Promise<Auction> {
    await delay(500)
    const index = auctions.findIndex(a => a.id === auction.id)
    if (index !== -1) {
      auctions[index] = auction
      return auction
    }
    throw new Error('Auction not found')
  },
}
