import { defineStore } from 'pinia'
import { api } from './api'
import type { Auction, User } from "@/au24/view/panels/naive-login/login-types"

interface State {
	user: User | null;
	auctions: Auction[];
}

export const useStore = defineStore('main', {
	state: (): State => ({
		user: null,
		auctions: []
	}),
	actions: {
		async login(username: string, password: string) {
			const user = await api.login(username, password)
			if (user) {
				this.user = user
				return true
			}
			return false
		},
		logout() {
			this.user = null
		},
		async fetchAuctions() {
			const auctions = await api.getAuctions()
			this.auctions = auctions
			return auctions
		},
		async addAuction(auction: Omit<Auction, 'id'>) {
			const newAuction = await api.createAuction(auction)
			this.auctions.push(newAuction)
		},
		async updateAuction(auction: Auction) {
			const updatedAuction = await api.updateAuction(auction)
			const index = this.auctions.findIndex(a => a.id === updatedAuction.id)
			if (index !== -1) {
				this.auctions[index] = updatedAuction
			}
		}
	},
	getters: {
		isLoggedIn: (state): boolean => !!state.user,
		userRole: (state): string | undefined => state.user?.role,
		activeAuctions: (state): Auction[] => state.auctions.filter(auction => auction.status === 'Active')
	}
})
