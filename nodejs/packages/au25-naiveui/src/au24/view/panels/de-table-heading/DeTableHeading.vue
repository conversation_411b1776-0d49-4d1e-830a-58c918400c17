<template>
	<div class="TableHeading au-label">
		<div class="_inner">
			<slot></slot>
		</div>
	</div>
</template>

<script setup lang="ts">
// No additional logic needed for this component
</script>

<style lang="less" scoped>
@import (reference) "../../../au-styles/variables.less";

.TableHeading {
	background-color: @au-background !important;
	height: 24px;
	width: 100%;

	._inner {
		margin-left: 10px;
		text-align: center;
	}
}
</style>
