<template>
	<n-input type="text" placeholder="suffix" v-model:value="suffix" />
	<n-input placeholder="decimal places" v-model:value="decimal_places" />
	<!--	<n-input-group>-->
	<!--		<n-input-group-label>Minimum Quantity:</n-input-group-label>-->
	<!--		<n-input :style="{ width: '33%' }" placeholder="" />-->
	<!--		<n-input-group-label style="width: 150px">{{ suffix }}</n-input-group-label>-->
	<!--	</n-input-group>-->

	<ReactiveExp1 :suffix="suffix" :decimal_places="decimal_places" />
</template>

<script setup lang="ts">
import ReactiveExp1 from "@/au24/view/experiments/ReactiveExp1.vue"
import { ref } from "vue"
import { NInput } from "naive-ui"

const suffix = ref("")

const decimal_places = ref(0)

// Import the 'computed' function from Vue
</script>

<style scoped></style>
