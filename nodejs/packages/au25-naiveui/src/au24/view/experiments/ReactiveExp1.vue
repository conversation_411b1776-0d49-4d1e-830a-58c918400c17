<template>
	<div>
		ReactiveExp1
		<br />
		suffix: {{ suffix }}
		<br />
		decimal places: {{ decimal_places }}
		<Au24NumberInput v-model:value="value" />
	</div>
</template>

<script setup lang="ts">
import Au24NumberInput from "@/au24/view/ui-component/au24-number-input/Au24NumberInput.vue"

const prefix = "" // "$"

const { suffix = "", decimal_places = 0 } = defineProps<{
	suffix?: string
	decimal_places?: number
}>()

// const suffix = computed(() => props.suffix ?? "")
// const decimal_places = computed(() => props.decimal_places ?? 0)
</script>

<style scoped></style>
