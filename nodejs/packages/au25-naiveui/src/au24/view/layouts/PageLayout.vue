<template>
	<n-layout :style="{ height: contentHeight + 'px' }">
		<n-layout-header style="height: 64px; padding: 24px" bordered>
			<slot name="header"></slot>
		</n-layout-header>
		<n-layout position="absolute" style="top: 64px; bottom: 64px">
			<slot></slot>
		</n-layout>
		<n-layout-footer position="absolute" style="height: 64px; padding: 24px" bordered>
			<slot name="footer"></slot>
		</n-layout-footer>
	</n-layout>
</template>

<script setup lang="ts">
import { computed } from "vue"
import { useWindowSize } from "@vueuse/core"

const { height, width } = useWindowSize()

const headerHeight = 100 // Adjust this value based on your header height
const scrollbarHeight = 20 // Adjust this value based on the scrollbar height
const footerHeight = 60
const minHeight = 300

const contentHeight = computed(() =>
	Math.max(minHeight, height.value - (headerHeight + scrollbarHeight + footerHeight))
)

//const contentHeight = computed(() => {
// 	const height = windowHeight.value = Math.max(
// 		headerHeight + scrollbarHeight + footerHeight,
// 		minHeight
// 	)
// 	console.log('contentHeight:', height)
// 	return height
// })
</script>

<style scoped></style>
