// useScrollbarSize.js
import { onMounted, onUnmounted, ref } from "vue"

export function useScrollbarSize() {
	const scrollbarWidth = ref(0)
	const scrollbarHeight = ref(0)

	function calculateScrollbarSize() {
		const div1 = document.createElement("div")
		const div2 = document.createElement("div")

		div1.style.width = "100px"
		div1.style.height = "100px"
		div1.style.overflow = "scroll"
		div1.style.visibility = "hidden"

		div2.style.width = "100%"
		div2.style.height = "100%"

		div1.appendChild(div2)
		document.body.appendChild(div1)

		scrollbarWidth.value = div1.offsetWidth - div2.offsetWidth
		scrollbarHeight.value = div1.offsetHeight - div2.offsetHeight

		document.body.removeChild(div1)
	}

	onMounted(() => {
		calculateScrollbarSize()
		window.addEventListener("resize", calculateScrollbarSize)
	})

	onUnmounted(() => {
		window.removeEventListener("resize", calculateScrollbarSize)
	})

	return {
		scrollbarWidth,
		scrollbarHeight
	}
}
