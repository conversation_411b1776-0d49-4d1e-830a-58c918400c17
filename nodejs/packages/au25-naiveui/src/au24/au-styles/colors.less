/**************************************************************
 * NOTES:
 *
 * 1) This file needs to be kept in sync with ./AuColors.ts
 *
 * 2) Tools:
 *    - https://www.w3schools.com/colors/colors_converter.asp
 *    - https://hslpicker.com
 *
 **************************************************************/

// primary is specified in ant-design-vue-plugin.less

/**
      TODO: MOVE THIS TO colors.less in the lib !
 */
// great color picker: http://hslpicker.com/

@white: #ddd;

@au-red: red;
@light_border: hsl(0, 0%, 80%);

@main-color: hsl(205, 20%, 5%);
//@layout-color: hsl(205, 20%, 30%);
// @layout-color: hsl(230, 10%, 40%); // same as scrollbar thumb
@layout-color: hsl(230, 10%, 15%);

@au_beige: #E4CDA7;
@au_beige_dimmedX2: darken(@au_beige, 55%);

// ROUND:
@au_purple: hsl(262, 71%, 72%); // #7D6A9E;
@au_round: @au_beige; //  @au_purple;

// prev:
// @au_green: #77ce77;
// @au_red: #fa8787;

// sell:
//@au_pink: hsl(331, 95%, 70%); // = #FB6AB0
//@au_sell_color: @au_pink; // @au_red;
//@au_sell_color_dimmed:  darken(@au_sell_color, 5%);
//@au_sell_dimmedX2: alpha(@au_sell_color_dimmed, 35%);
//
//// buy:
//@au_green: hsl(91, 74%, 60%);  // = #96E44E
//@au_buy_color: @au_green;
//@au_buy_color_dimmed: darken(@au_buy_color, 5%);
//@au_buy_dimmedX2: alpha(@au_buy_color_dimmed, 35%);
//
//
//@au_blue: hsla(187, 73.2%, 70.8%, 1);
//@au_match: @au_blue;
//@au_match_dimmedX2: alpha(@au_match, 35%);

// logo
// @logo_purple: hsl(270, 50%, 40%);

// @au_blue


// @au_match: hsl(44, 100%, 42%); //hsl(44, 100%, 50%);
//@price-color: #CC9FF5;
//@price-up-color: #77ce77;
//@price-down-color: #fa8787;

// @au-highlight: hsla(273, 60%, 74%, 1);


// Primary

// @primary-color: @au-primary-color;
@au-primary-color: hsl(77, 10%, 70%); // hsl(44, 100%, 50%);  //#b4aa69;
@au-primary-color-disabled: hsl(77, 5%, 45%);
@au-label-color: @au-primary-color; // darken(@logo_orange, 10%);

// ag grid

@ag-grid-background-color: hsl(197, 7%, 20%);

// other

@au-text-color-secondary: black;
@au-pseudo-input-color: @au-red;
@disabledColor: #aaa;
// Set to discern BWP from Charlize.
@au-body-background: @dark-bg-color; // #080d14;
@au-background-dark: hsl(183, 6%, 11%); //#262626; // #111;
@au-background: hsl(220, 10%, 20%); //#262626; // #111;
@au-background-light: #666666;
@au-background-very-light: #eeeeee;

@inputBorderColor: #9e9c86;
@inputColor: #BCC38A; // #d6d2a7;

@outputColor: #2C3335; // hsl(195, 10%, 16%); //hsl(190, 9%, 26%);  // #cdd5d5; // #bbdaf0;

// inspired by zenkit tron theme:
@dark-bg-color: darken(#303a52, 25%); // rgb(12, 20, 31);
@light-blue-header-color: darken(rgb(33, 98, 130), 15%);
@bright-blue-font-color: rgb(24, 202, 230);
//
@body-background: @au-body-background;
@component-background: #eee;
@background-color-light: #cecece;
@card-head-background: #666666;

//@label_font_color: hsl(44, 58%, 50%) !important;
//@label_font_size: 13px !important;


// TODO Ideally want to get rid of this.
@au-text-color: white;
@au_label_color: darken(${this.logo_orange}, 10%);

// TODO: not sure what this is about,
//  probably need to replace as primary color has different meaning in AntDV context
@primary-color: #585858;

// Modal
@au-modal-background: #383838;

// Notification
@au-notification-background: #111;
@au-notification-error-background: #5e2525;

// Popconfirm
@au-popconfirm-background: #383838;

// OLD:

//@au-body-background: #28324b;
//@au-border-input: #9e9c86;
//@au-padding-large: 16px;
//@au-border-radius: 5px;
//@au-create_scenario_result_rows-background-color: @au-background;
//@au-text-color: white;
//@au-text-color-warning: #ffff00;


//@sell_pink_1: hsl(331, 97%, 45%);
//@buy_green_1: hsl(78, 100%, 33%);
//
//@sell_pink_2: hsl(344, 47%, 51%);
//@buy_green_2: hsl(91, 50%, 48%);
//
//@sell_pink_3: hsl(331, 94%, 45%);
//@buy_green_3: hsl(110, 38%, 51%);
