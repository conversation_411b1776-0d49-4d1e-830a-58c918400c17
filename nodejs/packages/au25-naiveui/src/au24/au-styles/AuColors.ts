/**************************************************************
 * This file needs to be kept in sync with ./colors.less
 *
 * NOTE: styles.less overlaps with this;
 **************************************************************/

//import { OnlyInstantiableByContainer, Singleton } from "typescript-ioc"
import chroma from "chroma-js"
import { OrderType, PriceDirection } from "@/au24/types/generated.js"

// great color picker: http://hslpicker.com/


enum COLOR_THEME {
	RED_GREEN, PINK_GREEN
}

//@Singleton
//@OnlyInstantiableByContainer

export class AuColors {

	// Round:
	au_purple = "hsl(262, 71%, 72%)" //'hsl(262, 21%, 52%)'; //'#7D6A9E'
	au_round = this.au_purple

	// app
	// sell:
	au_red = "hsl(0, 92%, 75%)"
	sell_pink_4 = "hsl(331, 95%, 70%)" // '#FB6AB0';

	//
	// BUY / SELL COLORS:
	//
	// buy:
	au_green = "hsl(120, 47%, 64%)"
	buy_green_4 = "hsl(91, 74%, 60%)"  // '#96E44E';
	// match:
	au_blue = "hsl(187, 50%, 60%)"
	au_blue_bright = "hsla(187, 73.2%, 70.8%, 1)" // '#7EDEEB';
	au_text_color = "ccc"
	private current_theme = COLOR_THEME.RED_GREEN

	switch_theme() {
		this.current_theme = this.current_theme === COLOR_THEME.RED_GREEN ?
			COLOR_THEME.PINK_GREEN : COLOR_THEME.RED_GREEN
	}

	order_bright(side: OrderType | null): string {
		switch (side) {
			case OrderType.BUY:
				return this.au_buy_bright()
			case OrderType.SELL:
				return this.au_sell_bright()
			default:
				return "hsl(0, 0%, 90%)"
		}
	}

	order_quantity_text_color(side: OrderType | null): string {
		switch (side) {
			case OrderType.BUY:
				return this.au_buy()
			case OrderType.SELL:
				return this.au_sell()
			default:
				return this.au_none()
		}
	}

	order_quantity_opposite_text_color(side: OrderType): string {
		switch (side) {
			case OrderType.BUY:
				return this.au_sell()
			case OrderType.SELL:
				return this.au_buy()
			default:
				return this.au_none()
		}
	}

	au_sell() {
		return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_red : this.sell_pink_4
	}

	au_sell_bright() {
		return this.current_theme === COLOR_THEME.RED_GREEN ? "hsl(0, 100%, 60%)" : "hsl(331, 100%, 75%)"
	}

	// TODO Using alpha in colors is not great. As you can't move it with opacity back to 100%.
	au_sell_dim() {
		return chroma(this.au_sell()).alpha(0.50).hex()
	}

	au_sell_dimmedX2() {
		return chroma(this.au_sell()).alpha(0.15).hex()
	}

	au_sell_dark() {
		return chroma(this.au_sell()).luminance(0.1).hex()
	}

	au_buy() {
		return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_green : this.buy_green_4 // this.buy_green_4; // .au_red; //  .au_yellow;
	}

	au_buy_bright() {
		return this.current_theme === COLOR_THEME.RED_GREEN ? "hsl(120, 100%, 50%)" : "hsl(91, 84%, 70%)"
	}

	// NONE:

	au_buy_dimmed() {
		return chroma(this.au_buy()).alpha(0.50).hex()
	}

	au_buy_dimmedX2() {
		return chroma(this.au_buy()).alpha(0.15).hex()
	}

	au_buy_dark() {
		return chroma(this.au_buy()).luminance(0.1).hex()
	}

	au_none() {
		return "hsl(0,0%,90%)"
	}

	au_match() {
		return this.current_theme === COLOR_THEME.RED_GREEN ? this.au_blue : this.au_blue_bright
	} //'#86EBE3'; //'hsl(200, 100%, 42%)'; // 'hsl(44, 100%, 42%)';

	au_match_dimmed() {
		return chroma(this.au_match()).alpha(0.30).hex()
	}

	au_match_dimmedX2() {
		return chroma(this.au_match()).alpha(0.15).hex()
	} // 'hsl(204, 31%, 41%)';

	// EXCESS

	au_match_dark() {
		return chroma(this.au_match()).luminance(0.1).hex()
	}

	// MISC (TODO: move to colors.less)

	au_excess() {
		return "hsl(50, 10%, 60%)"
	}

	getPriceColor(price_direction?: PriceDirection | null): string {
		switch (price_direction) {
			case PriceDirection.DOWN:
				return this.au_sell() // price_down_color;
			case PriceDirection.UP:
				return this.au_buy() // price_up_color;
			default:
				return this.au_text_color
		}
	}
}

export const auColors = new AuColors()

