// Could be optimized with `for each` and lists.
// Not a huge priority if at all though.


@au-spacing-one: 6px;
@au-spacing-two: 12px;
@au-spacing-three: 18px;


.h-spacer-large {
  //  margin-right: 20px !important;
  width: 25px
}


.mt-05 {
  margin-top: (@au-spacing-one/2) !important;
}

.mr-05 {
  margin-right: (@au-spacing-one/2) !important;
}

.mb-05 {
  margin-bottom: (@au-spacing-one/2) !important;
}

.ml-05 {
  margin-left: (@au-spacing-one/2) !important;
}

.mt-1 {
  margin-top: @au-spacing-one !important;
}

.mr-1 {
  margin-right: @au-spacing-one !important;
}

.mb-1 {
  margin-bottom: @au-spacing-one !important;
}

.ml-1 {
  margin-left: @au-spacing-one !important;
}

.mt-2 {
  margin-top: @au-spacing-two !important;
}

.mr-2 {
  margin-right: @au-spacing-two !important;
}

.mb-2 {
  margin-bottom: @au-spacing-two !important;
}

.ml-2 {
  margin-left: @au-spacing-two !important;
}

.mt-3 {
  margin-top: @au-spacing-three !important;
}

.mr-3 {
  margin-right: @au-spacing-three !important;
}

.mb-3 {
  margin-bottom: @au-spacing-three !important;
}

.ml-3 {
  margin-left: @au-spacing-three !important;
}


.pt-05 {
  padding-top: (@au-spacing-one/2) !important;
}

.pr-05 {
  padding-right: (@au-spacing-one/2) !important;
}

.pb-05 {
  padding-bottom: (@au-spacing-one/2) !important;
}

.pl-05 {
  padding-left: (@au-spacing-one/2) !important;
}

.pt-1 {
  padding-top: @au-spacing-one !important;
}

.pr-1 {
  padding-right: @au-spacing-one !important;
}

.pb-1 {
  padding-bottom: @au-spacing-one !important;
}

.pl-1 {
  padding-left: @au-spacing-one !important;
}

.pt-2 {
  padding-top: @au-spacing-two !important;
}

.pr-2 {
  padding-right: @au-spacing-two !important;
}

.pb-2 {
  padding-bottom: @au-spacing-two !important;
}

.pl-2 {
  padding-left: @au-spacing-two !important;
}

.pt-3 {
  padding-top: @au-spacing-three !important;
}

.pr-3 {
  padding-right: @au-spacing-three !important;
}

.pb-3 {
  padding-bottom: @au-spacing-three !important;
}

.pl-3 {
  padding-left: @au-spacing-three !important;
}
