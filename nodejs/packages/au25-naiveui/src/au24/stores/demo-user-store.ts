import { defineStore } from "pinia"

export const useUserStore = defineStore("userStore", {
	state: () => ({
		users: [
			{ id: 1, name: "<PERSON>", email: "<EMAIL>" },
			{ id: 2, name: "<PERSON>", email: "<EMAIL>" }
		]
	}),
	actions: {
		addUser(user) {
			user.id = Date.now()
			this.users.push(user)
		},
		deleteUser(userId) {
			this.users = this.users.filter(user => user.id !== userId)
		},
		editUser(updatedUser) {
			const index = this.users.findIndex(user => user.id === updatedUser.id)
			if (index !== -1) {
				this.users[index] = updatedUser
			}
		}
	}
})
