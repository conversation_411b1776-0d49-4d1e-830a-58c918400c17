// useDimensionStore.js
import { defineStore, storeToRefs } from "pinia"
import { computed } from "vue"
import { useWindowSize } from "@vueuse/core"
import { useScrollbarSize } from "@/au24/composables/useScrollbarSize"
import { useThemeStore } from "@/stores/theme.ts"

const SCROLLBAR_OFFSET = 0 // ? due to borders ?
const MIN_PAGE_HEIGHT = 400
// const TOOLBAR_HEIGHT = 60; // these are in the themeStore
// const FOOTER_HEIGHT = 60;

export const useDimensionStore = defineStore("dimension", () => {
	const { width: windowWidth, height: windowHeight } = useWindowSize()
	const { scrollbarWidth, scrollbarHeight } = useScrollbarSize()

	const { toolbarHeight, footerHeight } = storeToRefs(useThemeStore())

	const pageHeight = computed(
		() => windowHeight.value - toolbarHeight.value - footerHeight.value - scrollbarHeight.value - SCROLLBAR_OFFSET
	)

	return {
		pageHeight
	}
})
