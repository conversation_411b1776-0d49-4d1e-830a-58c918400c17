import { acceptHMRUpdate, defineStore } from "pinia"

export const useAuAuthStore = defineStore("auAuthStore", {
	state: () => ({
		is_logged_in: false,
		is_demo: true
	}),
	actions: {
		logout() {
			this.is_logged_in = false
			// Additional logout logic can be handled here
		},
		login() {
			console.log("AuAuthStore: login")
			this.is_logged_in = true
		},
		persist: true
	}
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useAuAuthStore, import.meta.hot))
}
