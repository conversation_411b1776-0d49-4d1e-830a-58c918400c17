import { defineStore, storeToRefs } from "pinia"
import { type AuctionRowElement, LiveClientStore, PageName } from "@/au24/types/generated.js"
import { createDemo__AuctionRowElement } from "@/au24/helpers/demo-helpers/AuctionRowElement.helper.ts"
import { createDemo__DeAuctionValue_for_auctioneer } from "@/au24/helpers/demo-helpers/DeAuctionValue.helper.ts"
import { duration_ms_str } from "@/au24/utils"
import { isAuctioneerOrAdmin } from "../helpers/entity-helpers/AuUserRole"

export const piniaInternalProps = [
	"/$id",
	"/$state",
	"/$patch",
	"/$reset",
	"/$subscribe",
	"/$onAction",
	"/$dispose",
	"/$getters",
	"/$actions",
	"/_p",
	"/_s",
	"/_getters",
	"/_isOptionsAPI",
	"/_hotUpdate",
	"/_hmrPayload"
]

export const useLiveStore = defineStore('LiveClientStore', {
	state: (): LiveClientStore => ({
		auction_rows: [],
		companies: [],
		counterparty_credits: [],
		de_auction: null,
		seconds_since_last_message_received: 0,
		session_user: null,
		time: null,
		users: []
	}),
	getters: {
		isLogged: state => state.session_user?.role != null,
		isAuctioneerOrAdmin: state => isAuctioneerOrAdmin(state.session_user)
	},
	actions: {
		apply_patch(new_store: LiveClientStore) {
			const start = performance.now()
			this.$patch(new_store)
			console.log(`LiveClientStore patching with $patch took: : ${duration_ms_str(start)}`)
		},

		addTestAuctionRow() {
			const id = this.auction_rows.length + 1
			console.log("creating auction row:", id)
			this.auction_rows.push(createDemo__AuctionRowElement(true, id))
		},

		setTestAuction(auction: AuctionRowElement) {
			this.de_auction = createDemo__DeAuctionValue_for_auctioneer([], [])
			if (this.session_user && this.de_auction?.auction_id) {
				this.setPage(PageName.DE_AUCTIONEER_PAGE, this.de_auction.auction_id)
			}
		},

		setPage(page: PageName, id?: string) {
			if (this.session_user) {
				this.session_user.current_page = page
				if (id && page in [PageName.DE_AUCTIONEER_PAGE, PageName.DE_TRADER_PAGE]) {
					this.session_user.current_auction_id = id
				}
			}
		}
	}
})

if (import.meta.hot) {
	// acceptHMRUpdate is a Pinia helper, but we don't have that:
	// import.meta.hot.accept(acceptHMRUpdate(liveClientStoreComposable, import.meta.hot))
	import.meta.hot.accept(() => {
		// not sure we need to do anything
	})
}
