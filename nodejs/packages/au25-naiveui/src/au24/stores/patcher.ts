import { applyPatch, compare, Operation } from "fast-json-patch"
import { piniaInternalProps } from "@/au24/stores/live-client-store.ts"
import { duration_ms_str } from "@/au24/utils"

export function patch_with_fast_json(oldstore, newstore) {
	// NOTE: if we use fast-json-patch, this will remove various pinia properties!
	//   { "op": "remove", "path": "/_isOptionsAPI" },
	//   { "op": "remove", "path": "/_hotUpdate" },
	//   { "op": "remove", "path": "/$dispose" },
	//   { "op": "remove", "path": "/$subscribe" },
	//   { "op": "remove", "path": "/$reset" },
	//   { "op": "remove", "path": "/$patch" },
	//   { "op": "remove", "path": "/$onAction" },
	//   { "op": "remove", "path": "/$id" }
	// SO, we'll try with $patch, but if it's slow we'll go back to this
	//   - being careful to not patch those methods
	// not sure what we're patching here:

	const start = performance.now()

	const _diff: Operation[] = compare(oldstore, newstore)

	const filteredOperations: Operation[] = _diff.filter(op => !piniaInternalProps.includes(op.path))

	// other patching strategies:
	// patch_store_strategy_1_replace_all_by_key(this.store, c.store)
	// patch_store_strategy_2_jsondffpatcher(this.store, c.store)
	// patchLiveStore(this.store.live_store, c.store);

	applyPatch(oldstore, filteredOperations)
	console.log(`LiveClientStore patching with fast-json-patch took: : ${duration_ms_str(start)}`)

	//console.log('original diff:', JSON.stringify({ _diff }, null, 2))
	//console.log('filtered diff:', JSON.stringify({ filteredOperations }, null, 2))
}
