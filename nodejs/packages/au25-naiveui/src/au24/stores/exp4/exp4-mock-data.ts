import { UserRole, type User, type UserGroup, type Invitation } from '@/au24/stores/exp4/exp4-domain';

export const mockUsers: User[] = [
	{
		id: '1',
		name: '<PERSON>',
		email: '<EMAIL>',
		role: UserRole.ADMIN,
		isRegistered: true,
	},
	{
		id: '2',
		name: '<PERSON>',
		email: '<EMAIL>',
		role: UserRole.MANAGER,
		isRegistered: true,
	},
	{
		id: '3',
		name: '<PERSON>',
		email: '<EMAIL>',
		role: UserRole.EMPLOYEE,
		isRegistered: true,
	},
	{
		id: '4',
		name: '<PERSON>',
		email: '<EMAIL>',
		role: UserRole.EMPLOYEE,
		isRegistered: true,
	},
	{
		id: '5',
		name: '<PERSON>',
		email: '<EMAIL>',
		role: UserRole.MANAGER,
		isRegistered: true,
	},
];

export const mockGroups: {
	admins: UserGroup<UserRole.ADMIN>[],
	managers: UserGroup<UserRole.MANAGER>[],
	employees: UserGroup<UserRole.EMPLOYEE>[],
} = {
	admins: [
		{
			id: 'admin1',
			name: 'System Administrators',
			role: UserRole.ADMIN,
			users: [mockUsers[0]],
		},
	],
	managers: [
		{
			id: 'manager1',
			name: 'Project Managers',
			role: UserRole.MANAGER,
			users: [mockUsers[1], mockUsers[4]],
		},
	],
	employees: [
		{
			id: 'employee1',
			name: 'Development Team',
			role: UserRole.EMPLOYEE,
			users: [mockUsers[2]],
		},
		{
			id: 'employee2',
			name: 'Design Team',
			role: UserRole.EMPLOYEE,
			users: [mockUsers[3]],
		},
	],
};

export const mockInvitations: Invitation[] = [
	{
		id: 'inv1',
		email: '<EMAIL>',
		role: UserRole.EMPLOYEE,
		groupId: 'employee1',
		invitedBy: '1', // John Doe's id
		invitedAt: new Date('2023-06-01T10:00:00Z'),
		expiresAt: new Date('2023-06-08T10:00:00Z'),
		status: 'PENDING',
	},
	{
		id: 'inv2',
		email: '<EMAIL>',
		role: UserRole.MANAGER,
		groupId: 'manager1',
		invitedBy: '1', // John Doe's id
		invitedAt: new Date('2023-06-02T14:30:00Z'),
		expiresAt: new Date('2023-06-09T14:30:00Z'),
		status: 'PENDING',
	},
	{
		id: 'inv3',
		email: '<EMAIL>',
		role: UserRole.EMPLOYEE,
		groupId: 'employee2',
		invitedBy: '1', // John Doe's id
		invitedAt: new Date('2023-05-01T09:00:00Z'),
		expiresAt: new Date('2023-05-08T09:00:00Z'),
		status: 'EXPIRED',
	},
];

// Helper function to initialize the store with mock data
export function initializeExp4StoreWithMockData(store: any) {
	store.$patch({
		users: mockUsers,
		admins: mockGroups.admins,
		managers: mockGroups.managers,
		employees: mockGroups.employees,
		invitations: mockInvitations,
	});
}
