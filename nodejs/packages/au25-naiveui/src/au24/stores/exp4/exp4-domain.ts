// Enums and Interfaces
export enum UserRole {
	ADMIN = 'ADMIN',
	MANAGER = 'MANAGER',
	EMPLOYEE = 'EMPLOYEE'
}

export interface User {
	id: string;
	name: string;
	email: string;
	role: UserRole;
	isRegistered: boolean;
}

export interface UserGroup<T extends UserRole> {
	id: string;
	name: string;
	role: T;
	users: User[];
}

export interface Invitation {
	id: string;
	email: string;
	role: UserRole;
	groupId: string;
	invitedBy: string;
	invitedAt: Date;
	expiresAt: Date;
	status: 'PENDING' | 'ACCEPTED' | 'EXPIRED';
}
