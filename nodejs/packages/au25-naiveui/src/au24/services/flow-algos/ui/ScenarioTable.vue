<template>
	<div>
		<!--    <div style="color:white">row count = {{ rows.length }}</div>-->
		<ATable
			:style="{
				height: height + 'px',
				width: width + 'px'
			}"
			:columns="columns"
			:data-source="rows"
			:pagination="false"
			:scroll="{ x: width, y: height }"
		>
			<!--    <a-create_scenario_result_rows-column v-for="t in trader_names" :key="t" data-index="maxFlow">x</a-create_scenario_result_rows-column>-->
		</ATable>
	</div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator"
import { FlowScenarioMatch } from "../domain/model"

@Component({
	components: {}
})
export default class ScenarioTable extends Vue {
	@Prop({ required: true }) trader_names: string[]
	@Prop({ required: true }) rows: FlowScenarioMatch[]
	@Prop({ required: true }) width: number
	@Prop({ required: true }) height: number

	get columns() {
		return [
			{
				title: "seller",
				dataIndex: "seller_cid",
				key: "seller",
				width: 100
			},
			{
				title: "buyer",
				dataIndex: "buyer_cid",
				key: "buyer",
				width: 100
			},
			{
				title: "limit",
				dataIndex: "limit",
				key: "limit",
				width: 100
			},
			{
				title: "flow",
				dataIndex: "flow",
				key: "flow",
				width: 100
			},
			{
				title: "current",
				dataIndex: "current",
				key: "current",
				width: 100
			},
			{
				title: "",
				dataIndex: "action",
				key: "action",
				width: "auto"
			}
		]
	}
}
</script>

<style scoped></style>
