<template>
	<div>
		<!--    <div style="color:white">row count = {{ rows.length }}</div>-->
		<ATable
			:style="{
				height: height + 'px',
				width: width + 'px'
			}"
			:columns="columns"
			:data-source="rows"
			:pagination="false"
			:scroll="{ x: width, y: height }"
		>
			<!--    <a-create_scenario_result_rows-column v-for="t in trader_names" :key="t" data-index="maxFlow">x</a-create_scenario_result_rows-column>-->

			<a slot="name" slot-scope="text">{{ text }}</a>

			<div slot="maxflow" slot-scope="maxFlow">{{ maxFlow }}</div>

			<div slot="cell" slot-scope="cell">
				<div v-if="cell['status'] === 'BUY'" style="background-color: blue; color: white">B</div>
				<div v-else-if="cell['status'] === 'SELL'" style="background-color: red; color: white">S</div>
			</div>

			<span slot="tags" slot-scope="tags">
				{{ tags }}
				<!--      <a-tag-->
				<!--        v-for="tag in tags"-->
				<!--        :key="tag"-->
				<!--        :color="tag === 'loser' ? 'volcano' : tag.length > 5 ? 'geekblue' : 'green'"-->
				<!--      >-->
				<!--        {{ tag.toUpperCase() }}-->
				<!--      </a-tag>-->
				`
			</span>

			<span slot="action" slot-scope="text, record">
				<a>Invite 一 {{ record.name }}</a>
				<a-divider type="vertical" />
				<a>Delete</a>
				<a-divider type="vertical" />
				<a class="ant-dropdown-link">
					More actions
					<a-icon type="down" />
				</a>
			</span>
		</ATable>
	</div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator"

export interface ScenarioTableCell {
	cid: string
	key: string
}

export interface ScenarioTableRow {
	pkg: boolean[]
	key: string // pkg to string, eg 0110
	maxFlow: number
	cells: Array<ScenarioTableCell>
}

@Component({
	components: {}
})
export default class ScenarioMatrix extends Vue {
	@Prop({ required: true }) trader_names: string[]
	@Prop({ required: true }) rows: ScenarioTableRow[]
	@Prop({ required: true }) width: number
	@Prop({ required: true }) height: number

	get columns() {
		return [
			{
				title: "max flow",
				dataIndex: "maxflow",
				key: "maxflow",
				scopedSlots: { customRender: "maxflow" },
				fixed: "left",
				width: 100
			},
			...this.trader_names.map((t, index) => ({
				title: t,
				dataIndex: `cells[${index}]`, //'cells[index]',
				key: index,
				scopedSlots: { customRender: "cell" }
				//  width: 60
			})),
			{
				title: "",
				dataIndex: "action",
				key: "action",
				width: "auto"
			}
		]
	}
}
</script>

<style scoped></style>
