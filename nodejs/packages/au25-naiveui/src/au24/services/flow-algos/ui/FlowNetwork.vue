<template>
	<div>
		<div style="font-size: 20px; padding: 2px">maxflow: {{ maxFlow }}</div>
		<network class="network" ref="network" :nodes="nodes" :edges="edges" :options="options"></network>
	</div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator"
import { WindowInstanceMap } from "@au21-frontend/utils"
import { Network } from "vue-vis-network"
import { range } from "lodash"
import { AuFFEdge, NetworkFlowSolverBase } from "../domain/au-ford-fulkerson"

@Component({
	components: { Network }
})
export default class FlowNetwork extends Vue {
	@Prop({ required: true }) solver: NetworkFlowSolverBase

	get height() {
		return WindowInstanceMap.height - 100
	}

	width = 800

	get maxFlow(): number {
		return this.solver.getMaxFlow()
	}

	get nodes() {
		const nodes: AuFFEdge[][] = this.solver.getGraph()
		if (nodes.length < 2) return []

		const source = nodes.length - 2
		const sink = nodes.length - 1

		return range(0, nodes.length).map(i => ({
			id: i,
			label: i == source ? "start" : i == sink ? "end" : "" + i
			//   i == entries.length - 1 ? 'end' : i
		}))
	}

	get edges() {
		const nodes: AuFFEdge[][] = this.solver.getGraph()

		const edges = []

		nodes.forEach(n => {
			n.forEach(e => {
				edges.push({
					from: e.from,
					to: e.to,
					label: `${e.flow} / ${e.match}`,
					color: e.flow == 0 ? "black" : e.match == 0 ? "black" : e.flow == e.match ? "red" : "blue",
					dashes: e.match == 0
				})
			})
		})

		return edges
	}

	options = {
		autoResize: true,
		// height: '100%',
		// width: '100%',
		nodes: {
			borderWidth: 2
		},
		edges: {
			color: "black",
			arrows: {
				to: {
					enabled: true
				}
			}
		},
		layout: {
			randomSeed: 1 // so that it lays out the same every time.
		},
		physics: {
			solver: "repulsion"
			// solver: 'forceAtlas2Based',
			// forceAtlas2Based: {
			//   gravitationalConstant: -100,
			//   centralGravity: 0.03,
			//   springLength: 10,
			//   springConstant: 0.1,
			//   damping: 1,
			//   avoidOverlap: 0.4
			// }
		}
	}

	// inc_bid(t: TraderNode) {
	//   t.inc_bid();
	//   this.calculate();
	// }
	//
	// inc_ask(t: TraderNode) {
	//   t.inc_ask();
	//   this.calculate();
	// }
}
</script>

<style lang="less" scoped>
.network {
	height: 800px;
	width: 600px;
	border: 1px solid black;
}
</style>
