/*
 * MODEL
 */

import { AuFFEdge } from "./au-ford-fulkerson"

export enum FlowCalcTraderType {
	buyer,
	seller
}

export type FlowCalcTrader = {
	cid: string
	vertex: number // is equal to the index + 1
	buy: number
	sell: number
}

export type FlowCalcConstraint = {
	buyer_cid: string
	seller_cid: string
	buyer_vertex: number
	seller_vertex: number
	match: number
}

export type FlowScenarioMatch = {
	seller_cid: string
	buyer_cid: string
	limit: number
	flow: number
	current: number
}

export type FlowScenario = {
	package: boolean[] // if true -> buyer else seller // Map<FlowCalcTrader, FlowCalcTraderType>
	maxFlow: number
	solution: AuFFEdge[]
	matches: FlowScenarioMatch[]
}

export type FlowCalcResult = {
	milliseconds: string
	ordered_traders: FlowCalcTrader[]
	constraints: FlowCalcConstraint[]
	scenarios: FlowScenario[]
}
