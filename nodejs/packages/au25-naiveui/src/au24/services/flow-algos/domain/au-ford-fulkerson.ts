// from: https://www.youtube.com/watch?v=Xu8jjJnwvxE&ab_channel=WilliamFiset
// older:
// - https://www.adamconrad.dev/blog/maximum-flow-algorithms-networks-js/

/*
 * this class is generic, it doesn't know about traders, constraints, solutions, etc
 */
import { range } from "lodash"

export class AuFFParams {
	readonly source: number
	readonly target: number

	constructor(readonly V: number) {
		this.source = V - 2
		this.target = V - 1
	}
}

export class AuFFEdge {
	constructor(
		readonly from: number,
		readonly to: number,
		readonly match: number
	) {
	}

	public flow: number = 0
	public residual: AuFFEdge

	isRedisual(): boolean {
		return this.match == 0
	}

	remainingCapacity(): number {
		return this.match - this.flow
	}

	augment(bottleNeck: number) {
		// console.log(`augmenting: from:${this.from}, to:${this.to}, bottleneck:${bottleNeck}`)
		this.flow += bottleNeck
		this.residual.flow -= bottleNeck
	}

	toString(params: AuFFParams): string {
		const u: string =
			this.from == params.source
				? "s"
				: this.from == params.target
					? "t"
					: "" + this.from
		const v: string =
			this.to == params.source
				? "s"
				: this.to == params.target
					? "t"
					: "" + this.to
		return `Edge ${u} -> ${v} | flow = ${this.flow} | match = ${
			this.match
		} | is residual: ${this.isRedisual()}`
	}
}

export abstract class NetworkFlowSolverBase {
	static readonly INF: number = Number.MAX_VALUE / 2

	protected visitedToken: number = 1
	protected visited: number[] = []
	protected minCut: boolean[] = []

	protected solved: boolean = false
	protected maxFlow: number = 0

	protected graph: Array<AuFFEdge>[] = [] // Adjacency list, ie: edges where this node is 'from', residuals added implicityl with match = 0

	protected solution: AuFFEdge[] = []

	protected constructor(readonly params: AuFFParams) {
		range(this.params.V).forEach((i) => (this.graph[i] = []))
	}

	addEdge(from: number, to: number, match: number) {
		if (match <= 0) {
			//throw Error('Forward edge match <= 0');
			console.log(
				`cannot add edge from:${from}, to:${to} because match is not greater than zero`
			)
			return
		}
		const e1 = new AuFFEdge(from, to, match)
		const e2 = new AuFFEdge(to, from, 0)
		e1.residual = e2
		e2.residual = e1
		this.graph[from].push(e1)
		this.graph[to].push(e2)
	}

	markAllNodesAsUnvisited() {
		this.visitedToken++
	}

	addVisit(node: number) {
		this.visited[node] = this.visitedToken
	}

	hasVisited(node: number): boolean {
		return this.visited[node] == this.visitedToken
	}

	getGraph(): Array<AuFFEdge>[] {
		this.execute()
		return this.graph
	}

	getMaxFlow(): number {
		this.execute()
		return this.maxFlow
	}

	getSolution(): AuFFEdge[] {
		this.execute()
		return this.solution
	}

	execute() {
		if (this.solved) return
		this.solved = true
		this.solve()
	}

	abstract solve();
}

export class AuFFDfsSolver extends NetworkFlowSolverBase {
	constructor(params: AuFFParams) {
		super(params)
	}

	solve() {
		for (
			let f = this.dfs(this.params.source, AuFFDfsSolver.INF);
			f != 0;
			f = this.dfs(this.params.source, AuFFDfsSolver.INF)
		) {
			this.markAllNodesAsUnvisited()
			this.maxFlow += f
		}

		this.graph.forEach((edges: AuFFEdge[]) => {
			edges.forEach((e: AuFFEdge) => {
				if (e.flow > 0) {
					this.solution.push(e)
				}
			})
		})
	}

	dfs(node: number, flow: number): number {
		//  console.log(`node: ${node}, flow: ${flow}`)
		if (node == this.params.target) return flow

		const edges: AuFFEdge[] = this.graph[node]
		this.addVisit(node)

		for (let x = 0; x < edges.length; x++) {
			const e: AuFFEdge = edges[x]

			const remaining: number = e.remainingCapacity()
			if (remaining > 0 && !this.hasVisited(e.to)) {
				const bottleneck: number = this.dfs(e.to, Math.min(flow, remaining))

				if (bottleneck > 0) {
					e.augment(bottleneck)
					return bottleneck
				}
			}
		}
		return 0
	}
}

/*
class Vertex {
  constructor(
    public match = 0,
    public  flow = 0,
    public neighbor = null,
    public nextVertex = null,
    public residualCapacity = 0) {
  }
}

class Graph {
  resetSearch() {
    for (let i = 0; i < this.vertices.length; i++) {
      PROCESSED[i] = false;
      VISITED[i] = false;
      PARENTS[i] = null;
    }
  }

  findEdge(start, end) {
    let path = this.connections[start];
    while (path) {
      if (path.adjacencyInfo === end) return path;
      path = path.nextVertex;
    }
    return path;
  }

  augmentPath(start, end, parents, quantity) {
    if (start === end) return;

    let edge = this.findEdge(parents[end], end);
    edge.flow += quantity;
    edge.residualCapacity -= quantity;

    edge = this.findEdge(end, parents[end]);
    edge.residualCapacity += quantity;

    this.augmentPath(start, parents[end], parents, quantity);
  }

  pathQuantity(source, sink, parents) {
    if (parents[parents.length-1] === -1) return 0;

    let edge = this.findEdge(parents[sink], sink);

    if (source === parents[sink]) {
      return edge.residualCapacity;
    } else {
      return Math.min(
        this.pathQuantity(source, parents[sink], parents),
        edge.residualCapacity
      );
    }
  }

  edmondsKarp(source, sink) {
    this.addResidualEdges();
    this.resetSearch();
    this.bfs(source);

    let quantity = this.pathQuantity(source, sink, this.PARENTS);

    while (quantity > 0) {
      this.augmentPath(source, sink, this.PARENTS, quantity);
      this.resetSearch();
      this.bfs(source);
      quantity = this.pathQuantity(source, sink, this.PARENTS);
    }
  }
}
*/
