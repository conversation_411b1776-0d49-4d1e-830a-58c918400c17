<template>
  <a-tabs :animated="false" card style="height: 400px; width: 950px">
    <ATabPane key="1" tab="matrix nodes">
      <GenericTable
        :width="table_width"
        :height="table_height"
        :rows="scenario.matrix_nodes"
      />
    </ATabPane>
    <ATabPane key="2" tab="matrx edges">
      <GenericTable
        :width="table_width"
        :height="table_height"
        :rows="scenario.matrix_edges"
      >
      </GenericTable>
    </ATabPane>
    <ATabPane key="3" tab="ordered traders">
      <GenericTable
        :width="table_width"
        :height="table_height"
        :rows="scenario.ordered_traders"
      />
    </ATabPane>
    <!--    <ATabPane key="4" tab="packages">-->
    <!--      <GenericTable-->
    <!--        :width="table_width"-->
    <!--        :height="table_height"-->
    <!--        :rows="scenario.sorted_summary_table_rows.map(r => {r.key, r.pkg, r.maxFlow})"-->
    <!--      />-->
    <!--    </ATabPane>-->
    <!--    <ATabPane key="30" tab="scenario">-->
    <!--      <ScenarioTable-->
    <!--        :trader_names="scenario.ordered_traders"-->
    <!--        :rows="scenario.sorted_summary_table_rows"-->
    <!--      />-->
    <!--    </ATabPane>-->
  </a-tabs>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import ScenarioTable from '../ui/ScenarioTable.vue'

@Component({
  components: {
    ScenarioTable,
  },
})
export default class ScenarioTableDebug1Demo extends Vue {
  table_width = 900
  table_height = 400
}
</script>

<style scoped></style>
