<template>
  <VbDemo>
    <div class="header">
      <div class="cell" style="float: left">Trader count:</div>
      <a-input-number
        style="float: left; margin: 0 10px"
        id="inputNumber"
        v-model="trader_count"
        :min="1"
        :max="100"
      />
      <div style="margin: 6px; font-weight: bold">
        traders: <span class="result">{{ trader_count }}</span> ,
        <!--              buyers: <span class="result">{{ buyer_count }}</span> ,-->
        <!--              seller: <span class="result">{{ seller_count }}</span> ,-->
        <!--        packages: <span class="result">{{ total_pgk_count }}</span> ,-->
        <!--              buyer packages: <span class="result">{{ last_buyer_pkg }}</span>-->
      </div>
    </div>
    <div style="position: relative; top: 30px">
      <FlowNetwork :solver="solver" />
    </div>
    <div style="margin-top: 80px">
      <!--      <ScenarioTableDebug-->
      <!--        :cols="ordered_traders"-->
      <!--        :rows="max_scenario_table_rows"-->
      <!--      />-->
    </div>
  </VbDemo>
</template>

<script lang="ts">
import {Component, Vue} from 'vue-property-decorator';
import FlowNetwork from '../ui/FlowNetwork.vue';
import {AuFFDfsSolver, NetworkFlowSolverBase} from '../domain/au-ford-fulkerson';

@Component({
  components: {
    FlowNetwork,
  },
})
export default class FlowNetworkDemo extends Vue {
  table_width = 900
  table_height = 300

  trader_count: number = 3

  get solver(): NetworkFlowSolverBase {
    const trader_count = 4

    const g = new AuFFDfsSolver({
      source: trader_count,
      target: trader_count + 1,
      V: trader_count + 2,
    })

    g.addEdge(4, 0, 10)
    g.addEdge(4, 1, 5)
    //  g.addEdge(6, 2, 15);

    g.addEdge(0, 2, 4)
    g.addEdge(0, 3, 9)

    g.addEdge(1, 2, 4)
    g.addEdge(1, 3, 8)

    g.addEdge(2, 5, 3)
    g.addEdge(3, 5, 16)

    return g
  }
}
</script>

<style scoped>
.result {
  padding: 3px;
  background-color: gray;
  color: yellow;
}

.header {
  position: fixed;
  z-index: 999;
  background-color: #bbcccc;
  width: 800px;
}

.cell {
  font-weight: bold;
}
</style>
