import { type DeMatrixEdgeElement, type DeMatrixNodeElement } from "@/au24/types/generated.js"
import { range } from "lodash"
import { createDemo__DeMatrixEdgeElement } from "../../../demo-helpers/DeMatrixEdgeElement.helper"
import { createDemo__DeMatrixNodeElement } from "../../../demo-helpers/DeMatrixNodeElement.helper"

export class ScenarioGenerator {
	readonly trader_range: string[]
	readonly matrix_nodes: DeMatrixNodeElement[]
	readonly matrix_edges: DeMatrixEdgeElement[]

	constructor(readonly trader_count: number) {
		this.trader_range = range(0, trader_count).map(it => "t" + it)

		this.matrix_nodes = this.trader_range.map(t => createDemo__DeMatrixNodeElement(1, t))

		this.matrix_edges = this.trader_range.flatMap((seller: string) =>
			this.trader_range.map((buyer: string) => createDemo__DeMatrixEdgeElement(1, buyer, seller))
		)
	}
}

/*
  const buyers: DeMatrixNodeElement[] = [];
  const sellers: DeMatrixNodeElement[] = [];

  const dividor = int_to_binary_array(this.scenario_count, this.scenario_max_count);


  let count = 0;
  this.matrix_nodes.forEach(node => {
    dividor[count] ?
      sellers.push(node) :
      buyers.push(node);
    count++;
  });

  console.log({ dividor });
  console.log('buyers');
  console.create_scenario_result_rows(buyers);
  console.log('sellers');
  console.create_scenario_result_rows(sellers);
  return new PotentialMatchScenario(buyers, sellers, this.round_edges);
}
*/
