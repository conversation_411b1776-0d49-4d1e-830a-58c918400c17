import {
	type CompanyElement,
	type DeMatrixNodeElement,
	type DeRoundTraderElement,
	type DeTraderElement
} from "@/au24/types/generated"

/**
 * Moving the various de domain helper functions here, for re-use
 */

// ROUND TABLE:

export const round_trader_elements_for_company_sorted_by_round = (
	round_trader_elements: DeRoundTraderElement[],
	company_id: string
): DeRoundTraderElement[] => round_trader_elements.filter(cell => cell.cid === company_id).sort(cell => cell.round)

// TODO What's the point of this?
//  @asva
export const sorted_traders = (traders: DeTraderElement[]): DeTraderElement[] => sort_by_shortname(traders)

// AUCTIONEER PAGE:
export const round_trader_elements_for_round = (
	round_trader_elements: DeRoundTraderElement[],
	round_number: number
): DeRoundTraderElement[] => round_trader_elements?.filter(o => o.round == round_number) || []

export const traders_that_have_seen_auction = (traders: DeTraderElement[]): DeTraderElement[] =>
	traders.filter(t => t.has_seen_auction)

// TRADERS:

export const company_ids = (traders: DeTraderElement[]): Set<string> => new Set<string>(traders.map(t => t.company_id))

export const sort_by_shortname = <T extends DeMatrixNodeElement | DeTraderElement>(items: T[]): T[] =>
	[...items].sort((a, b) => (a.shortname > b.shortname ? 1 : -1))

export function current_auction_companies(store: {
	live_store: {
		companies: CompanyElement[]
		de_auction: {
			auction_id: string
			blotter: {
				traders: DeTraderElement[]
			}
		}
	}
}): CompanyElement[] {
	const auction_id = store?.live_store?.de_auction?.auction_id
	const traders = store?.live_store?.de_auction?.blotter?.traders
	const companies = store?.live_store?.companies

	return (
		auction_id == null || traders == null ? [] : traders.map(t => companies.find(c => c.company_id == t.company_id))
	) as CompanyElement[]
}

export function companies_not_in_current_auction(store: {
	live_store: {
		companies: CompanyElement[]
		de_auction: {
			auction_id: string
			blotter: {
				traders: DeTraderElement[]
			}
		}
	}
}): CompanyElement[] {
	const current_auction_company_ids = current_auction_companies(store).map(c => c.company_id)
	return store?.live_store?.companies?.filter(c => current_auction_company_ids.indexOf(c.company_id) < 0)
}

// MATRIX:

export const matched_nodes = (nodes: DeMatrixNodeElement[]): DeMatrixNodeElement[] =>
	nodes.filter(t => t.buy_match > 0 || t.sell_match > 0)
