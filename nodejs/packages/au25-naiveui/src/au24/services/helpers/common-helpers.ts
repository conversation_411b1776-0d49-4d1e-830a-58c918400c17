/**
 * MOVE THESE OUT OF CHARLIZE STORE:
 */

import { type AuctionRowElement, type SessionUserValue, type UserElement } from "@/au24/types/generated"

// CONNECTED:

export function is_connected(s: SessionUserValue | null): boolean {
	return s !== null
}

// AUCTIONS:

export function open_auctions(store: { live_store: { auction_rows: AuctionRowElement[] } }): AuctionRowElement[] {
	return store.live_store.auction_rows
		?.filter(a => !a.isClosed)
		?.sort((a, b) => b.auction_id.localeCompare(a.auction_id))
}

export function closed_auctions(store: { live_store: { auction_rows: AuctionRowElement[] } }): AuctionRowElement[] {
	return (
		store.live_store.auction_rows
			?.filter(a => a.isClosed)
			?.sort((a, b) => b.auction_id.localeCompare(a.auction_id)) || []
	)
}

// USERS:

export function getOnlineUsersForCompany(companyId: string, users: UserElement[]): UserElement[] {
	if (companyId == null || users == null) return []

	return users.filter(u => u.isOnline && u.company_id === companyId)
}
