import { type AuctionRowElement, type CompanyElement, type UserElement } from "@/au24/types/generated.js"
import { useLiveStore } from "@/au24/stores/live-client-store.ts"
import { pretty } from "@/au24/utils"

export const auctionController = {
	auction_selected(row: AuctionRowElement) {
		console.log("auction_selected: " + row.auction_id)
		useLiveStore().setTestAuction(row)
	},
	toggleHidden(auction_id: string) {
		console.log("toggleHidden: " + auction_id)
	},
	on_delete_click(auction_id: string) {
		console.log("on_delete_click: " + auction_id)
	}
}

export const authController = {
	login(username: string, password: string) {}
}
export const userController = {
	user_selected(u:UserElement) {
		pretty({
			action: "user_selected: ",
			user: u
		})
	},
	update_user(updatedUser: UserElement) {
		console.log("Sending update command for user:", updatedUser.id)
		// Here you would implement the logic to send the update command to the server
		// For now, we'll just return a promise that resolves after a short delay
		return new Promise(resolve => {
			setTimeout(() => {
				console.log("User update command sent for user:", updatedUser.id)
				resolve(true)
			}, 500)
		})
	},
	user_delete(u:UserElement){
		pretty({
			action: 'delete',
			user: u
		})
	}
}

export const companyController = {
	company_selected(company_id: string) {
		console.log("company_selected: " + company_id)
	},
	update_company(updatedCompany: CompanyElement) {
		console.log("Sending update command for user:", updatedCompany.id)
		// Here you would implement the logic to send the update command to the server
		// For now, we'll just return a promise that resolves after a short delay
		return new Promise(resolve => {
			setTimeout(() => {
				console.log("User update command sent for user:", updatedCompany.id)
				resolve(true)
			}, 500)
		})
	}
}
