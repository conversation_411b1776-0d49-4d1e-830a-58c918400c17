import type { RtlItem } from "naive-ui/es/config-provider/src/internal-interface"
import {
	unstableAlertRtl,
	unstableAvatarGroupRtl,
	unstableButtonGroupRtl,
	// unstableBadgeRtl,
	unstableButtonRtl,
	unstableCardRtl,
	unstableCheckboxRtl,
	unstableCollapseRtl,
	unstableCollapseTransitionRtl,
	unstableDataTableRtl,
	unstableDialogRtl,
	unstableDrawerRtl,
	unstableDynamicInputRtl,
	unstableFlexRtl,
	unstableInputNumberRtl,
	unstableInputRtl,
	unstableListRtl,
	unstableMessageRtl,
	unstableNotificationRtl,
	unstablePaginationRtl,
	unstableRadioRtl,
	unstableRowRtl,
	unstableScrollbarRtl,
	unstableSelectRtl,
	unstableSpaceRtl,
	unstableStatisticRtl,
	unstableStepsRtl,
	unstableTableRtl,
	unstableTagRtl,
	unstableThingRtl,
	unstableTreeRtl
} from "naive-ui"

const rtlStyles: RtlItem[] = [
	unstableAlertRtl,
	unstableAvatarGroupRtl,
	// unstableBadgeRtl,
	unstableButtonRtl,
	unstableNotificationRtl,
	unstableMessageRtl,
	unstableDataTableRtl,
	unstableButtonGroupRtl,
	unstableCardRtl,
	unstableCheckboxRtl,
	unstableCollapseRtl,
	unstableCollapseTransitionRtl,
	unstableDialogRtl,
	unstableDrawerRtl,
	unstableDynamicInputRtl,
	unstableFlexRtl,
	unstableInputRtl,
	unstableInputNumberRtl,
	unstableListRtl,
	unstablePaginationRtl,
	unstableRadioRtl,
	unstableSelectRtl,
	unstableSpaceRtl,
	unstableStatisticRtl,
	unstableStepsRtl,
	unstableTableRtl,
	unstableTagRtl,
	unstableThingRtl,
	unstableTreeRtl,
	unstableRowRtl,
	unstableScrollbarRtl
]

export { rtlStyles }
