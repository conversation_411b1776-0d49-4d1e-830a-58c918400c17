import type { MenuMixedOption } from "naive-ui/es/menu/src/interface"
import { renderIcon } from "@/utils"
import { h } from "vue"
import { RouterLink } from "vue-router"

import apps from "./apps"
import authentication from "./authentication"
import calendars from "./calendars"
import cards from "./cards"
import charts from "./charts"
import getComponents from "./components"
import dashboard from "./dashboard"
import editors from "./editors"
import layout from "./layout"
import maps from "./maps"
import tables from "./tables"
import toolbox from "./toolbox"

const BlankIcon = "carbon:document-blank"
const TypographyIcon = "fluent:text-font-16-regular"
const MultiLanguageIcon = "ion:language-outline"
const GroupIcon = "carbon:tree-view"
const IconsIcon = "fluent:icons-24-regular"

export default function getItems(mode: "vertical" | "horizontal", collapsed: boolean): MenuMixedOption[] {
	return [
		dashboard,
		calendars,
		...apps,
		{
			key: "divider-1",
			type: "divider",
			props: {
				style: {
					// marginLeft: "32px"
				}
			}
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: {
							name: "Icons"
						}
					},
					{ default: () => "Icons" }
				),
			key: "Icons",
			icon: renderIcon(IconsIcon)
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: {
							name: "Typography"
						}
					},
					{ default: () => "Typography" }
				),
			key: "Typography",
			icon: renderIcon(TypographyIcon)
		},
		{
			label: () =>
				h(
					RouterLink,
					{
						to: {
							name: "MultiLanguage"
						}
					},
					{ default: () => "Languages & RTL" }
				),
			key: "MultiLanguage",
			icon: renderIcon(MultiLanguageIcon)
		},
		authentication,
		cards,
		tables,
		getComponents(),
		maps,
		charts,
		editors,
		layout,
		toolbox,
		{
			label: () => (
				<div class="item-badge">
					<div>Disabled item</div>
					<div>3</div>
				</div>
			),
			key: "Disabled item",
			icon: renderIcon(BlankIcon),
			disabled: true,
			children: [
				{
					label: "Disabled item a",
					key: "Disabled item a"
				}
			]
		},
		{
			label: "Multi level",
			key: "multi-level",
			icon: renderIcon(BlankIcon),
			children: [
				{
					label: "With icon",
					key: "With icon",
					icon: renderIcon(BlankIcon),
					children: [
						{
							label: "Level three A",
							key: "Level three",
							children: [
								{
									label: "Level four",
									key: "Level four",
									children: [
										{
											label: "Level five",
											key: "Level five"
										}
									]
								}
							]
						}
					]
				},
				{
					label: "Without icon",
					key: "Without icon",
					children: [
						{
							label: "Level three B",
							key: "Level three B"
						}
					]
				},
				{
					label: "Long text, long text, long text, long text",
					key: "Long text, long text, long text, long text"
				},
				{
					type: "group",
					label: "Group",
					key: "group",
					children: [
						{
							label: "Level two A",
							key: "level two A",
							icon: renderIcon(BlankIcon)
						},
						{
							label: "Level two B",
							key: "level two B",
							icon: renderIcon(BlankIcon)
						}
					]
				}
			]
		},
		{
			label: "Group items",
			key: "group-items",
			type: mode === "vertical" && !collapsed ? "group" : undefined,
			icon: renderIcon(GroupIcon),
			children: [
				{
					label: "Level two A",
					key: "level two A",
					icon: renderIcon(BlankIcon)
				},
				{
					label: "Level two B",
					key: "level two B",
					icon: renderIcon(BlankIcon)
				}
			]
		}
	]
}
