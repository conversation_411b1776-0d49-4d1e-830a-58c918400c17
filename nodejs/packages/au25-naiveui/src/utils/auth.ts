import type { RouteMetaAuth } from "@/types/auth.d"
import type { RouteLocationNormalized } from "vue-router"
import { useAuthStore } from "@/stores/auth"

export function authCheck(route: RouteLocationNormalized): string | false | void {
	const { checkAuth, authRedirect, auth, roles }: RouteMetaAuth = route.meta
	const authStore = useAuthStore()

	// Logout handling
	if (route?.redirectedFrom?.name === "Logout") authStore.setLogout()

	// Auth check: if not logged or role not granted
	if (auth && (!authStore.isLogged || (roles && !authStore.isRoleGranted(roles)))) {
		window.location.href = `/login${window.location.search}`
		return false
	}

	// If checkAuth is true and user is logged in
	if (checkAuth && authStore.isLogged) {
		return !roles || authStore.isRoleGranted(roles) ? authRedirect || "/" : route.path
	}
}
