import { createApp } from "vue"
import { getI18NConf, type Locales, type MessageSchema } from "@/lang/config"
import app_router from "@/router"
import { book_router } from "./router/book-router"
import VueGoogleMaps from "@fawmi/vue-google-maps"
import { createPinia } from "pinia"
import { createPersistedState } from "pinia-plugin-persistedstate"
import { createI18n } from "vue-i18n"
import VueApexCharts from "vue3-apexcharts"
import naive from "naive-ui"
import VueVectorMap from "vuevectormap"
import { au24_router } from "@/router/au24-router"


const meta = document.createElement("meta")
meta.name = "naive-ui-style"
document.head.appendChild(meta)

let app: any

const pinia = createPinia()
pinia.use(
	createPersistedState({
		key: id => `__persisted__${id}`
	})
)

const i18n = createI18n<MessageSchema, Locales>(getI18NConf())

// Dynamically import the App component based on the environment variable
const VITE_APP_MODE = import.meta.env.VITE_APP_MODE
console.log("VITE_APP_MODE", VITE_APP_MODE)

let component: any
let router: any

switch (VITE_APP_MODE) {
	case "BOOK": // Book.vue and book_router
		component = (await import("@/Book.vue")).default
		router = book_router
		break
	case "BOOK_PINX": // Book.vue and app_router
		component = (await import("@/Book.vue")).default
		router = app_router
		break
	case "APP_ORIGINAL": // App.vue and app_router
		component = (await import("@/App.vue")).default
		router = app_router
		break
	default: // App.vue and au24_router
		component = (await import("@/App.vue")).default
		router = au24_router
		break
}

console.log({
	VITE_APP_MODE,
	component,
	router
})

app = createApp(component)
app.use(router)
app.use(pinia)
app.use(i18n)
app.use(naive)
app.use(VueApexCharts)
app.use(VueGoogleMaps, {
	load: {}
})
app.use(VueVectorMap)

app.mount("#app")
