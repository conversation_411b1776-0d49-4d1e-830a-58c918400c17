// stores/remoteStore.ts
import { acceptHMRUpdate, defineStore } from "pinia"

export const useRemoteStore = defineStore('remoteStore', {
    state: () => ({
        system: {
            hosts: [] as Host[],
            users: [] as User[],
            userSession: null as UserSession | null,
        },
    }),
    getters: {
        /**
         * Determines if the current user has the ADMIN role.
         */
        isAdmin(): boolean {
            return this.system.userSession?.userRole === UserRole.ADMIN;
        }
    },
    actions: {
        /**
         * Updates the entire system state with a new system object.
         * @param newSystem - The new system state to replace the current state.
         */
        updateSystem(newSystem: System) {
            this.system = newSystem;
        },

        /**
         * Sets the user session within the system state.
         * @param session - The new user session to set.
         */
        setUserSession(session: UserSession) {
            this.system.userSession = session;
        },
    },
});

if (import.meta.hot) {
    import.meta.hot.accept(acceptHMRUpdate(useRemoteStore, import.meta.hot))
}
