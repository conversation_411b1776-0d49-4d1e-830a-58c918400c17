<template>
	<Provider>
		<n-split
			direction="horizontal"
			v-model:size="split"
			class="layout"
			@keydown="handleKeyDown"
			ref="splitContainer"
		>
			<template #1>
				<NCard class="table-container">
					<NInput v-model:value="filterText" placeholder="Filter components..." />
					<NSpace>
						<n-button size="tiny" @click="setFilter('')">x</n-button>
						<n-button size="tiny" @click="setFilter('Au24')">Au24</n-button>
						<n-button size="tiny" @click="setFilter('Page')">Page</n-button>
						<n-button size="tiny" @click="setFilter('Table')">Table</n-button>
						<n-button size="tiny" @click="setFilter('Form')">Form</n-button>
					</NSpace>
					<NDataTable
						ref="dataTableRef"
                        style="font-size: 12px"
						:columns="columns"
						:data="filteredTableData"
						:pagination="false"
						:scroll-x="100"
						:max-height="tableHeight"
						:striped="true"
					/>
				</NCard>
			</template>
			<template #2>
				<NCard class="content-container">
					<div class="scrollable-content">
						<router-view></router-view>
					</div>
				</NCard>
			</template>
		</n-split>
	</Provider>
</template>

<script lang="ts" setup>
import { useWindowSize } from "@vueuse/core"
import { onErrorCaptured, ref, h, onMounted, watch, nextTick, computed } from "vue"
import Provider from "@/app-layouts/common/Provider.vue"
import { useRoute, useRouter } from "vue-router"
import { UAParser } from "ua-parser-js"
import { NDataTable, NButton, NCard, NInput } from "naive-ui"
//import ErrorsDialog from "@/au2410/view/modals/errors-dialog/ErrorsDialog.vue"
import { useThemeStore } from "@/stores/theme"

const { width, height } = useWindowSize()
const tableHeight = computed(() => height.value - 120)

const router = useRouter()
const route = useRoute()
const splitContainer = ref<any>(null)
const tableData = ref<any>([])
const selectedIndex = ref<number>(0)
const dataTableRef = ref<any>(null)

const split = ref(0.25) // Initial size
const initialSplit = computed(() => 270 / (width.value - 6))

const filterText = ref("")

const filteredTableData = computed(() => {
	if (!filterText.value) return tableData.value
	const lowercaseFilter = filterText.value.toLowerCase()
	return tableData.value.filter(item => item.name.toLowerCase().includes(lowercaseFilter))
})

onMounted(() => {
	split.value = initialSplit.value

	tableData.value = router
		.getRoutes()
		.filter(route => route.name) // Filter out routes without names
		.map(route => ({
			name: route.name
		}))
	splitContainer.value?.$el.focus()
	updateSelectedIndex()
})

const updateSelectedIndex = () => {
	selectedIndex.value = filteredTableData.value.findIndex(item => item.name === route.name)
	scrollSelectedIntoView()
}

const setFilter = (text: string) => (filterText.value = text)

const columns: DataTableColumns = [
	{
		key: "name",
		width: 200,
		render: (row: any, index: number) => {
			return h(
				"div",
				{
					style: `cursor: pointer; ${filteredTableData.value[index].name === route.name ? "background-color: #666;" : ""}`,
					onClick: () => navigateToRoute(row.name as string)
				},
				{ default: () => row.name }
			)
		}
	}
]

const navigateToRoute = (name: string) => {
	if (name) {
		router.push({ name })
	}
}

const handleKeyDown = (event: KeyboardEvent) => {
	if (event.key === "ArrowUp") {
		event.preventDefault()
		selectedIndex.value = Math.max(0, selectedIndex.value - 1)
		navigateToRoute(filteredTableData.value[selectedIndex.value].name)
	} else if (event.key === "ArrowDown") {
		event.preventDefault()
		selectedIndex.value = Math.min(filteredTableData.value.length - 1, selectedIndex.value + 1)
		navigateToRoute(filteredTableData.value[selectedIndex.value].name)
	}
}

const scrollSelectedIntoView = () => {
	nextTick(() => {
		const table = dataTableRef.value?.$el.querySelector(".n-data-table-base-table")
		const selectedRow = table?.querySelector(`tr:nth-child(${selectedIndex.value + 1})`)
		selectedRow?.scrollIntoView({ block: "nearest", behavior: "smooth" })
	})
}

// Watch for route changes
watch(() => route.name, updateSelectedIndex)

// Watch for filter changes
watch(filterText, updateSelectedIndex)

const parser = new UAParser()
console.log(parser.getResult())

const selectedStory = ref()

onErrorCaptured(error => {
	console.error("onErrorCaptured:", error)
	// You can also log additional information here
	return false // Prevents the error from propagating further
})

useThemeStore().setTheme("dark")
</script>

<style lang="scss" scoped>
.content-container {
	background-color: var(--bg-body);
}

.layout {
	height: 100vh;
	outline: none;
}

.table-container,
.content-container {
	height: 100%;
}


.scrollable-content {
	flex: 1;
	overflow-y: auto;
}


</style>

<!--:deep(.n-card__content) {-->
<!--height: 100%;-->
<!--display: flex;-->
<!--flex-direction: column;-->
<!--padding: 8px;-->
<!--}-->

<!--:deep(.n-input) {-->
<!--margin-bottom: 8px;-->
<!--}-->

<!--:deep(.n-data-table-td) {-->
<!--padding: 0px 0px !important;-->
<!--font-size: 12px;-->
<!--}-->

<!--:deep(.n-data-table tr) {-->
<!--height: 24px;-->
<!--}-->
