import {createRouter, createWebHistory} from "vue-router"
import AdminDashboard from "@/temp/organisms/AdminDashboard.vue"
import Au24AuctionSettingsFormDemo from "@/au24/view/forms/auction-settings/Au24AuctionSettingsFormDemo.vue"
import Au24NumberInputDemo from "@/au24/view/ui-component/au24-number-input/Au24NumberInputDemo.vue"
import BusDemo from "@/au24/connector/__tests__/BusDemo.vue"
import LoginPageDemo from "@/au24/view/pages/login/LoginPageDemo.vue"
import NaiveLoginDemo from "@/au24/view/panels/naive-login/NaiveLoginDemo.vue"
import Au24UserTableDemo from "@/au24/view/tables/au24-user-table/Au24UserTableDemo.vue"
import Au24TraderSelectTableDemo from "@/au24/view/tables/au24-trader-select-table/Au24TraderSelectTableDemo.vue"
import Au24OrderConstraintsWithRangeDemo from "@/au24/view/widgets/constraints/Au24OrderConstraintsWithRangeDemo.vue";
import Au24OrderConstraintsBarDemo from "@/au24/view/widgets/constraints/Au24OrderConstraintsBarDemo.vue";
import ConstraintsLogicDemo from "@/au24/view/widgets/constraints/ConstraintsLogicDemo.vue";
import ConstraintsLogicAllDemo from "@/au24/view/widgets/constraints/ConstraintsLogicAllDemo.vue";
import Au24TraderHistoryTableDemo from "@/au24/view/tables/au24-trader-history-table/Au24TraderHistoryTableDemo.vue";
import Au24OrderBookDemo from "@/au24/view/tables/au24-order-book/Au24OrderBookDemo.vue";
import Au24AuctionTableDemo from "@/au24/view/tables/au24-auction-table/Au24AuctionTableDemo.vue";
import Au24CompanyFormDemo from "@/au24/view/forms/company-form/Au24CompanyFormDemo.vue";
import Au24AuctionSettingsFormPerplexity1Demo
    from "@/au24/view/forms/auction-settings/Au24AuctionSettingsFormPerplexity1Demo.vue";
import Au24AuctionSettingsFormClaude1Demo
    from "@/au24/view/forms/auction-settings/Au24AuctionSettingsFormClaude1Demo.vue";
import Au24AwardPageDemo from "@/au24/view/pages/award-page/Au24AwardPageDemo.vue";
import DeSettingsGemini from "@/experiments/gemini/DeSettingsGemini.vue";

const book_components = {
    AdminDashboard, //: () => import("@/au24/views/organisms/AdminDashboard.vue")
    //     //DeTraderHeadingDemo: () => import("@/au24/view/panels/de-trader-heading/DeTraderHeadingDemo.vue")
    Au24AuctionSettingsFormDemo,
    Au24AuctionSettingsFormClaude1Demo,
    Au24AuctionSettingsFormPerplexity1Demo,
    Au24AuctionTableDemo,
    Au24AwardPageDemo,
    //Au24CompanyFormDemo,
    //     Au24CompanyPageDemo,
    //     Au24CompanyTableDemo,
    Au24NumberInputDemo,
    Au24OrderBookDemo,
    Au24OrderConstraintsBarDemo,
    Au24OrderConstraintsWithRangeDemo,
    //     //Au24SankeyDiagramDemo,
    //     Au24TraderHistoryChartDemo,
    Au24TraderHistoryTableDemo,
    Au24TraderSelectTableDemo,
    //     Au24UserFormDemo,
    Au24UserTableDemo,
    //     Au24UserPageDemo,
    //     BlinkerDemo,
    BusDemo,
    //     BuySellHBarDemo,
    //     BuySellVBarDemo,
    //     ComboDemo,
    //     CompanyTableDemo,
    //     ConnectorDemo,
    ConstraintsLogicDemo,
    ConstraintsLogicAllDemo,
    //     DeAwardTableDemo,
    // DeOrderBookDemo, REPLACED
    DeSettingsGemini,
    //     DeTraderHeadingDemo,
    //     DemoUserPage,
    //     ErrorsDialogDemo,
    //     HomePageDemo,
    LoginPageDemo,
    NaiveLoginDemo,
    // ReactiveExp1Demo
    //     SampleCheckboxTableDemo,
    //     Sankey2Demo,
    // SimulatorPage,
    //     TraderPageDemo,
    //     TraderSelectTableDemo,
    //     UserTableDemo,
}

export const book_router = createRouter({
    history: createWebHistory(),
    routes: [
        {
            path: "/",
            redirect: to => {
                // Redirect to the first component in the list
                const firstComponent = Object.keys(book_components)[0]
                return {path: `/${firstComponent}`}
            }
        },
        ...Object.entries(book_components).map(([name, component]) => ({
            path: `/${name}`,
            name,
            component: () => Promise.resolve(component)
        }))
    ]
})
