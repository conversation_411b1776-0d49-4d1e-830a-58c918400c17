<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" href="/favicon.ico" />
		<meta name="viewport" content="initial-scale=1, minimum-scale=1, width=device-width, height=device-height" />
		<meta name="mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
		<meta name="apple-mobile-web-app-title" content="Pinx" />
		<meta name="apple-touch-fullscreen" content="yes" />
		<meta name="HandheldFriendly" content="true" />

		<title>Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template</title>

		<!-- Search Engine -->
		<link rel="canonical" href="https://pinx.vercel.app/" />
		<meta name="title" content="Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template" />
		<meta
			name="description"
			content="The sleek and straightforward template designed for developers, crafted with Vue + TypeScript, and designed for an enhanced user experience"
		/>
		<meta
			name="keywords"
			content="HTML,CSS,VueJs,JAVASCRIPT,typescript,ts,JS,element,admin,dashboard,theme,nuxt,naive,tailwind,scss,vite,responsive"
		/>
		<meta name="author" content="D*VERSE Studio" />
		<meta name="image" content="https://pinx.vercel.app/logo.jpg" />
		<!-- Twitter -->
		<meta name="twitter:card" content="summary_large_image" />
		<meta name="twitter:title" content="Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template" />
		<meta
			name="twitter:description"
			content="The sleek and straightforward template designed for developers, crafted with Vue + TypeScript, and designed for an enhanced user experience"
		/>
		<meta name="twitter:site" content="Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template" />
		<meta property="twitter:url" content="https://pinx.vercel.app/" />
		<meta name="twitter:creator" content="@DVERSEStudio" />
		<meta name="twitter:image" content="https://pinx.vercel.app/tw_preview.jpg" />
		<!-- Open Graph -->
		<meta name="og:title" content="Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template" />
		<meta
			name="og:description"
			content="The sleek and straightforward template designed for developers, crafted with Vue + TypeScript, and designed for an enhanced user experience"
		/>
		<meta name="og:image" content="https://pinx.vercel.app/og_preview.jpg" />
		<meta name="og:url" content="https://pinx.vercel.app" />
		<meta name="og:site_name" content="Pinx - Vuejs / Nuxt Tailwind + Naive Admin Dashboard Template" />
		<meta name="og:type" content="website" />
	</head>

	<body>
		<div id="app"></div>
		<script type="module" src="/src/main.ts"></script>
	</body>
</html>
