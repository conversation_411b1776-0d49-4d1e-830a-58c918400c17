// using threads:

import {inflate} from 'pako';
import {expose} from 'threads';

expose(function unzip_and_parse_command(data): any | null {
  try {
    //const start = performance.now();
    const unzipped_arr = inflate(data);
    const unzipped_string = new TextDecoder().decode(unzipped_arr);
    const cmd = JSON.parse(unzipped_string);
    //console.log({ unzipped_string });
    //console.log({ cmd });
    //const duration = (performance.now() - start).toFixed(3);
    // console.log(`parsing ${cmd.command} took ${duration} ms`, { cmd });
    return cmd;
  } catch (e) {
    console.log(e);
  }
  return null;
});
