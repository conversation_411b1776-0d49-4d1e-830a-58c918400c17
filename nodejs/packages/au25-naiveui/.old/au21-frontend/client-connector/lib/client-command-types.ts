/**
 *  THIS FILE NEEDS TO BE IN SYNC WITH:
 *  au12-engine/src/main/kotlin/au21/engine/framework/commands/client/client-command-types.kt
 *  au21-frontend/libs/client-connector/src/lib/client-command-types.ts
 */

import {ClientCommand} from '@au21-frontend/client-connector';

// export type string = string

export type EngineTransaction = {
  readonly client_command_maps: Array<ClientCommandSessionsMap>
  readonly engine_command_json: string | null // null in case of heartbeat
  readonly engine_command_name: string
  readonly isHeartbeat: boolean
  readonly session_id: string | null
  readonly duration_ms: number
  readonly has_alert:boolean
  readonly has_session_differ_error:boolean
  readonly requests_since_last_restart: number
}

export type ClientCommandSessionsMap = {
  readonly command: ClientCommand,
  readonly sessionIds: Array<string>
}

export type SessionClientCommandsEnvelope = {
  readonly session_id: string,
  readonly envelope_count_since_last_restart: number, // used by faye server
  readonly commands: Array<ClientCommand>
}

// TODO: this should be generated:
export type ClientCommandType =
// 1) Browser Commands:
  | "CommandSucceeded"
  | "ShowMessage"
  | "TerminateSession"
  // these are not yet implemented:
  | "NetworkDown"
  | "NetworkUp"
  // 2) Store Commands:
  | "SetLiveStore"
  | "AddElements"
