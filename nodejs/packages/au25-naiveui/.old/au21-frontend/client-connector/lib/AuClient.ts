import {CommandSucceeded, NetworkDown, NetworkUp, ShowMessage, TerminateSession} from '@au21-frontend/client-connector';


export type AuClientEvent = {
  CommandSucceeded: CommandSucceeded,
  ShowMessage: ShowMessage,
  TerminateSession: TerminateSession,
  NetworkDown: NetworkDown,
  NetworkUp: NetworkUp,
}

export abstract class AuClient {
  abstract onEvent<EVENT extends keyof AuClientEvent>(eventName: EVENT, event: AuClientEvent[EVENT])
}
