{"name": "au25-<PERSON><PERSON>", "type": "module", "version": "1.14.1", "private": true, "engines": {"node": ">=18.0.0"}, "scripts": {"dev:app": "VITE_APP_MODE=APP vite", "dev:app:original": "VITE_APP_MODE=APP_ORIGINAL vite", "dev:book": "VITE_APP_MODE=BOOK vite", "dev:book:pinx": "VITE_APP_MODE=BOOK_PINX vite", "build": "run-p type-check \"build-only {@}\" --", "build-only": "vite build", "build-analyzer": "vite-bundle-visualizer -t sunburst", "build-check": "run-s libs-reload build lint type-check format test:e2e test:unit", "preview": "vite build && echo '' && vite preview --port 4173 --host", "preview-only": "vite preview --port 4173 --host", "test:unit": "vitest", "test:e2e": "start-server-and-test preview http://localhost:4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' http://localhost:4173 'cypress open --e2e'", "type-check": "vue-tsc --build --force", "lint": "eslint . --fix", "tailwind-config-viewer": "tailwind-config-viewer -o", "design-tokens": "node scripts/tokens-tool.js", "libs-check": "taze", "libs-reload": "rm -rf node_modules package-lock.json && npm install", "format": "prettier --write src/"}, "dependencies": {"@fawmi/vue-google-maps": "^0.9.79", "@fontsource/jetbrains-mono": "^5.1.1", "@fontsource/lexend": "^5.1.1", "@fontsource/public-sans": "^5.1.1", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "@milkdown/core": "^7.5.0", "@milkdown/ctx": "^7.5.0", "@milkdown/preset-commonmark": "^7.5.0", "@milkdown/prose": "^7.5.0", "@milkdown/theme-nord": "^7.5.0", "@milkdown/transformer": "^7.5.0", "@milkdown/vue": "^7.5.0", "@popperjs/core": "^2.11.8", "@revolist/revogrid": "^4.10.18", "@revolist/revogrid-column-numeral": "^2.0.2", "@revolist/vue3-datagrid": "^4.10.15", "@tiptap/extension-character-count": "^2.8.0", "@tiptap/extension-highlight": "^2.8.0", "@tiptap/extension-link": "^2.8.0", "@tiptap/extension-task-item": "^2.8.0", "@tiptap/extension-task-list": "^2.8.0", "@tiptap/extension-text-align": "^2.8.0", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/extension-underline": "^2.8.0", "@tiptap/pm": "^2.8.0", "@tiptap/starter-kit": "^2.8.0", "@tiptap/vue-3": "^2.8.0", "@vicons/ionicons5": "^0.12.0", "@vueup/vue-quill": "^1.2.0", "@vueuse/components": "^11.1.0", "@vueuse/core": "^11.1.0", "apexcharts": "^3.54.0", "chart.js": "^4.4.4", "chroma-js": "^3.1.2", "colord": "^2.9.3", "dayjs": "^1.11.13", "detect-touch-device": "^1.1.6", "geojson": "^0.5.0", "highlight.js": "^11.10.0", "lodash": "^4.17.21", "maplibre-gl": "^4.7.1", "mitt": "^3.0.1", "naive-ui": "^2.40.1", "pako": "^2.1.0", "pinia": "^2.2.4", "pinia-plugin-persistedstate": "^4.1.1", "quill": "^2.0.2", "shepherd.js": "^13.0.3", "ua-parser-js": "^1.0.39", "v-calendar": "^3.1.2", "vue": "^3.5.11", "vue-advanced-cropper": "^2.8.9", "vue-cal": "^4.10.0", "vue-chartjs": "^5.3.1", "vue-highlight-words": "^3.0.1", "vue-i18n": "^10.0.4", "vue-maplibre-gl": "^3.1.3", "vue-router": "^4.4.5", "vue3-apexcharts": "1.5.3", "vuedraggable": "^4.1.0", "vuevectormap": "^2.0.1"}, "devDependencies": {"@antfu/eslint-config": "^3.7.3", "@clack/prompts": "^0.7.0", "@faker-js/faker": "^9.0.3", "@iconify/vue": "^4.1.2", "@previewjs/cli": "^1.28.1", "@previewjs/config": "^6.0.1", "@previewjs/core": "^29.1.1", "@previewjs/plugin-vue3": "^15.0.2", "@previewjs/pro": "^1.15.3", "@previewjs/vfs": "^2.1.3", "@storybook/addon-essentials": "^8.4.3", "@storybook/testing-vue3": "^1.0.0", "@storybook/vue3": "^8.4.3", "@tsconfig/node20": "^20.1.4", "@types/dom-view-transitions": "^1.0.5", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jsdom": "^21.1.7", "@types/lodash": "^4.17.10", "@types/node": "^22.7.5", "@types/pako": "^2.0.3", "@vitejs/plugin-vue": "^5.2.0", "@vitejs/plugin-vue-jsx": "^4.0.1", "@vue-leaflet/vue-leaflet": "^0.10.1", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "asva-helpers": "^0.1.5", "autoprefixer": "^10.4.20", "cypress": "^13.15.0", "eslint": "^9.12.0", "fs-extra": "^11.2.0", "globals": "^15.9.0", "jsdom": "^25.0.1", "leaflet": "^1.9.4", "less": "^4.2.0", "npm-run-all2": "^6.2.3", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.79.4", "start-server-and-test": "^2.0.8", "tailwind-config-viewer": "^2.0.4", "tailwindcss": "^3.4.13", "taze": "^0.17.2", "ts-node": "^10.9.2", "type-fest": "^4.26.1", "typescript": "~5.5.4", "unplugin-vue-components": "^0.27.4", "vite": "^5.4.8", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-vue-devtools": "^7.4.6", "vite-svg-loader": "^5.1.0", "vitest": "^2.1.2", "vue-tsc": "^2.1.6"}, "overrides": {"@typescript-eslint/eslint-plugin": {"eslint": "^9.12.0"}, "@typescript-eslint/parser": {"eslint": "^9.12.0"}, "@vueup/vue-quill": {"quill": "^2.0.2", "quill-delta": "^5.1.0"}}}