## src/**/*.vue

### by filename

| File | Directory | Line Count |
|------|-----------|------------|
| ActionToolbar.vue | src/components/apps/Mailbox | 127 |
| Affix.vue | src/views/Components | 133 |
| Alert.vue | src/views/Components | 67 |
| Analytics.vue | src/views/Dashboard | 252 |
| Anchor.vue | src/views/Components | 75 |
| Apex.vue | src/components/charts | 67 |
| ApexCharts.vue | src/views/Charts | 67 |
| App.vue | src | 86 |
| AuthForm.vue | src/components/auth | 149 |
| AutoComplete.vue | src/views/Components | 127 |
| Avatar.vue | src/app-layouts/common/Toolbar | 50 |
| Avatar.vue | src/views/Components | 212 |
| BackTop.vue | src/views/Components | 40 |
| Badge.vue | src/views/Components | 149 |
| Bar.vue | src/components/charts/demo-pages/apex-charts-components | 134 |
| Bar.vue | src/components/charts/demo-pages/chartjs-components | 65 |
| Base.vue | src/components/tables | 194 |
| Base.vue | src/views/Tables | 76 |
| Basic.vue | src/views/Tables/data-tables-components | 92 |
| Basic.vue | src/views/Cards | 58 |
| Basic.vue | src/views/Components/data-table-components | 142 |
| Breadcrumb.vue | src/app-layouts/common/Toolbar | 104 |
| Breadcrumb.vue | src/views/Components | 156 |
| Brush.vue | src/components/charts/demo-pages/apex-charts-components | 173 |
| Button.vue | src/views/Components | 461 |
| Calendar.vue | src/views/Components | 62 |
| Card.vue | src/views/Components | 103 |
| CardActions.vue | src/components/cards | 166 |
| CardBasic1.vue | src/components/cards/basic | 22 |
| CardBasic2.vue | src/components/cards/basic | 57 |
| CardBasic3.vue | src/components/cards/basic | 33 |
| CardBasic4.vue | src/components/cards/basic | 17 |
| CardBasic5.vue | src/components/cards/basic | 40 |
| CardBasic6.vue | src/components/cards/basic | 38 |
| CardCodeExample.vue | src/components/cards | 127 |
| CardCombo1.vue | src/components/cards/combo | 276 |
| CardCombo2.vue | src/components/cards/combo | 65 |
| CardCombo3.vue | src/components/cards/combo | 271 |
| CardCombo4.vue | src/components/cards/combo | 90 |
| CardCombo5.vue | src/components/cards/combo | 240 |
| CardCombo6.vue | src/components/cards/combo | 142 |
| CardCombo7.vue | src/components/cards/combo | 88 |
| CardCombo8.vue | src/components/cards/combo | 71 |
| CardComboIcon.vue | src/components/cards/combo | 62 |
| CardEcommerce1.vue | src/components/cards/ecommerce | 51 |
| CardEcommerce2.vue | src/components/cards/ecommerce | 116 |
| CardEcommerce3.vue | src/components/cards/ecommerce | 26 |
| CardEcommerce4.vue | src/components/cards/ecommerce | 114 |
| CardExtra1.vue | src/components/cards/extra | 19 |
| CardExtra2.vue | src/components/cards/extra | 27 |
| CardExtra3.vue | src/components/cards/extra | 67 |
| CardExtra4.vue | src/components/cards/extra | 14 |
| CardExtra5.vue | src/components/cards/extra | 194 |
| CardExtra6.vue | src/components/cards/extra | 125 |
| CardExtra7.vue | src/components/cards/extra | 108 |
| CardSocial1.vue | src/components/cards/social | 257 |
| CardWrapper.vue | src/components/cards | 83 |
| Carousel.vue | src/views/Components | 193 |
| Cascader.vue | src/views/Components | 229 |
| ChartJS.vue | src/views/Charts | 33 |
| Chat.vue | src/views/Apps | 500 |
| Checkbox.vue | src/views/Components | 109 |
| Collapse.vue | src/views/Components | 171 |
| ColorPicker.vue | src/views/Components | 43 |
| Column.vue | src/components/charts/demo-pages/apex-charts-components | 119 |
| ColumnEditor.vue | src/components/apps/Kanban | 37 |
| Combo.vue | src/views/Cards | 196 |
| ComposeView.vue | src/components/apps/Mailbox | 125 |
| Countdown.vue | src/views/Components | 56 |
| DataTable.vue | src/views/Tables | 67 |
| DataTable.vue | src/views/Components | 44 |
| DatePicker.vue | src/views/Components | 235 |
| DemoApex.vue | src/components/charts | 301 |
| Descriptions.vue | src/views/Components | 63 |
| Dialog.vue | src/views/Components | 125 |
| Divider.vue | src/views/Components | 81 |
| Draggable.vue | src/views/Tables/data-tables-components | 93 |
| Draggable.vue | src/views/Components/data-table-components | 158 |
| Drawer.vue | src/views/Components | 295 |
| Dropdown.vue | src/views/Components | 482 |
| DynamicInput.vue | src/views/Components | 168 |
| DynamicTags.vue | src/views/Components | 41 |
| eCommerce.vue | src/views/Dashboard | 239 |
| Ecommerce.vue | src/views/Cards | 21 |
| Editable.vue | src/views/Tables/data-tables-components | 141 |
| Editor.vue | src/components/editors/Milkdown | 37 |
| Ellipsis.vue | src/views/Components | 124 |
| Email.vue | src/components/apps/Mailbox | 193 |
| EmailContent.vue | src/components/apps/Mailbox | 149 |
| EmailToolbar.vue | src/components/apps/Mailbox | 157 |
| Empty.vue | src/views/Tables/data-tables-components | 42 |
| Empty.vue | src/views/Components | 45 |
| EventEditor.vue | src/components/apps/FullCalendar | 122 |
| Expand.vue | src/views/Tables/data-tables-components | 133 |
| Extra.vue | src/views/Cards | 64 |
| FileDrop.vue | src/components/common | 55 |
| ForgotPassword.vue | src/components/auth | 61 |
| Form.vue | src/views/Components | 270 |
| FullCalendar.vue | src/views/Apps/Calendars | 840 |
| FullscreenSwitch.vue | src/app-layouts/common/Toolbar | 36 |
| FullWidth.vue | src/views/Layout | 24 |
| GlobalListener.vue | src/app-layouts/common | 14 |
| GoogleMaps.vue | src/views/Maps | 81 |
| GradientText.vue | src/views/Components | 64 |
| Grid.vue | src/views/Tables | 157 |
| Grid.vue | src/views/Components | 111 |
| HeaderBar.vue | src/app-layouts/HorizontalNav | 58 |
| Icon.vue | src/components/common | 59 |
| Icon.vue | src/views/Components | 74 |
| Icons.vue | src/views | 138 |
| Image.vue | src/views/Components | 86 |
| ImageCropper.vue | src/components/common | 148 |
| ImageLoader.vue | src/components/common | 116 |
| index.vue | src/components/editors/Tiptap | 94 |
| index.vue | src/components/editors/Milkdown | 11 |
| index.vue | src/app-layouts/Blank | 22 |
| index.vue | src/app-layouts/HorizontalNav | 27 |
| index.vue | src/app-layouts/common/Navbar | 285 |
| index.vue | src/app-layouts/common/Toolbar | 209 |
| index.vue | src/app-layouts/VerticalNav | 24 |
| Input.vue | src/views/Components | 371 |
| InputNumber.vue | src/views/Components | 156 |
| Kanban.vue | src/views/Apps | 286 |
| LargeData.vue | src/views/Tables/data-tables-components | 74 |
| Layout.vue | src/views/Components | 123 |
| LayoutSettings.vue | src/components/common | 463 |
| Leaflet.vue | src/views/Maps | 64 |
| LeftSidebar.vue | src/views/Layout | 49 |
| LegacyGrid.vue | src/views/Components | 111 |
| Line.vue | src/components/charts/demo-pages/chartjs-components | 62 |
| List.vue | src/components/common/Notifications | 221 |
| List.vue | src/components/list | 147 |
| List.vue | src/views/Cards | 213 |
| List.vue | src/views/Components | 81 |
| LocaleSelect.vue | src/components/common | 60 |
| LocaleSwitch.vue | src/app-layouts/common/Toolbar | 58 |
| Login.vue | src/views/Auth | 54 |
| Logo.vue | src/app-layouts/common | 57 |
| LtrContext.vue | src/components/common | 16 |
| Mailbox.vue | src/views/Apps | 345 |
| MainContainer.vue | src/app-layouts/Blank | 51 |
| MainContainer.vue | src/app-layouts/HorizontalNav | 106 |
| MainContainer.vue | src/app-layouts/VerticalNav | 131 |
| MainFooter.vue | src/app-layouts/common | 84 |
| Map.vue | src/components/maps/maplibre | 172 |
| Map.vue | src/components/maps/leaflet | 135 |
| MapLibre.vue | src/views/Maps | 49 |
| Mention.vue | src/views/Components | 120 |
| Menu.vue | src/views/Components | 359 |
| MenuBar.vue | src/components/editors/Tiptap | 236 |
| MenuItem.vue | src/components/editors/Tiptap | 101 |
| Merge.vue | src/views/Tables/data-tables-components | 125 |
| Merge.vue | src/views/Components/data-table-components | 230 |
| Message.vue | src/views/Components | 95 |
| Milkdown.vue | src/views/Editors | 24 |
| Modal.vue | src/views/Components | 76 |
| MultiLanguage.vue | src/views | 82 |
| Navigator.vue | src/components/apps/Mailbox | 34 |
| Notes.vue | src/views/Apps | 313 |
| NotFound.vue | src/views | 27 |
| Notification.vue | src/views/Components | 196 |
| Notifications.vue | src/app-layouts/common/Toolbar | 84 |
| NumberAnimation.vue | src/views/Components | 73 |
| PageHeader.vue | src/views/Components | 172 |
| Pagination.vue | src/views/Components | 85 |
| Percentage.vue | src/components/common | 125 |
| Pie.vue | src/components/charts/demo-pages/apex-charts-components | 64 |
| PinnedPagesV1.vue | src/app-layouts/common/Toolbar | 254 |
| PinnedPagesV2.vue | src/app-layouts/common/Toolbar | 234 |
| Popconfirm.vue | src/views/Components | 67 |
| Popover.vue | src/views/Components | 304 |
| Popselect.vue | src/views/Components | 225 |
| Profile.vue | src/views | 190 |
| ProfileActivity.vue | src/components/profile | 89 |
| ProfileSettings.vue | src/components/profile | 107 |
| Progress.vue | src/views/Components | 339 |
| Provider.vue | src/app-layouts/common | 99 |
| Quill.vue | src/views/Editors | 49 |
| Radar.vue | src/components/charts/demo-pages/apex-charts-components | 110 |
| Radio.vue | src/views/Components | 175 |
| Rate.vue | src/views/Components | 46 |
| Realtime.vue | src/components/charts/demo-pages/apex-charts-components | 211 |
| RefreshTool.vue | src/views/Toolbox | 40 |
| Result.vue | src/views/Components | 192 |
| RightSidebar.vue | src/views/Layout | 49 |
| Scrollbar.vue | src/views/Components | 101 |
| Search.vue | src/app-layouts/common/Toolbar | 129 |
| SearchDialog.vue | src/components/common | 420 |
| SegmentedPage.vue | src/components/common | 444 |
| Select.vue | src/views/Components | 245 |
| Selection.vue | src/views/Tables/data-tables-components | 173 |
| Selection.vue | src/views/Components/data-table-components | 186 |
| Settings.vue | src/components/auth | 101 |
| Sidebar.vue | src/app-layouts/HorizontalNav | 122 |
| Sidebar.vue | src/app-layouts/VerticalNav | 133 |
| SidebarFooter.vue | src/app-layouts/HorizontalNav | 76 |
| SidebarFooter.vue | src/app-layouts/VerticalNav | 76 |
| SidebarHeader.vue | src/app-layouts/HorizontalNav | 166 |
| SidebarHeader.vue | src/app-layouts/VerticalNav | 166 |
| SignIn.vue | src/components/auth | 103 |
| SignUp.vue | src/components/auth | 115 |
| Skeleton.vue | src/views/Components | 97 |
| Slider.vue | src/views/Components | 102 |
| Sorting.vue | src/views/Tables/data-tables-components | 155 |
| Sorting.vue | src/views/Components/data-table-components | 292 |
| Space.vue | src/views/Components | 132 |
| Spin.vue | src/views/Components | 82 |
| SplashScreen.vue | src/app-layouts/common | 47 |
| Statistic.vue | src/views/Components | 64 |
| Steps.vue | src/views/Components | 264 |
| Switch.vue | src/views/Components | 99 |
| Sync.vue | src/components/charts/demo-pages/apex-charts-components | 246 |
| Table.vue | src/views/Components | 89 |
| Tabs.vue | src/views/Components | 236 |
| Tag.vue | src/views/Components | 148 |
| TaskCard.vue | src/components/apps/Kanban | 78 |
| TaskEditor.vue | src/components/apps/Kanban | 69 |
| TestScope.vue | src/components/common | 12 |
| ThemeSwitch.vue | src/app-layouts/common/Toolbar | 124 |
| Thing.vue | src/views/Components | 209 |
| Time.vue | src/views/Components | 45 |
| Timeline.vue | src/views/Components | 118 |
| TimePicker.vue | src/views/Components | 115 |
| Tiptap.vue | src/views/Editors | 39 |
| Toolbar.vue | src/components/common/Notifications | 34 |
| Tooltip.vue | src/views/Components | 79 |
| Tour.vue | src/views/Toolbox | 216 |
| Transfer.vue | src/views/Components | 86 |
| Tree.vue | src/views/Components | 295 |
| TreeSelect.vue | src/views/Components | 443 |
| Typography.vue | src/views | 469 |
| Typography.vue | src/views/Components | 208 |
| Upload.vue | src/views/Components | 190 |
| VectorMap.vue | src/views/Maps | 157 |
| VueCal.vue | src/views/Apps/Calendars | 390 |
| Watermark.vue | src/views/Components | 161 |


---

### by path

| File | Directory | Line Count |
|------|-----------|------------|
| index.vue | src/app-layouts/Blank | 22 |
| MainContainer.vue | src/app-layouts/Blank | 51 |
| GlobalListener.vue | src/app-layouts/common | 14 |
| Logo.vue | src/app-layouts/common | 57 |
| MainFooter.vue | src/app-layouts/common | 84 |
| index.vue | src/app-layouts/common/Navbar | 285 |
| Provider.vue | src/app-layouts/common | 99 |
| SplashScreen.vue | src/app-layouts/common | 47 |
| Avatar.vue | src/app-layouts/common/Toolbar | 50 |
| Breadcrumb.vue | src/app-layouts/common/Toolbar | 104 |
| FullscreenSwitch.vue | src/app-layouts/common/Toolbar | 36 |
| index.vue | src/app-layouts/common/Toolbar | 209 |
| LocaleSwitch.vue | src/app-layouts/common/Toolbar | 58 |
| Notifications.vue | src/app-layouts/common/Toolbar | 84 |
| PinnedPagesV1.vue | src/app-layouts/common/Toolbar | 254 |
| PinnedPagesV2.vue | src/app-layouts/common/Toolbar | 234 |
| Search.vue | src/app-layouts/common/Toolbar | 129 |
| ThemeSwitch.vue | src/app-layouts/common/Toolbar | 124 |
| HeaderBar.vue | src/app-layouts/HorizontalNav | 58 |
| index.vue | src/app-layouts/HorizontalNav | 27 |
| MainContainer.vue | src/app-layouts/HorizontalNav | 106 |
| Sidebar.vue | src/app-layouts/HorizontalNav | 122 |
| SidebarFooter.vue | src/app-layouts/HorizontalNav | 76 |
| SidebarHeader.vue | src/app-layouts/HorizontalNav | 166 |
| index.vue | src/app-layouts/VerticalNav | 24 |
| MainContainer.vue | src/app-layouts/VerticalNav | 131 |
| Sidebar.vue | src/app-layouts/VerticalNav | 133 |
| SidebarFooter.vue | src/app-layouts/VerticalNav | 76 |
| SidebarHeader.vue | src/app-layouts/VerticalNav | 166 |
| App.vue | src | 86 |
| EventEditor.vue | src/components/apps/FullCalendar | 122 |
| ColumnEditor.vue | src/components/apps/Kanban | 37 |
| TaskCard.vue | src/components/apps/Kanban | 78 |
| TaskEditor.vue | src/components/apps/Kanban | 69 |
| ActionToolbar.vue | src/components/apps/Mailbox | 127 |
| ComposeView.vue | src/components/apps/Mailbox | 125 |
| Email.vue | src/components/apps/Mailbox | 193 |
| EmailContent.vue | src/components/apps/Mailbox | 149 |
| EmailToolbar.vue | src/components/apps/Mailbox | 157 |
| Navigator.vue | src/components/apps/Mailbox | 34 |
| AuthForm.vue | src/components/auth | 149 |
| ForgotPassword.vue | src/components/auth | 61 |
| Settings.vue | src/components/auth | 101 |
| SignIn.vue | src/components/auth | 103 |
| SignUp.vue | src/components/auth | 115 |
| CardBasic1.vue | src/components/cards/basic | 22 |
| CardBasic2.vue | src/components/cards/basic | 57 |
| CardBasic3.vue | src/components/cards/basic | 33 |
| CardBasic4.vue | src/components/cards/basic | 17 |
| CardBasic5.vue | src/components/cards/basic | 40 |
| CardBasic6.vue | src/components/cards/basic | 38 |
| CardActions.vue | src/components/cards | 166 |
| CardCodeExample.vue | src/components/cards | 127 |
| CardWrapper.vue | src/components/cards | 83 |
| CardCombo1.vue | src/components/cards/combo | 276 |
| CardCombo2.vue | src/components/cards/combo | 65 |
| CardCombo3.vue | src/components/cards/combo | 271 |
| CardCombo4.vue | src/components/cards/combo | 90 |
| CardCombo5.vue | src/components/cards/combo | 240 |
| CardCombo6.vue | src/components/cards/combo | 142 |
| CardCombo7.vue | src/components/cards/combo | 88 |
| CardCombo8.vue | src/components/cards/combo | 71 |
| CardComboIcon.vue | src/components/cards/combo | 62 |
| CardEcommerce1.vue | src/components/cards/ecommerce | 51 |
| CardEcommerce2.vue | src/components/cards/ecommerce | 116 |
| CardEcommerce3.vue | src/components/cards/ecommerce | 26 |
| CardEcommerce4.vue | src/components/cards/ecommerce | 114 |
| CardExtra1.vue | src/components/cards/extra | 19 |
| CardExtra2.vue | src/components/cards/extra | 27 |
| CardExtra3.vue | src/components/cards/extra | 67 |
| CardExtra4.vue | src/components/cards/extra | 14 |
| CardExtra5.vue | src/components/cards/extra | 194 |
| CardExtra6.vue | src/components/cards/extra | 125 |
| CardExtra7.vue | src/components/cards/extra | 108 |
| CardSocial1.vue | src/components/cards/social | 257 |
| Apex.vue | src/components/charts | 67 |
| Bar.vue | src/components/charts/demo-pages/apex-charts-components | 134 |
| Brush.vue | src/components/charts/demo-pages/apex-charts-components | 173 |
| Column.vue | src/components/charts/demo-pages/apex-charts-components | 119 |
| Pie.vue | src/components/charts/demo-pages/apex-charts-components | 64 |
| Radar.vue | src/components/charts/demo-pages/apex-charts-components | 110 |
| Realtime.vue | src/components/charts/demo-pages/apex-charts-components | 211 |
| Sync.vue | src/components/charts/demo-pages/apex-charts-components | 246 |
| Bar.vue | src/components/charts/demo-pages/chartjs-components | 65 |
| Line.vue | src/components/charts/demo-pages/chartjs-components | 62 |
| DemoApex.vue | src/components/charts | 301 |
| FileDrop.vue | src/components/common | 55 |
| Icon.vue | src/components/common | 59 |
| ImageCropper.vue | src/components/common | 148 |
| ImageLoader.vue | src/components/common | 116 |
| LayoutSettings.vue | src/components/common | 463 |
| LocaleSelect.vue | src/components/common | 60 |
| LtrContext.vue | src/components/common | 16 |
| List.vue | src/components/common/Notifications | 221 |
| Toolbar.vue | src/components/common/Notifications | 34 |
| Percentage.vue | src/components/common | 125 |
| SearchDialog.vue | src/components/common | 420 |
| SegmentedPage.vue | src/components/common | 444 |
| TestScope.vue | src/components/common | 12 |
| Editor.vue | src/components/editors/Milkdown | 37 |
| index.vue | src/components/editors/Milkdown | 11 |
| index.vue | src/components/editors/Tiptap | 94 |
| MenuBar.vue | src/components/editors/Tiptap | 236 |
| MenuItem.vue | src/components/editors/Tiptap | 101 |
| List.vue | src/components/list | 147 |
| Map.vue | src/components/maps/leaflet | 135 |
| Map.vue | src/components/maps/maplibre | 172 |
| ProfileActivity.vue | src/components/profile | 89 |
| ProfileSettings.vue | src/components/profile | 107 |
| Base.vue | src/components/tables | 194 |
| FullCalendar.vue | src/views/Apps/Calendars | 840 |
| VueCal.vue | src/views/Apps/Calendars | 390 |
| Chat.vue | src/views/Apps | 500 |
| Kanban.vue | src/views/Apps | 286 |
| Mailbox.vue | src/views/Apps | 345 |
| Notes.vue | src/views/Apps | 313 |
| Login.vue | src/views/Auth | 54 |
| Basic.vue | src/views/Cards | 58 |
| Combo.vue | src/views/Cards | 196 |
| Ecommerce.vue | src/views/Cards | 21 |
| Extra.vue | src/views/Cards | 64 |
| List.vue | src/views/Cards | 213 |
| ApexCharts.vue | src/views/Charts | 67 |
| ChartJS.vue | src/views/Charts | 33 |
| Affix.vue | src/views/Components | 133 |
| Alert.vue | src/views/Components | 67 |
| Anchor.vue | src/views/Components | 75 |
| AutoComplete.vue | src/views/Components | 127 |
| Avatar.vue | src/views/Components | 212 |
| BackTop.vue | src/views/Components | 40 |
| Badge.vue | src/views/Components | 149 |
| Breadcrumb.vue | src/views/Components | 156 |
| Button.vue | src/views/Components | 461 |
| Calendar.vue | src/views/Components | 62 |
| Card.vue | src/views/Components | 103 |
| Carousel.vue | src/views/Components | 193 |
| Cascader.vue | src/views/Components | 229 |
| Checkbox.vue | src/views/Components | 109 |
| Collapse.vue | src/views/Components | 171 |
| ColorPicker.vue | src/views/Components | 43 |
| Countdown.vue | src/views/Components | 56 |
| Basic.vue | src/views/Components/data-table-components | 142 |
| Draggable.vue | src/views/Components/data-table-components | 158 |
| Merge.vue | src/views/Components/data-table-components | 230 |
| Selection.vue | src/views/Components/data-table-components | 186 |
| Sorting.vue | src/views/Components/data-table-components | 292 |
| DataTable.vue | src/views/Components | 44 |
| DatePicker.vue | src/views/Components | 235 |
| Descriptions.vue | src/views/Components | 63 |
| Dialog.vue | src/views/Components | 125 |
| Divider.vue | src/views/Components | 81 |
| Drawer.vue | src/views/Components | 295 |
| Dropdown.vue | src/views/Components | 482 |
| DynamicInput.vue | src/views/Components | 168 |
| DynamicTags.vue | src/views/Components | 41 |
| Ellipsis.vue | src/views/Components | 124 |
| Empty.vue | src/views/Components | 45 |
| Form.vue | src/views/Components | 270 |
| GradientText.vue | src/views/Components | 64 |
| Grid.vue | src/views/Components | 111 |
| Icon.vue | src/views/Components | 74 |
| Image.vue | src/views/Components | 86 |
| Input.vue | src/views/Components | 371 |
| InputNumber.vue | src/views/Components | 156 |
| Layout.vue | src/views/Components | 123 |
| LegacyGrid.vue | src/views/Components | 111 |
| List.vue | src/views/Components | 81 |
| Mention.vue | src/views/Components | 120 |
| Menu.vue | src/views/Components | 359 |
| Message.vue | src/views/Components | 95 |
| Modal.vue | src/views/Components | 76 |
| Notification.vue | src/views/Components | 196 |
| NumberAnimation.vue | src/views/Components | 73 |
| PageHeader.vue | src/views/Components | 172 |
| Pagination.vue | src/views/Components | 85 |
| Popconfirm.vue | src/views/Components | 67 |
| Popover.vue | src/views/Components | 304 |
| Popselect.vue | src/views/Components | 225 |
| Progress.vue | src/views/Components | 339 |
| Radio.vue | src/views/Components | 175 |
| Rate.vue | src/views/Components | 46 |
| Result.vue | src/views/Components | 192 |
| Scrollbar.vue | src/views/Components | 101 |
| Select.vue | src/views/Components | 245 |
| Skeleton.vue | src/views/Components | 97 |
| Slider.vue | src/views/Components | 102 |
| Space.vue | src/views/Components | 132 |
| Spin.vue | src/views/Components | 82 |
| Statistic.vue | src/views/Components | 64 |
| Steps.vue | src/views/Components | 264 |
| Switch.vue | src/views/Components | 99 |
| Table.vue | src/views/Components | 89 |
| Tabs.vue | src/views/Components | 236 |
| Tag.vue | src/views/Components | 148 |
| Thing.vue | src/views/Components | 209 |
| Time.vue | src/views/Components | 45 |
| Timeline.vue | src/views/Components | 118 |
| TimePicker.vue | src/views/Components | 115 |
| Tooltip.vue | src/views/Components | 79 |
| Transfer.vue | src/views/Components | 86 |
| Tree.vue | src/views/Components | 295 |
| TreeSelect.vue | src/views/Components | 443 |
| Typography.vue | src/views/Components | 208 |
| Upload.vue | src/views/Components | 190 |
| Watermark.vue | src/views/Components | 161 |
| Analytics.vue | src/views/Dashboard | 252 |
| eCommerce.vue | src/views/Dashboard | 239 |
| Milkdown.vue | src/views/Editors | 24 |
| Quill.vue | src/views/Editors | 49 |
| Tiptap.vue | src/views/Editors | 39 |
| Icons.vue | src/views | 138 |
| FullWidth.vue | src/views/Layout | 24 |
| LeftSidebar.vue | src/views/Layout | 49 |
| RightSidebar.vue | src/views/Layout | 49 |
| GoogleMaps.vue | src/views/Maps | 81 |
| Leaflet.vue | src/views/Maps | 64 |
| MapLibre.vue | src/views/Maps | 49 |
| VectorMap.vue | src/views/Maps | 157 |
| MultiLanguage.vue | src/views | 82 |
| NotFound.vue | src/views | 27 |
| Profile.vue | src/views | 190 |
| Base.vue | src/views/Tables | 76 |
| Basic.vue | src/views/Tables/data-tables-components | 92 |
| Draggable.vue | src/views/Tables/data-tables-components | 93 |
| Editable.vue | src/views/Tables/data-tables-components | 141 |
| Empty.vue | src/views/Tables/data-tables-components | 42 |
| Expand.vue | src/views/Tables/data-tables-components | 133 |
| LargeData.vue | src/views/Tables/data-tables-components | 74 |
| Merge.vue | src/views/Tables/data-tables-components | 125 |
| Selection.vue | src/views/Tables/data-tables-components | 173 |
| Sorting.vue | src/views/Tables/data-tables-components | 155 |
| DataTable.vue | src/views/Tables | 67 |
| Grid.vue | src/views/Tables | 157 |
| RefreshTool.vue | src/views/Toolbox | 40 |
| Tour.vue | src/views/Toolbox | 216 |
| Typography.vue | src/views | 469 |


---

### by line count

| File | Directory | Line Count |
|------|-----------|------------|
| FullCalendar.vue | src/views/Apps/Calendars | 840 |
| Chat.vue | src/views/Apps | 500 |
| Dropdown.vue | src/views/Components | 482 |
| Typography.vue | src/views | 469 |
| LayoutSettings.vue | src/components/common | 463 |
| Button.vue | src/views/Components | 461 |
| SegmentedPage.vue | src/components/common | 444 |
| TreeSelect.vue | src/views/Components | 443 |
| SearchDialog.vue | src/components/common | 420 |
| VueCal.vue | src/views/Apps/Calendars | 390 |
| Input.vue | src/views/Components | 371 |
| Menu.vue | src/views/Components | 359 |
| Mailbox.vue | src/views/Apps | 345 |
| Progress.vue | src/views/Components | 339 |
| Notes.vue | src/views/Apps | 313 |
| Popover.vue | src/views/Components | 304 |
| DemoApex.vue | src/components/charts | 301 |
| Drawer.vue | src/views/Components | 295 |
| Tree.vue | src/views/Components | 295 |
| Sorting.vue | src/views/Components/data-table-components | 292 |
| Kanban.vue | src/views/Apps | 286 |
| index.vue | src/app-layouts/common/Navbar | 285 |
| CardCombo1.vue | src/components/cards/combo | 276 |
| CardCombo3.vue | src/components/cards/combo | 271 |
| Form.vue | src/views/Components | 270 |
| Steps.vue | src/views/Components | 264 |
| CardSocial1.vue | src/components/cards/social | 257 |
| PinnedPagesV1.vue | src/app-layouts/common/Toolbar | 254 |
| Analytics.vue | src/views/Dashboard | 252 |
| Sync.vue | src/components/charts/demo-pages/apex-charts-components | 246 |
| Select.vue | src/views/Components | 245 |
| CardCombo5.vue | src/components/cards/combo | 240 |
| eCommerce.vue | src/views/Dashboard | 239 |
| MenuBar.vue | src/components/editors/Tiptap | 236 |
| Tabs.vue | src/views/Components | 236 |
| DatePicker.vue | src/views/Components | 235 |
| PinnedPagesV2.vue | src/app-layouts/common/Toolbar | 234 |
| Merge.vue | src/views/Components/data-table-components | 230 |
| Cascader.vue | src/views/Components | 229 |
| Popselect.vue | src/views/Components | 225 |
| List.vue | src/components/common/Notifications | 221 |
| Tour.vue | src/views/Toolbox | 216 |
| List.vue | src/views/Cards | 213 |
| Avatar.vue | src/views/Components | 212 |
| Realtime.vue | src/components/charts/demo-pages/apex-charts-components | 211 |
| index.vue | src/app-layouts/common/Toolbar | 209 |
| Thing.vue | src/views/Components | 209 |
| Typography.vue | src/views/Components | 208 |
| Combo.vue | src/views/Cards | 196 |
| Notification.vue | src/views/Components | 196 |
| CardExtra5.vue | src/components/cards/extra | 194 |
| Base.vue | src/components/tables | 194 |
| Email.vue | src/components/apps/Mailbox | 193 |
| Carousel.vue | src/views/Components | 193 |
| Result.vue | src/views/Components | 192 |
| Upload.vue | src/views/Components | 190 |
| Profile.vue | src/views | 190 |
| Selection.vue | src/views/Components/data-table-components | 186 |
| Radio.vue | src/views/Components | 175 |
| Brush.vue | src/components/charts/demo-pages/apex-charts-components | 173 |
| Selection.vue | src/views/Tables/data-tables-components | 173 |
| Map.vue | src/components/maps/maplibre | 172 |
| PageHeader.vue | src/views/Components | 172 |
| Collapse.vue | src/views/Components | 171 |
| DynamicInput.vue | src/views/Components | 168 |
| SidebarHeader.vue | src/app-layouts/HorizontalNav | 166 |
| SidebarHeader.vue | src/app-layouts/VerticalNav | 166 |
| CardActions.vue | src/components/cards | 166 |
| Watermark.vue | src/views/Components | 161 |
| Draggable.vue | src/views/Components/data-table-components | 158 |
| EmailToolbar.vue | src/components/apps/Mailbox | 157 |
| VectorMap.vue | src/views/Maps | 157 |
| Grid.vue | src/views/Tables | 157 |
| Breadcrumb.vue | src/views/Components | 156 |
| InputNumber.vue | src/views/Components | 156 |
| Sorting.vue | src/views/Tables/data-tables-components | 155 |
| EmailContent.vue | src/components/apps/Mailbox | 149 |
| AuthForm.vue | src/components/auth | 149 |
| Badge.vue | src/views/Components | 149 |
| ImageCropper.vue | src/components/common | 148 |
| Tag.vue | src/views/Components | 148 |
| List.vue | src/components/list | 147 |
| CardCombo6.vue | src/components/cards/combo | 142 |
| Basic.vue | src/views/Components/data-table-components | 142 |
| Editable.vue | src/views/Tables/data-tables-components | 141 |
| Icons.vue | src/views | 138 |
| Map.vue | src/components/maps/leaflet | 135 |
| Bar.vue | src/components/charts/demo-pages/apex-charts-components | 134 |
| Sidebar.vue | src/app-layouts/VerticalNav | 133 |
| Affix.vue | src/views/Components | 133 |
| Expand.vue | src/views/Tables/data-tables-components | 133 |
| Space.vue | src/views/Components | 132 |
| MainContainer.vue | src/app-layouts/VerticalNav | 131 |
| Search.vue | src/app-layouts/common/Toolbar | 129 |
| ActionToolbar.vue | src/components/apps/Mailbox | 127 |
| CardCodeExample.vue | src/components/cards | 127 |
| AutoComplete.vue | src/views/Components | 127 |
| ComposeView.vue | src/components/apps/Mailbox | 125 |
| CardExtra6.vue | src/components/cards/extra | 125 |
| Percentage.vue | src/components/common | 125 |
| Dialog.vue | src/views/Components | 125 |
| Merge.vue | src/views/Tables/data-tables-components | 125 |
| ThemeSwitch.vue | src/app-layouts/common/Toolbar | 124 |
| Ellipsis.vue | src/views/Components | 124 |
| Layout.vue | src/views/Components | 123 |
| Sidebar.vue | src/app-layouts/HorizontalNav | 122 |
| EventEditor.vue | src/components/apps/FullCalendar | 122 |
| Mention.vue | src/views/Components | 120 |
| Column.vue | src/components/charts/demo-pages/apex-charts-components | 119 |
| Timeline.vue | src/views/Components | 118 |
| CardEcommerce2.vue | src/components/cards/ecommerce | 116 |
| ImageLoader.vue | src/components/common | 116 |
| SignUp.vue | src/components/auth | 115 |
| TimePicker.vue | src/views/Components | 115 |
| CardEcommerce4.vue | src/components/cards/ecommerce | 114 |
| Grid.vue | src/views/Components | 111 |
| LegacyGrid.vue | src/views/Components | 111 |
| Radar.vue | src/components/charts/demo-pages/apex-charts-components | 110 |
| Checkbox.vue | src/views/Components | 109 |
| CardExtra7.vue | src/components/cards/extra | 108 |
| ProfileSettings.vue | src/components/profile | 107 |
| MainContainer.vue | src/app-layouts/HorizontalNav | 106 |
| Breadcrumb.vue | src/app-layouts/common/Toolbar | 104 |
| SignIn.vue | src/components/auth | 103 |
| Card.vue | src/views/Components | 103 |
| Slider.vue | src/views/Components | 102 |
| Settings.vue | src/components/auth | 101 |
| MenuItem.vue | src/components/editors/Tiptap | 101 |
| Scrollbar.vue | src/views/Components | 101 |
| Provider.vue | src/app-layouts/common | 99 |
| Switch.vue | src/views/Components | 99 |
| Skeleton.vue | src/views/Components | 97 |
| Message.vue | src/views/Components | 95 |
| index.vue | src/components/editors/Tiptap | 94 |
| Draggable.vue | src/views/Tables/data-tables-components | 93 |
| Basic.vue | src/views/Tables/data-tables-components | 92 |
| CardCombo4.vue | src/components/cards/combo | 90 |
| ProfileActivity.vue | src/components/profile | 89 |
| Table.vue | src/views/Components | 89 |
| CardCombo7.vue | src/components/cards/combo | 88 |
| App.vue | src | 86 |
| Image.vue | src/views/Components | 86 |
| Transfer.vue | src/views/Components | 86 |
| Pagination.vue | src/views/Components | 85 |
| MainFooter.vue | src/app-layouts/common | 84 |
| Notifications.vue | src/app-layouts/common/Toolbar | 84 |
| CardWrapper.vue | src/components/cards | 83 |
| Spin.vue | src/views/Components | 82 |
| MultiLanguage.vue | src/views | 82 |
| Divider.vue | src/views/Components | 81 |
| List.vue | src/views/Components | 81 |
| GoogleMaps.vue | src/views/Maps | 81 |
| Tooltip.vue | src/views/Components | 79 |
| TaskCard.vue | src/components/apps/Kanban | 78 |
| SidebarFooter.vue | src/app-layouts/HorizontalNav | 76 |
| SidebarFooter.vue | src/app-layouts/VerticalNav | 76 |
| Modal.vue | src/views/Components | 76 |
| Base.vue | src/views/Tables | 76 |
| Anchor.vue | src/views/Components | 75 |
| Icon.vue | src/views/Components | 74 |
| LargeData.vue | src/views/Tables/data-tables-components | 74 |
| NumberAnimation.vue | src/views/Components | 73 |
| CardCombo8.vue | src/components/cards/combo | 71 |
| TaskEditor.vue | src/components/apps/Kanban | 69 |
| CardExtra3.vue | src/components/cards/extra | 67 |
| Apex.vue | src/components/charts | 67 |
| ApexCharts.vue | src/views/Charts | 67 |
| Alert.vue | src/views/Components | 67 |
| Popconfirm.vue | src/views/Components | 67 |
| DataTable.vue | src/views/Tables | 67 |
| CardCombo2.vue | src/components/cards/combo | 65 |
| Bar.vue | src/components/charts/demo-pages/chartjs-components | 65 |
| Pie.vue | src/components/charts/demo-pages/apex-charts-components | 64 |
| Extra.vue | src/views/Cards | 64 |
| GradientText.vue | src/views/Components | 64 |
| Statistic.vue | src/views/Components | 64 |
| Leaflet.vue | src/views/Maps | 64 |
| Descriptions.vue | src/views/Components | 63 |
| CardComboIcon.vue | src/components/cards/combo | 62 |
| Line.vue | src/components/charts/demo-pages/chartjs-components | 62 |
| Calendar.vue | src/views/Components | 62 |
| ForgotPassword.vue | src/components/auth | 61 |
| LocaleSelect.vue | src/components/common | 60 |
| Icon.vue | src/components/common | 59 |
| LocaleSwitch.vue | src/app-layouts/common/Toolbar | 58 |
| HeaderBar.vue | src/app-layouts/HorizontalNav | 58 |
| Basic.vue | src/views/Cards | 58 |
| Logo.vue | src/app-layouts/common | 57 |
| CardBasic2.vue | src/components/cards/basic | 57 |
| Countdown.vue | src/views/Components | 56 |
| FileDrop.vue | src/components/common | 55 |
| Login.vue | src/views/Auth | 54 |
| MainContainer.vue | src/app-layouts/Blank | 51 |
| CardEcommerce1.vue | src/components/cards/ecommerce | 51 |
| Avatar.vue | src/app-layouts/common/Toolbar | 50 |
| Quill.vue | src/views/Editors | 49 |
| LeftSidebar.vue | src/views/Layout | 49 |
| RightSidebar.vue | src/views/Layout | 49 |
| MapLibre.vue | src/views/Maps | 49 |
| SplashScreen.vue | src/app-layouts/common | 47 |
| Rate.vue | src/views/Components | 46 |
| Empty.vue | src/views/Components | 45 |
| Time.vue | src/views/Components | 45 |
| DataTable.vue | src/views/Components | 44 |
| ColorPicker.vue | src/views/Components | 43 |
| Empty.vue | src/views/Tables/data-tables-components | 42 |
| DynamicTags.vue | src/views/Components | 41 |
| CardBasic5.vue | src/components/cards/basic | 40 |
| BackTop.vue | src/views/Components | 40 |
| RefreshTool.vue | src/views/Toolbox | 40 |
| Tiptap.vue | src/views/Editors | 39 |
| CardBasic6.vue | src/components/cards/basic | 38 |
| ColumnEditor.vue | src/components/apps/Kanban | 37 |
| Editor.vue | src/components/editors/Milkdown | 37 |
| FullscreenSwitch.vue | src/app-layouts/common/Toolbar | 36 |
| Navigator.vue | src/components/apps/Mailbox | 34 |
| Toolbar.vue | src/components/common/Notifications | 34 |
| CardBasic3.vue | src/components/cards/basic | 33 |
| ChartJS.vue | src/views/Charts | 33 |
| index.vue | src/app-layouts/HorizontalNav | 27 |
| CardExtra2.vue | src/components/cards/extra | 27 |
| NotFound.vue | src/views | 27 |
| CardEcommerce3.vue | src/components/cards/ecommerce | 26 |
| index.vue | src/app-layouts/VerticalNav | 24 |
| Milkdown.vue | src/views/Editors | 24 |
| FullWidth.vue | src/views/Layout | 24 |
| index.vue | src/app-layouts/Blank | 22 |
| CardBasic1.vue | src/components/cards/basic | 22 |
| Ecommerce.vue | src/views/Cards | 21 |
| CardExtra1.vue | src/components/cards/extra | 19 |
| CardBasic4.vue | src/components/cards/basic | 17 |
| LtrContext.vue | src/components/common | 16 |
| GlobalListener.vue | src/app-layouts/common | 14 |
| CardExtra4.vue | src/components/cards/extra | 14 |
| TestScope.vue | src/components/common | 12 |
| index.vue | src/components/editors/Milkdown | 11 |
