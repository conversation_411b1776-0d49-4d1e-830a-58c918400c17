This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

================================================================
Directory Structure for project au25-naiveui
================================================================
src/
  au24/
    connector/
      connector.ts

================================================================
Files
================================================================

================
File: src/au24/connector/connector.ts
================
import { useWebSocket } from "@vueuse/core"
//import * as pako from "pako"
import pako from "pako"
import { pretty, uuid } from "@/au24/utils"
import { onMounted } from "vue"
import { type EngineCommandEnvelope } from "@/au24/types/generated.js"
import { BrowserAgent } from "@/au24/connector/browser-agent.js"
import { UAParser } from "ua-parser-js"
import UnzipWorker from "./unzip-worker.ts?worker"
import { LiveStoreType } from "@/au24/stores/live-client-store.ts"
import { handleIncomingCommand } from "@/au24/connector/incoming-command-handler.ts"

/**
 * NOTE: Path comes from the WEBSOCKET_URL
 *  - session_id is created randomly, but can be overriden
 *
 * Usage: in main MainApp (or BookApp):
 *  const connector = useClientConnector({
 *       sid: '1', // if empty, then it uses the session_id
 *       show_connector_log: true // or false, depending on your needs
 *     });
 */

const worker = new UnzipWorker()

const DONT_CONNECT = false

let _store: LiveStoreType | null = null // will be passed in

worker.onmessage = function (event) {
	const cmd = event.data
	if (cmd && _store) {
		console.log("worker.onmessage: cmd", cmd)
		//	console.log('worker.onmessage received commond type:', cmd.command)
		handleIncomingCommand(cmd, _store)
	}
}

// NB this is the default session id, but can be overriden when connector is created
const random_session_id: string = uuid()

// used for testing:
export function encodeAndZip(obj: any): Uint8Array {
	const jsonString = JSON.stringify(obj)
	const encodedData = new TextEncoder().encode(jsonString)
	return pako.deflate(encodedData)
}

const browser_agent: BrowserAgent = new UAParser().getResult()

// Intended to work with both localhost and staging/production url.
// - http://localhost:8080 -> to ws://localhost:8080/socket/123456
// - https://dev1.auctionologies.com -> to https://dev1.auctionologies.com/socket/123456

const location = window.location
const calculated_websocked_url: string = import.meta.env.VITE_WEBSOCKET_URL
	? `${process.env.WEBSOCKET_URL}/`
	: `${location.protocol.replace("http", "ws")}//${location.host}/socket/`

console.log({ websocket_path: calculated_websocked_url })

// let clientConnectorInstance: ReturnType<typeof useClientConnector> | null = null
export type ClientConnectorType = ReturnType<typeof useClientConnector>

// static instance:
let clientConnectorInstance: ClientConnectorType | null = null

export type ConnectorOptions = {
	session_id?: string
	url?: string
	show_connector_log?: boolean
}

export function createClientConnector(store: LiveStoreType, o: ConnectorOptions) {
	_store = store

	if (clientConnectorInstance) {
		// hot reload will try this
		return clientConnectorInstance
	}

	clientConnectorInstance = useClientConnector(
		o.url || calculated_websocked_url,
		o.session_id || random_session_id,
		o.show_connector_log || false
	)

	return clientConnectorInstance
}

function useClientConnector(_url: string, sid: string, show_connector_log: boolean) {
	if (!_url.endsWith("/")) _url += "/"

	if (!_url.endsWith("socket/")) {
		alert("useClientConnector: url must have path 'socket/'!")
	}

	const url = _url + sid

	console.log("connector created with url: " + url)

	// const { on: onOutgoing } = useOutgoingCommandBus()
	// const { emit: emitSeconds } = useSecondsSinceLastMessageReceivedBus()

	onOutgoing((envelope: EngineCommandEnvelope) => {
		publish(envelope)
	})

	const seconds_timestamp = (): number => Math.round(Date.now() / 1000)
	let last_message_received = seconds_timestamp()

	function incrementSeconds() {
		const current_seconds_timestamp = seconds_timestamp()
		const interval = current_seconds_timestamp - last_message_received
		emitSeconds(interval)
		last_message_received = current_seconds_timestamp
		setTimeout(incrementSeconds, 1000)
	}

	const outgoingQueue: EngineCommandEnvelope[] = []

	const { send, status } = useWebSocket(url, {
		immediate: true,
		autoReconnect: { retries: Infinity, delay: 1000 },
		onConnected: ws => {
			console.log("WebSocket connected:", ws.url)
			ws.binaryType = "arraybuffer"
			while (outgoingQueue.length > 0) {
				const message = outgoingQueue.shift()
				if (message) {
					publish(message)
				}
			}
		},
		onDisconnected: () => console.log("WebSocket disconnected."),
		onError: event => {
			console.error("WebSocket error:", JSON.stringify(event))
			console.log()
		},
		onMessage: async (ws, event) => {
			try {
				if (DONT_CONNECT) {
					return
				}
				last_message_received = seconds_timestamp()
				const message = event.data
				console.log("WebSocket message received:", message)
				if (message) {
					worker.postMessage(message)
				}
			} catch (e) {
				console.log("useClientConnector:onMessage", e)
			}
		}
	})

	function publish(envelope: EngineCommandEnvelope) {
		envelope.session_id = sid
		if (DONT_CONNECT) {
			return
		}
		try {
			if (status.value === "OPEN") {
				const json = JSON.stringify(envelope)
				send(json)
				if (show_connector_log) {
					console.log(">>> sending engine command:")
					pretty({ command: envelope })
					console.log("<<<")
				}
			} else {
				outgoingQueue.push(envelope)
				if (show_connector_log) {
					console.log("not connected, putting message on the queue:", JSON.stringify(envelope, null, 2))
				}
			}
		} catch (error) {
			console.error("Error in publishing message:", error)
			// Optionally retry or handle the error further
		}
	}

	onMounted(() => {
		if (DONT_CONNECT) {
			return
		}
		incrementSeconds() // kicks off the timer
	})

	return {
		// Expose any necessary methods or properties
		publish
	}
}

/*
MESSAGE QUEUE:

	const messageQueue = ref<string[]>([]);
	function pushMessage(message: string) {
		if(message) {
			messageQueue.value.push(message);
		}
	}
	function handleMessage() {
		if (messageQueue.value.length > 0) {
			const message = messageQueue.value.shift();
			console.log('Handling message:', message);
			// Perform any necessary actions with the message
		}
	}
	watch(messageQueue, (newQueue) => {
		if (newQueue.length > 0) {
			handleMessage();
		}
	});

 */



================================================================
End of Codebase
================================================================
