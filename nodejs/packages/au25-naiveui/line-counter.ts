import { readFileSync, writeFileSync, readdirSync, statSync } from 'fs';
import { join, relative, resolve, dirname, basename } from 'path';

function walkDir(dir: string, pattern: RegExp): string[] {
    let results: string[] = [];
    const list = readdirSync(dir);
    
    for (const file of list) {
        const filePath = join(dir, file);
        const stat = statSync(filePath);
        
        if (stat.isDirectory()) {
            results = results.concat(walkDir(filePath, pattern));
        } else if (pattern.test(filePath)) {
            results.push(filePath);
        }
    }
    
    return results;
}

interface FileInfo {
    file: string;
    directory: string;
    lineCount: number;
    fullPath: string;
}

const main = async () => {
    const baseDir = 'src';
    const pattern = /\.vue$/;
    const globPattern = `${baseDir}/**/*.vue`;

    try {
        const files = walkDir(baseDir, pattern);
        let fileInfos: FileInfo[] = [];

        for (const file of files) {
            const content = readFileSync(file, 'utf-8');
            const lineCount = content.split('\n').length;
            const relativePath = relative(process.cwd(), file);
            const directory = dirname(relativePath);
            const filename = basename(relativePath);

            fileInfos.push({ file: filename, directory, lineCount, fullPath: relativePath });
        }

        let output = `## ${globPattern}\n\n`;

        // Table header
        const tableHeader = "| File | Directory | Line Count |\n|------|-----------|------------|\n";

        // Table 1: Sorted by File
        output += "### by filename\n\n";
        output += tableHeader;
        fileInfos.sort((a, b) => a.file.localeCompare(b.file));
        for (const { file, directory, lineCount } of fileInfos) {
            output += `| ${file} | ${directory} | ${lineCount} |\n`;
        }

        // Two line separation
        output += "\n\n---\n\n";

        // Table 2: Sorted by Path
        output += "### by path\n\n";
        output += tableHeader;
        fileInfos.sort((a, b) => a.fullPath.localeCompare(b.fullPath));
        for (const { file, directory, lineCount } of fileInfos) {
            output += `| ${file} | ${directory} | ${lineCount} |\n`;
        }

        // Two line separation
        output += "\n\n---\n\n";

        // Table 3: Sorted by Line Count
        output += "### by line count\n\n";
        output += tableHeader;
        fileInfos.sort((a, b) => b.lineCount - a.lineCount);
        for (const { file, directory, lineCount } of fileInfos) {
            output += `| ${file} | ${directory} | ${lineCount} |\n`;
        }

        console.log(output);

        const outputPath = resolve('vue-file-stats.md');
        writeFileSync(outputPath, output);
        console.log(`\nFile written to: ${outputPath}`);
        console.log(`Total .vue files found: ${fileInfos.length}`);

    } catch (error) {
        console.error('An error occurred:', error.message);
        process.exit(1);
    }
};

main();