{
	"compilerOptions": {
		"target": "ES2020",
		"module": "ESNext",
		"moduleResolution": "node",
		"lib": ["ES2015", "DOM", "DOM.Iterable"],
		"jsx": "preserve",
		"composite": true,
		"strict": true,
		"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"],
			"au25-connector": ["../../packages/au25-connector"], // Map to the directory
			"au25-connector/*": ["../../packages/au25-connector/*"] // Map sub-paths relative to the directory
		},
		"types": ["node", "naive-ui/volar"],
		"allowJs": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowImportingTsExtensions": true,
		"noEmit": true,
		"isolatedModules": true,
		"verbatimModuleSyntax": false
	},
	"include": ["src/**/*.d.ts", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.json", "src/**/*.js"],
	"exclude": ["src/**/__tests__/*"]
}
