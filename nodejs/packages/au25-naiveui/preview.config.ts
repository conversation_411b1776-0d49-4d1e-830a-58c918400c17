import { defineConfig } from "@previewjs/config"
import { fileURLToPath, URL } from "node:url"
import { loadEnv } from 'vite'
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import Components from "unplugin-vue-components/vite"
import svgLoader from "vite-svg-loader"
import type { UserConfig } from 'vite'
import type { PreviewConfig } from "@previewjs/config"

export default defineConfig(({ mode }: { mode: string }): PreviewConfig => {
    const env = loadEnv(mode, process.cwd(), '')

    return {
        framework: {
            name: "vue3" as const,
            include: [
                "**/*.vue",
                "**/*.ts",
                "**/*.tsx"
            ]
        },
        wrapper: {
            path: "src/PreviewWrapper.vue",
        },
        vite: {
            plugins: [
                vue({
                    script: {
                        defineModel: true,
                        propsDestructure: true,
                    },
                    template: {
                        compilerOptions: {
                            isCustomElement: (tag) => false
                        }
                    }
                }),
                vueJsx(),
                svg<PERSON>oader(),
                Components({
                    dirs: [
                        "src/components/cards",
                        "src/au24/view/forms/auction-settings/login-panel"
                    ],
                    dts: "src/unplugin.components.d.ts"
                })
            ],
            resolve: {
                alias: {
                    '@': fileURLToPath(new URL("./src", import.meta.url))
                }
            },
            define: {
                'import.meta.env.VITE_APP_MODE': JSON.stringify(env.VITE_APP_MODE || 'APP_ORIGINAL'),
                '__APP_ENV__': JSON.stringify(env.APP_ENV || 'development'),
                'process.env': process.env
            },
            optimizeDeps: {
                include: [
                    "pinia",
                    "pinia-plugin-persistedstate",
                    "naive-ui"
                ]
            },
            server: {
                fs: {
                    strict: false
                }
            }
        } satisfies UserConfig
    }
})
