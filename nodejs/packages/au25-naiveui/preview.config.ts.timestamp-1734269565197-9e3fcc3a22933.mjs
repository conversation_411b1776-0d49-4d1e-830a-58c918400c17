// preview.config.ts
import { defineConfig } from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/@previewjs/config/dist/index.mjs";
import { fileURLToPath, URL } from "node:url";
import { loadEnv } from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/vite/dist/node/index.js";
import vue from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import Components from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/unplugin-vue-components/dist/vite.js";
import svgLoader from "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/node_modules/vite-svg-loader/index.js";
var __vite_injected_original_import_meta_url = "file:///Users/<USER>/au/codebase/2024/au24-auction/projects/node/packages/pinx-vue-full-version/preview.config.ts";
var preview_config_default = defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  return {
    framework: {
      name: "vue3",
      include: [
        "**/*.vue",
        "**/*.ts",
        "**/*.tsx"
      ]
    },
    wrapper: {
      path: "src/PreviewWrapper.vue"
    },
    vite: {
      plugins: [
        vue({
          script: {
            defineModel: true,
            propsDestructure: true
          },
          template: {
            compilerOptions: {
              isCustomElement: (tag) => false
            }
          }
        }),
        vueJsx(),
        svgLoader(),
        Components({
          dirs: ["src/components/cards"],
          dts: "src/unplugin.components.d.ts"
        })
      ],
      resolve: {
        alias: {
          "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
        }
      },
      define: {
        "import.meta.env.VITE_APP_MODE": JSON.stringify(env.VITE_APP_MODE || "APP_ORIGINAL"),
        "__APP_ENV__": JSON.stringify(env.APP_ENV || "development"),
        "process.env": process.env
      },
      optimizeDeps: {
        include: [
          "pinia",
          "pinia-plugin-persistedstate",
          "naive-ui"
        ]
      },
      server: {
        fs: {
          strict: false
        }
      }
    }
  };
});
export {
  preview_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
