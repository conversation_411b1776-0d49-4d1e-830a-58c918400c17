// vitest.config.ts
import { fileURLToPath as fileURLToPath2 } from "node:url";
import { configDefaults, defineConfig as defineConfig2, mergeConfig } from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/vitest@2.1.8_@types+node@22.10.5_jsdom@25.0.1_less@4.2.1_sass@1.83.1/node_modules/vitest/dist/config.js";

// vite.config.ts
import process from "node:process";
import { fileURLToPath, URL as URL2 } from "node:url";
import vue from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/@vitejs+plugin-vue@5.2.1_vite@5.4.11_@types+node@22.10.5_less@4.2.1_sass@1.83.1__vue@3.5.13_typescript@5.5.4_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.1.1_vite@5.4.11_@types+node@22.10.5_less@4.2.1_sass@1.83.1__vue@3.5.13_typescript@5.5.4_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import Components from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/unplugin-vue-components@0.27.5_@babel+parser@7.26.5_@nuxt+kit@3.15.1_rollup@4.30.1__rollup@4._abxmyep2q5dxl2wztoho6xhmjm/node_modules/unplugin-vue-components/dist/vite.js";
import { defineConfig, loadEnv } from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/vite@5.4.11_@types+node@22.10.5_less@4.2.1_sass@1.83.1/node_modules/vite/dist/node/index.js";
import svgLoader from "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/node_modules/.pnpm/vite-svg-loader@5.1.0_vue@3.5.13_typescript@5.5.4_/node_modules/vite-svg-loader/index.js";
var __vite_injected_original_import_meta_url = "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/vite.config.ts";
var vite_config_default = defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd(), "") };
  return {
    plugins: [
      vue({
        script: {
          defineModel: true
        }
      }),
      vueJsx(),
      //VueDevTools(),
      svgLoader(),
      Components({
        dirs: ["src/components/cards"],
        dts: "src/unplugin.components.d.ts"
      })
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL2("./src", __vite_injected_original_import_meta_url))
      }
    },
    optimizeDeps: {
      include: ["@fawmi/vue-google-maps", "fast-deep-equal"]
    },
    define: {
      __APP_ENV__: JSON.stringify(process.env.APP_ENV)
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ["legacy-js-api"]
        }
      }
    }
  };
});

// vitest.config.ts
var __vite_injected_original_import_meta_url2 = "file:///Users/<USER>/au/codebase/auctions/au25-auction/nodejs/packages/au25-naiveui/vitest.config.ts";
var vitest_config_default = defineConfig2(
  (env) => mergeConfig(
    vite_config_default(env),
    defineConfig2({
      test: {
        environment: "jsdom",
        exclude: [...configDefaults.exclude, "e2e/*"],
        root: fileURLToPath2(new URL("./", __vite_injected_original_import_meta_url2))
      }
    })
  )
);
export {
  vitest_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
