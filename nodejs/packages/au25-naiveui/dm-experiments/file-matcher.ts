/// <reference types="bun-types" />

/** 
 *  THIS FILE DOESN'T WORK
 *  - use: /usr/local/bin/file-matcher
 *  - on: macbook and ryzen
 * 
*/


// Feb 10, 2025: from au25-naiveui 

import { glob } from 'bun';
import { readdir, readFile, writeFile } from 'bun:fs';

interface Args {
  patterns: string[];
  filePatterns: string[];
  outputFile: string;
  showHelp: boolean;
}

function parseArgs(argv: string[]): Args {
  const args: Args = { patterns: [], filePatterns: [], outputFile: '', showHelp: false };

  for (let i = 2; i < argv.length; i++) {
    const arg = argv[i];
    if (arg === '-f') {
      if (i + 1 < argv.length) {
        args.filePatterns.push(argv[i + 1]);
        i++;
      } else {
        throw new Error('Error: -f flag requires a filename. Use -h for usage.');
      }
    } else if (arg === '-o') {
      if (i + 1 < argv.length) {
        if (args.outputFile !== '') {
          throw new Error('Error: Multiple output files specified. Use -h for usage.');
        }
        args.outputFile = argv[i + 1];
        i++;
      } else {
        throw new Error('Error: -o flag requires a filename. Use -h for usage.');
      }
    } else if (arg === '-h' || arg === '--help') {
      args.showHelp = true;
    } else {
      args.patterns.push(arg);
    }
  }
  return args;
}

async function readPatternsFromFile(filePath: string): Promise<string[]> {
  try {
    const data = await readFile(filePath, 'utf8');
    return data.split(/\r?\n/).map(line => line.trim()).filter(line => line && !line.startsWith('#'));
  } catch (err) {
    throw new Error(`Error: Cannot read file "${filePath}". Use -h for usage.`);
  }
}

async function main() {
  try {
    const { patterns, filePatterns, outputFile, showHelp } = parseArgs(process.argv);

    if (showHelp || process.argv.length <= 2) {
      console.log(`
Usage:
  bun file-matcher.ts <patterns...> [-f <pattern file>] -o <output file>

Options:
  -f <pattern file>    Specify a file containing glob patterns (one per line).
  -o <output file>     Specify the output file to write results.
  -h, --help           Display this help message.

Examples:
  bun file-matcher.ts src/**/*.{vue,ts} -f globpatterns.txt -o output.md
  bun file-matcher.ts -h
`);
      process.exit(0);
    }

    if (!outputFile) {
      console.error('Error: Output file not specified. Use the -o flag to specify an output file.');
      process.exit(1);
    }

    let allPatterns = [...patterns];

    if (filePatterns.length > 0) {
      for (const filePath of filePatterns) {
        const fileContentPatterns = await readPatternsFromFile(filePath);
        allPatterns = allPatterns.concat(fileContentPatterns);
      }
    }

    if (allPatterns.length === 0) {
      console.error('Error: No glob patterns specified. Use -h for usage.');
      process.exit(1);
    }

    const inclusionPatterns: string[] = [];
    const exclusionPatterns: string[] = [];

    for (const pattern of allPatterns) {
      if (pattern.startsWith('!')) {
        exclusionPatterns.push(pattern.slice(1));
      } else {
        inclusionPatterns.push(pattern);
      }
    }

    const matchedFilesSet = new Set<string>();

    for (const inclusionPattern of inclusionPatterns) {
      for await (const file of glob(inclusionPattern)) {
        let included = true;
        for (const exclusionPattern of exclusionPatterns) {
          if (await glob(exclusionPattern).then(files => files.includes(file))) {
            included = false;
            break;
          }
        }
        if (included) {
          matchedFilesSet.add(file);
        }
      }
    }

    const matchedFiles = Array.from(matchedFilesSet);
    matchedFiles.sort();

    console.log(`found ${matchedFiles.length} files:`);
    for (const file of matchedFiles) {
      console.log(`- ${file}`);
    }

    let outputContent = `found ${matchedFiles.length} files:\n`;
    for (const file of matchedFiles) {
      outputContent += `- ${file}\n`;
    }

    for (const file of matchedFiles) {
      try {
        const content = await readFile(file, 'utf8');
        outputContent += `\n${file}\n\`\`\`\`\n${content}\n\`\`\`\`\n`;
      } catch (err) {
        console.error(`Error reading file "${file}": ${err.message}.`);
      }
    }

    try {
      await writeFile(outputFile, outputContent, 'utf8');
      console.log(`Output successfully written to "${outputFile}"`);
    } catch (err) {
      console.error(`Error writing to file "${outputFile}": ${err.message}`);
      process.exit(1);
    }
  } catch (err) {
    console.error(err.message);
    process.exit(1);
  }
}

main();