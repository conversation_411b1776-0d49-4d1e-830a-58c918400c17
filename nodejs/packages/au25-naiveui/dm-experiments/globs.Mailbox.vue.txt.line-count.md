| File | Line Count |
|------|------------|
| src/views/Apps/Mailbox.vue | 345 |
| src/components/apps/Mailbox/ActionToolbar.vue | 127 |
| src/components/apps/Mailbox/ComposeView.vue | 125 |
| src/components/apps/Mailbox/Email.vue | 193 |
| src/components/apps/Mailbox/EmailContent.vue | 149 |
| src/components/apps/Mailbox/EmailToolbar.vue | 157 |
| src/components/apps/Mailbox/Navigator.vue | 34 |
| src/components/common/Icon.vue | 59 |
| src/components/common/SegmentedPage.vue | 444 |
| src/composables/useHideLayoutFooter.ts | 20 |
| src/design-tokens.json | 168 |
| src/mock/mailbox.ts | 102 |
| src/stores/apps/useMailboxStore.ts | 37 |
| src/stores/theme.ts | 265 |
| src/theme/index.ts | 248 |
| src/types/theme.d.ts | 33 |
| src/utils/dayjs.ts | 21 |
| src/utils/index.ts | 53 |
| src/utils/theme.ts | 83 |

total lines = 2663