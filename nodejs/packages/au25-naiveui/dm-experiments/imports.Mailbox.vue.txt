import "@/assets/scss/quill-override.scss"
import { faker } from "@faker-js/faker"
import { Icon, type IconifyIcon, loadIcon } from "@iconify/vue"
import { onClickOutside, useWindowSize } from "@vueuse/core"
import { colord } from "colord"
import dayjs from "dayjs"
import locale_en from "dayjs/locale/en.js"
import customParseFormat from "dayjs/plugin/customParseFormat"
import duration from "dayjs/plugin/duration"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import relativeTime from "dayjs/plugin/relativeTime"
import timezone from "dayjs/plugin/timezone"
import utc from "dayjs/plugin/utc"
import { isMobile as detectMobile } from "detect-touch-device"
import _get from "lodash/get"
import _pick from "lodash/pick"
import _set from "lodash/set"
import { darkTheme, type GlobalThemeOverrides, lightTheme, type MenuOption, NAutoComplete, NAvatar, NButton, NButtonGroup, NCheckbox, NDropdown, NForm, NFormItem, NIcon, NIconWrapper, NInput, NInputGroup, NMenu, NScrollbar, NSplit, NTime, NTooltip, type ThemeCommonVars, useOsTheme } from "naive-ui"
import { BuiltInGlobalTheme } from "naive-ui/es/themes/interface"
import process from "node:process"
import { acceptHMRUpdate, defineStore } from "pinia"
import { type Component, computed, type ComputedRef, defineAsyncComponent, h, onBeforeMount, onBeforeUnmount, onMounted, ref, toRefs, useSlots, watch, watchEffect } from "vue"