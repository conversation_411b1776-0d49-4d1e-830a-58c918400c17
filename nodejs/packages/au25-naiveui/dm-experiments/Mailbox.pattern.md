found 19 files:
- src/components/apps/Mailbox/ActionToolbar.vue
- src/components/apps/Mailbox/ComposeView.vue
- src/components/apps/Mailbox/Email.vue
- src/components/apps/Mailbox/EmailContent.vue
- src/components/apps/Mailbox/EmailToolbar.vue
- src/components/apps/Mailbox/Navigator.vue
- src/components/common/Icon.vue
- src/components/common/SegmentedPage.vue
- src/composables/useHideLayoutFooter.ts
- src/design-tokens.json
- src/mock/mailbox.ts
- src/stores/apps/useMailboxStore.ts
- src/stores/theme.ts
- src/theme/index.ts
- src/types/theme.d.ts
- src/utils/dayjs.ts
- src/utils/index.ts
- src/utils/theme.ts
- src/views/Apps/Mailbox.vue

src/components/apps/Mailbox/ActionToolbar.vue
```vue
<template>
	<div class="action-toolbar flex items-center">
		<div class="flex">
			<n-checkbox
				:checked="checkControl === 1"
				:indeterminate="checkControl === 2"
				size="large"
				@click="toggleCheckAll()"
			/>
		</div>
		<div v-if="checkControl" class="flex grow items-center gap-3">
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="TrashIcon" />
					</n-button>
				</template>
				<span>Delete</span>
			</n-tooltip>

			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="LabelOutIcon" />
					</n-button>
				</template>
				<span>Add label</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="FolderIcon" />
					</n-button>
				</template>
				<span>Move to folder</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="StarredIcon" />
					</n-button>
				</template>
				<span>Star</span>
			</n-tooltip>
		</div>
		<div v-if="!checkControl" class="flex grow search-box">
			<n-input v-model:value="search" placeholder="Search..." clearable size="medium">
				<template #prefix>
					<Icon :name="SearchIcon" />
				</template>
			</n-input>
		</div>
		<div v-if="!checkControl" class="flex justify-center opacity-50">
			<n-button text>
				<Icon :size="18" :name="RefreshIcon" />
			</n-button>
		</div>
		<div v-if="!checkControl" class="menu-btn flex justify-center opacity-50">
			<n-button text @click="sidebarOpen = true">
				<Icon :size="24" :name="MenuIcon" />
			</n-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import Icon from "@/components/common/Icon.vue"
import { onClickOutside } from "@vueuse/core"
import { NButton, NCheckbox, NInput, NTooltip } from "naive-ui"
import { ref, toRefs } from "vue"

const props = defineProps<{
	checkControl: number
}>()

const emit = defineEmits<{
	(e: "toggleCheckAll"): void
}>()

const { checkControl } = toRefs(props)

const search = defineModel<string>("search", { default: "" })

const StarredIcon = "carbon:star"
const TrashIcon = "carbon:trash-can"
const LabelOutIcon = "carbon:bookmark"
const MenuIcon = "ion:menu-sharp"
const SearchIcon = "carbon:search"
const FolderIcon = "carbon:folder-move-to"
const RefreshIcon = "ion:reload"

const sidebarOpen = ref(false)

const sidebar = ref(null)
onClickOutside(sidebar, () => (sidebarOpen.value = false))

function toggleCheckAll() {
	emit("toggleCheckAll")
}
</script>

<style lang="scss" scoped>
.action-toolbar {

	gap: 18px;

	.menu-btn,
	.new-btn {
		display: none;
	}

	.search-box {
		margin: 0px 12px;
		.n-input {
			background-color: transparent;

			:deep() {
				.n-input__border,
				.n-input__state-border {
					display: none;
				}
			}
		}
	}
}
</style>

```
src/components/apps/Mailbox/ComposeView.vue
```vue
<template>
	<div class="compose-view flex flex-col grow">
		<n-form label-placement="left" size="small" label-width="auto">
			<n-form-item label="To">
				<n-input-group>
					<n-auto-complete
						v-model:value="emailForm.email"
						:input-props="{
							autocomplete: 'disabled'
						}"
						:options="autoCompleteOptions"
						placeholder="Email"
					/>
					<n-button type="tertiary">CC</n-button>
					<n-button type="tertiary">BCC</n-button>
				</n-input-group>
			</n-form-item>
			<n-form-item label="Subject">
				<n-input placeholder="Message subject..." />
			</n-form-item>
		</n-form>
		<div class="compose-view-attachments flex justify-end">
			<n-button ghost>
				<template #icon>
					<Icon :name="DocumentAddIcon" />
				</template>

				Add attachment
			</n-button>
		</div>
		<div class="compose-view-body grow flex flex-col scrollbar-styled">
			<QuillEditor v-if="mounted" theme="snow" toolbar="minimal" @blur="resetScroll()" />
		</div>
		<div class="compose-view-footer flex justify-end">
			<n-button-group>
				<n-button type="primary" ghost>
					<template #icon>
						<Icon :name="SentIcon" />
					</template>
					Send
				</n-button>
				<n-dropdown
					trigger="click"
					:options="[
						{
							label: 'Save as draft',
							key: 'Save as draft'
						},
						{
							label: 'Postponed sending',
							key: 'Postponed sending'
						}
					]"
				>
					<n-button type="primary" ghost>
						<template #icon>
							<Icon :name="ChevronDownIcon" />
						</template>
					</n-button>
				</n-dropdown>
			</n-button-group>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { Email } from "@/mock/mailbox"
import Icon from "@/components/common/Icon.vue"
import { NAutoComplete, NButton, NButtonGroup, NDropdown, NForm, NFormItem, NInput, NInputGroup } from "naive-ui"
import { type Component, computed, defineAsyncComponent, onMounted, ref, toRefs } from "vue"
import "@/assets/scss/quill-override.scss"

const props = defineProps<{
	email: Partial<Email>
}>()

const QuillEditor = defineAsyncComponent<Component>(() => {
	return (async () => {
		const { QuillEditor } = await import("@vueup/vue-quill")
		return QuillEditor
	})()
})

const { email: emailForm } = toRefs(props)

const SentIcon = "carbon:send"
const ChevronDownIcon = "carbon:chevron-down"
const DocumentAddIcon = "carbon:document-add"
const mounted = ref(false)
const autoCompleteOptions = computed(() => {
	return ["@gmail.com", "@live.com", "@qq.com", "@me.com"].map(suffix => {
		const prefix = emailForm.value?.email?.split("@")[0]
		return {
			label: prefix + suffix,
			value: prefix + suffix
		}
	})
})

function resetScroll() {
	window.scrollTo(0, 0)
}

onMounted(() => {
	mounted.value = true
})
</script>

<style lang="scss" scoped>
.compose-view {

	.n-form {
		:deep(.n-form-item-feedback-wrapper) {
			min-height: 10px;
		}
	}

	.compose-view-body {
		overflow: hidden;
		height: 0;
		padding: 20px 0px;
	}
}
</style>

```
src/components/apps/Mailbox/Email.vue
```vue
<template>
	<div class="email flex items-center" :class="{ selected: email.selected, seen: email.seen }" @click="select(email)">
		<div class="check">
			<n-checkbox :checked="email.selected" size="large" @click.stop="toggleCheck(email)" />
		</div>
		<div class="starred flex" :class="{ 'opacity-50': !email.starred }">
			<n-button text @click.stop="toggleStar(email)">
				<Icon v-if="email.starred" :size="16" :name="StarActiveIcon" :color="primaryColor" />
				<Icon v-else :size="16" :name="StarIcon" />
			</n-button>
		</div>
		<div class="avatar flex">
			<n-avatar round size="small" :src="email.avatar" :img-props="{ alt: `${email.name}-avatar` }" />
		</div>
		<div class="title grow">
			<span class="name">
				{{ email.name }}
			</span>
			<span class="subject">
				{{ email.subject }}
			</span>
		</div>
		<div class="labels flex">
			<Icon
				v-for="label of email.labels"
				:key="label.id"
				:size="16"
				:color="labelsColors[label.id]"
				:name="LabelIcon"
			/>
		</div>
		<div v-if="email.attachments.length" class="attachments flex">
			<Icon :size="16" :name="AttachmentIcon" />
		</div>
		<div class="date text-secondary-color">
			{{ email.dateText }}
		</div>
		<div class="actions text-secondary-color flex items-start gap-3">
			<n-button text>
				<Icon :size="20" :name="TrashIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="LabelOutIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="FolderIcon" />
			</n-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { Email } from "@/mock/mailbox"
import Icon from "@/components/common/Icon.vue"
import { useMailboxStore } from "@/stores/apps/useMailboxStore"
import { useThemeStore } from "@/stores/theme"
import { NAvatar, NButton, NCheckbox } from "naive-ui"
import { computed, toRefs } from "vue"

const props = defineProps<{
	email: Email
}>()
const emit = defineEmits<{
	(e: "select", value: Email): void
}>()

const { email } = toRefs(props)

const StarActiveIcon = "carbon:star-filled"
const StarIcon = "carbon:star"
const TrashIcon = "carbon:trash-can"
const LabelIcon = "carbon:bookmark-filled"
const LabelOutIcon = "carbon:bookmark"
const AttachmentIcon = "carbon:attachment"
const FolderIcon = "carbon:folder-move-to"
const mailboxStore = useMailboxStore()
const themeStore = useThemeStore()
const primaryColor = computed(() => themeStore.primaryColor)
const secondaryColors = computed(() => themeStore.secondaryColors)

const labelsColors = {
	personal: secondaryColors.value.secondary1,
	office: secondaryColors.value.secondary2,
	important: secondaryColors.value.secondary3,
	shop: secondaryColors.value.secondary4
} as unknown as { [key: string]: string }

function select(email: Email) {
	emit("select", email)
}

function toggleCheck(email: Email) {
	mailboxStore.toggleCheck(email)
}

function toggleStar(email: Email) {
	mailboxStore.toggleStar(email)
}
</script>

<style lang="scss" scoped>
.email {
	height: 52px;
	padding: 0 30px;
	border-bottom: var(--border-small-050);
	gap: 18px;
	line-height: 1.2;
	white-space: nowrap;
	cursor: pointer;
	transition: all 0.1s ease-in;
	container-type: inline-size;

	.title {
		overflow: hidden;
		width: 0;
		text-overflow: ellipsis;
		font-size: 15px;

		.name {
			margin-right: 14px;
		}
		.subject {
			font-weight: bold;
		}
	}

	.actions {
		display: none;
	}

	&.seen {
		background-color: var(--bg-secondary-color);
		.title {
			opacity: 0.85;
			.subject {
				font-weight: normal;
			}
		}
	}

	&.selected {
		background-color: var(--primary-005-color);
	}

	&:hover {
		box-shadow: 0px 0px 0px 1px var(--primary-050-color) inset;

		.actions {
			display: flex;
		}
		.labels,
		.attachments,
		.date {
			display: none;
		}
	}

	@container (max-width: 760px) {
		.title {
			display: flex;
			flex-direction: column;

			.name,
			.subject {
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}
	@container (max-width: 500px) {
		.avatar {
			display: none;
		}
	}
	@container (max-width: 360px) {
		.labels {
			display: none;
		}
	}
}

@media (max-width: 700px) {
	.email {
		gap: 14px;
		padding: 0 20px;

		.title {
			font-size: 14px;
		}
	}
}
</style>

```
src/components/apps/Mailbox/EmailContent.vue
```vue
<template>
	<div class="email-content">
		<div class="email-sender flex flex-wrap items-center">
			<div class="avatar flex">
				<n-avatar round :size="45" :src="email.avatar" :img-props="{ alt: `${email.name}-avatar` }" />
			</div>
			<div class="info grow flex flex-wrap items-center">
				<div class="title grow flex flex-col">
					<span class="name">
						{{ email.name }}
					</span>
					<span class="email">
						{{ email.email }}
					</span>
				</div>
				<div class="date">
					<n-time :time="email.date" format="d MMM @ HH:mm" />
				</div>
			</div>
		</div>
		<div class="email-subject">
			<span class="subject">
				{{ email.subject }}
			</span>
			<span
				v-for="label of email.labels"
				:key="label.id"
				class="label custom-label"
				:style="`--label-color:${labelsColors[label.id]}`"
			>
				{{ label.title }}
			</span>
		</div>
		<div class="email-body" v-html="email.body" />
		<div v-if="email.attachments.length" class="email-attachments flex flex-wrap">
			<div v-for="attachment of email.attachments" :key="attachment.name" class="attachment-item flex">
				<div class="attachment-icon">
					<Icon :size="26" :name="FileIcon" />
				</div>
				<div class="attachment-info">
					<div class="attachment-name">{{ attachment.name }}</div>
					<div class="attachment-size">{{ attachment.size }}</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { Email } from "@/mock/mailbox"
import Icon from "@/components/common/Icon.vue"
import { useThemeStore } from "@/stores/theme"
import { NAvatar, NTime } from "naive-ui"
import { computed, toRefs } from "vue"

const props = defineProps<{
	email: Email
}>()

const FileIcon = "tabler:file-invoice"

const { email } = toRefs(props)

const secondaryColors = computed(() => useThemeStore().secondaryColors)

const labelsColors = {
	personal: secondaryColors.value.secondary1,
	office: secondaryColors.value.secondary2,
	important: secondaryColors.value.secondary3,
	shop: secondaryColors.value.secondary4
} as unknown as { [key: string]: string }
</script>

<style lang="scss" scoped>
.email-content {

	.email-sender {
		gap: 18px;
		margin-bottom: 20px;

		.title {
			margin-right: 30px;
			.name {
				font-size: 18px;
			}
			.email {
				opacity: 0.8;
			}
		}

		.date {
			font-size: 16px;
			opacity: 0.7;
		}
	}

	.email-subject {
		line-height: 1.25;

		.subject {
			font-size: 20px;
			font-weight: bold;
			margin-right: 10px;
		}
		.label {
			top: -2px;
		}
	}

	.email-body {
		padding: 20px 0px;
		line-height: 1.35;
		font-size: 16px;
	}

	.email-attachments {
		padding: 20px 0px;
		gap: 20px;

		.attachment-item {
			background-color: var(--primary-010-color);
			padding: 14px;
			border-radius: var(--border-radius);
			max-width: 100%;

			.attachment-icon {
				margin-top: 3px;
				margin-right: 10px;
			}

			.attachment-info {
				padding-right: 5px;
				line-height: 1.4;
				.attachment-name {
					font-size: 14px;
					word-break: break-all;
					line-height: 1.2;
				}
				.attachment-size {
					font-size: 10px;
					opacity: 0.5;
					margin-top: 3px;
				}
			}
		}
	}
}
</style>

```
src/components/apps/Mailbox/EmailToolbar.vue
```vue
<template>
	<div class="email-toolbar flex items-center">
		<div class="actions-btns flex items-center gap-2">
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="TrashIcon" />
					</n-button>
				</template>
				<span>Delete</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="LabelOutIcon" />
					</n-button>
				</template>
				<span>Add label</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="FolderIcon" />
					</n-button>
				</template>
				<span>Move to folder</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="PrinterIcon" />
					</n-button>
				</template>
				<span>Print</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text @click.stop="toggleStar(email)">
						<Icon v-if="email.starred" :size="20" :name="StarActiveIcon" :color="primaryColor" />
						<Icon v-else :size="20" :name="StarIcon" />
					</n-button>
				</template>
				<span>Star</span>
			</n-tooltip>
		</div>
		<div class="menu-btns flex items-center">
			<n-dropdown :options="menuOptions">
				<n-button text>
					<Icon :size="24" :name="MenuHorizontalIcon" />
				</n-button>
			</n-dropdown>
		</div>
		<div class="grow" />
		<div class="reply-btns flex items-center gap-2">
			<n-button text>
				<Icon :size="20" :name="ReplyIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="ReplyAllIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="ForwardIcon" />
			</n-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { Email } from "@/mock/mailbox"
import Icon from "@/components/common/Icon.vue"
import { useMailboxStore } from "@/stores/apps/useMailboxStore"
import { useThemeStore } from "@/stores/theme"
import { renderIcon } from "@/utils"
import { NButton, NDropdown, NTooltip } from "naive-ui"
import { computed, toRefs } from "vue"

const props = defineProps<{
	email: Email
}>()
const StarActiveIcon = "carbon:star-filled"
const StarIcon = "carbon:star"
const TrashIcon = "carbon:trash-can"
const LabelOutIcon = "carbon:bookmark"
const MenuHorizontalIcon = "carbon:overflow-menu-horizontal"
const FolderIcon = "carbon:folder-move-to"
const PrinterIcon = "carbon:printer"
const ReplyAllIcon = "fluent:arrow-reply-all-20-filled"
const ReplyIcon = "fluent:arrow-reply-20-filled"
const ForwardIcon = "fluent:arrow-forward-20-filled"

const { email } = toRefs(props)

const store = useMailboxStore()

const primaryColor = computed(() => useThemeStore().primaryColor)

function toggleStar(email: Email) {
	store.toggleStar(email)
}

const menuOptions = [
	{
		label: "Delete",
		key: "Delete",
		icon: renderIcon(TrashIcon)
	},
	{
		label: "Add label",
		key: "Add label",
		icon: renderIcon(LabelOutIcon)
	},
	{
		label: "Move to folder",
		key: "Move to folder",
		icon: renderIcon(FolderIcon)
	},
	{
		label: "Print",
		key: "Print",
		icon: renderIcon(PrinterIcon)
	},
	{
		label: "Star",
		key: "Star",
		icon: renderIcon(StarIcon)
	}
]
</script>

<style lang="scss" scoped>
.email-toolbar {

	gap: 18px;

	.actions-btns {
		gap: 18px;
	}

	.menu-btns {
		display: none;
	}

	.reply-btns {
		margin-right: 15px;
	}

	@container (max-width:500px) {
		.actions-btns {
			display: none;
		}
		.menu-btns {
			display: flex;
		}
	}
}
</style>

```
src/components/apps/Mailbox/Navigator.vue
```vue
<template>
	<div class="navigator flex items-center">
		<div class="nav-btns flex items-center gap-2">
			<span class="opacity-70">1 - 30 of 635</span>
			<n-button text size="small">
				<Icon :size="24" :name="ChevronLeftIcon" />
			</n-button>
			<n-button text size="small">
				<Icon :size="24" :name="ChevronRightIcon" />
			</n-button>
		</div>
	</div>
</template>

<script setup lang="ts">
import Icon from "@/components/common/Icon.vue"
import { NButton } from "naive-ui"

const ChevronLeftIcon = "carbon:chevron-left"
const ChevronRightIcon = "carbon:chevron-right"
</script>

<style lang="scss" scoped>
.navigator {
	@container (max-width:600px) {
		.nav-btns {
			span {
				display: none;
			}
		}
	}
}
</style>

```
src/components/common/Icon.vue
```vue
<template>
	<component :is="componentName" v-bind="options">
		<template v-if="$slots.default">
			<slot />
		</template>
		<template v-else>
			<Icon v-if="icon" :icon :width="size" :height="size" />
		</template>
	</component>
</template>

<script setup lang="ts">
import { Icon, type IconifyIcon, loadIcon } from "@iconify/vue"
import { NIcon, NIconWrapper } from "naive-ui"
import { computed, ref, watchEffect } from "vue"

const props = defineProps<{
	name?: string
	size?: number
	bgSize?: number
	color?: string
	bgColor?: string
	borderRadius?: number
	depth?: 1 | 2 | 3 | 4 | 5
}>()

const useWrapper = computed(() => !!(props.bgColor || props.bgSize || props.borderRadius))
const componentName = computed(() => (useWrapper.value ? NIconWrapper : NIcon))

const options = computed(() => {
	const opt: Partial<{ size: number; color: string; borderRadius: number; iconColor: string; depth: number }> = {}
	if (useWrapper.value) {
		if (props.bgSize !== undefined) opt.size = props.bgSize
		if (props.bgColor !== undefined) opt.color = props.bgColor
		if (props.borderRadius !== undefined) opt.borderRadius = props.borderRadius
		if (props.color !== undefined) opt.iconColor = props.color
	} else {
		if (props.color !== undefined) opt.color = props.color
		if (props.depth !== undefined) opt.depth = props.depth
		if (props.size !== undefined) opt.size = props.size
	}
	return opt
})

const load = (name: string) => loadIcon(name).catch(() => console.error(`Failed to load icon ${name}`))

const icon = ref<void | Required<IconifyIcon>>()

function setIcon(name: string | undefined) {
	if (name) {
		load(name).then(res => (icon.value = res))
	}
}

setIcon(props.name)

watchEffect(() => setIcon(props.name))
</script>

```
src/components/common/SegmentedPage.vue
```vue
<template>
	<n-split
		ref="splitPane"
		direction="horizontal"
		:default-size="sanitizedDefaultSplit"
		:min="0"
		:max="1"
		:resize-trigger-size="0"
		:disabled="!enableResize || splitDisabled"
		class="wrapper flex grow"
		:class="[{ 'sidebar-open': sidebarOpen }, `sidebar-position-${sidebarPosition}`]"
		:pane1-style="pane1Style"
	>
		<template #[tplNameSide]>
			<div v-if="sidebarAvailable" ref="sidebar" class="sidebar flex flex-col">
				<div v-if="$slots['sidebar-header']" class="sidebar-header flex items-center">
					<slot name="sidebar-header" />
				</div>
				<div v-if="$slots['sidebar-content']" class="sidebar-main grow">
					<n-scrollbar class="max-h-full">
						<div class="sidebar-main-content" :style="sidebarContentStyle" :class="sidebarContentClass">
							<slot name="sidebar-content" />
						</div>
					</n-scrollbar>
				</div>
				<div v-if="$slots['sidebar-footer']" class="sidebar-footer flex items-center">
					<slot name="sidebar-footer" />
				</div>
			</div>
		</template>

		<template #resize-trigger>
			<div class="split-trigger">
				<div class="split-trigger-icon">
					<Icon :name="SplitIcon" :size="12" />
				</div>
			</div>
		</template>

		<template #[tplNameMain]>
			<div class="main flex-grow flex flex-col">
				<div v-if="$slots['main-toolbar']" class="main-toolbar flex items-center">
					<div v-if="sidebarAvailable && !hideMenuBtn" class="menu-btn flex justify-center opacity-50">
						<n-button text @click="sidebarOpen = true">
							<Icon :size="24" :name="MenuIcon" />
						</n-button>
					</div>

					<div class="grow">
						<slot name="main-toolbar" />
					</div>
				</div>
				<div class="main-view grow" :class="{ 'no-container-query': disableContainerQuery }">
					<n-scrollbar v-if="useMainScroll" ref="mainScrollbar" class="max-h-full">
						<div class="main-content" :style="mainContentStyle" :class="mainContentClass">
							<slot name="main-content" />
						</div>
					</n-scrollbar>
					<div v-else class="main-content" :style="mainContentStyle" :class="mainContentClass">
						<slot name="main-content" />
					</div>
				</div>
				<div v-if="$slots['main-footer']" class="main-footer flex items-center">
					<div class="wrap">
						<slot name="main-footer" />
					</div>
				</div>

				<div v-if="sidebarOpen" class="main-overlay" />
			</div>
		</template>
	</n-split>
</template>

<script setup lang="ts">
import Icon from "@/components/common/Icon.vue"
import { onClickOutside, useWindowSize } from "@vueuse/core"
import { NButton, NScrollbar, NSplit } from "naive-ui"
import { computed, onMounted, ref, toRefs, useSlots, watch } from "vue"

type SidebarPosition = "left" | "right"

export interface CtxSegmentedPage {
	mainScrollbar: typeof NScrollbar | null
	closeSidebar: () => void
	openSidebar: () => void
}

const props = withDefaults(
	defineProps<{
		sidebarPosition?: SidebarPosition
		hideMenuBtn?: boolean
		useMainScroll?: boolean
		mainContentStyle?: string
		mainContentClass?: string
		sidebarContentStyle?: string
		sidebarContentClass?: string
		enableResize?: boolean
		disableContainerQuery?: boolean
		defaultSplit?: number
		maxSidebarWidth?: number
		minSidebarWidth?: number
	}>(),
	{ sidebarPosition: "left", useMainScroll: true, defaultSplit: 0.3, maxSidebarWidth: 450, minSidebarWidth: 250 }
)

const emit = defineEmits<{
	(e: "mounted", value: CtxSegmentedPage): void
	(e: "sidebar", value: boolean): void
}>()

const {
	sidebarPosition,
	hideMenuBtn,
	useMainScroll,
	mainContentStyle,
	mainContentClass,
	sidebarContentStyle,
	sidebarContentClass,
	enableResize,
	disableContainerQuery,
	defaultSplit,
	maxSidebarWidth,
	minSidebarWidth
} = toRefs(props)

const MenuIcon = "ph:list-light"
const SplitIcon = "carbon:draggable"

const splitPane = ref()
const sanitizedDefaultSplit = ref(defaultSplit.value)
const splitDisabled = ref(false)

const slots = useSlots()
const sidebarOpen = ref(false)
const sidebar = ref(null)
const mainScrollbar = ref<typeof NScrollbar | null>(null)
const { width } = useWindowSize()
const sidebarAvailable = computed(
	() => !!slots["sidebar-header"] || !!slots["sidebar-content"] || !!slots["sidebar-footer"]
)
const isSidebarLeft = computed<boolean>(() => sidebarPosition.value === "left")
const tplNameMain = computed<1 | 2>(() => (isSidebarLeft.value ? 2 : 1))
const tplNameSide = computed<1 | 2>(() => (isSidebarLeft.value ? 1 : 2))
const pane1Style = computed(() => ({
	maxWidth: isSidebarLeft.value ? `${maxSidebarWidth.value}px` : `calc(100% - ${minSidebarWidth.value}px)`,
	minWidth: isSidebarLeft.value ? `${minSidebarWidth.value}px` : `calc(100% - ${maxSidebarWidth.value}px)`
}))

function closeSidebar() {
	sidebarOpen.value = false
}

function openSidebar() {
	sidebarOpen.value = true
}

onClickOutside(sidebar, () => closeSidebar())

watch(
	sidebarOpen,
	val => {
		emit("sidebar", val)
	},
	{ immediate: true }
)

watch(
	width,
	val => {
		sanitizedDefaultSplit.value = val <= 700 ? 0 : isSidebarLeft.value ? defaultSplit.value : 1 - defaultSplit.value
		splitDisabled.value = val <= 700
	},
	{ immediate: true }
)

onMounted(() => {
	emit("mounted", {
		mainScrollbar: mainScrollbar.value,
		closeSidebar,
		openSidebar
	})
})
</script>

<style lang="scss" scoped>
.wrapper {
	--mb-toolbar-height: 70px;
	position: relative;
	height: 100%;
	overflow: hidden;
	border-radius: var(--border-radius);
	border: 1px solid var(--border-color);
	background-color: var(--bg-color);
	direction: ltr;

	.split-trigger {
		height: 100%;
		width: 3px;
		display: flex;
		align-items: center;
		position: relative;
		z-index: 1;
		left: -2px;
		transition: background-color 0.3s var(--bezier-ease);

		.split-trigger-icon {
			background-color: var(--border-color);
			border-radius: var(--border-radius-small);
			height: 18px;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			margin-left: -5px;
			z-index: 1;
			transition: background-color 0.3s var(--bezier-ease);
		}

		&:hover {
			background-color: var(--primary-010-color);

			.split-trigger-icon {
				background-color: var(--primary-010-color);
			}
		}
	}

	.sidebar {
		background-color: var(--bg-secondary-color);
		height: 100%;
		overflow: hidden;
		border-right: 1px solid var(--border-color);

		.sidebar-header {
			border-block-end: var(--border-small-050);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 30px;
		}

		.sidebar-main {
			overflow: hidden;

			.sidebar-main-content {
				padding: 30px;
			}
		}

		.sidebar-footer {
			border-block-start: var(--border-small-050);
			min-height: var(--mb-toolbar-height);
			padding: 0 30px;
		}
	}

	.main {
		background-color: var(--bg-color);
		position: relative;
		height: 100%;

		.main-overlay {
			position: absolute;
			width: 100%;
			height: 100%;
			z-index: 2;
			top: 0;
			left: 0;
		}

		.main-toolbar {
			border-block-end: var(--border-small-050);
			min-height: var(--mb-toolbar-height);
			height: var(--mb-toolbar-height);
			padding: 0 30px;
			gap: 18px;
			line-height: 1.3;
			container-type: inline-size;

			.menu-btn {
				display: none;
			}
		}

		.main-view {
			overflow: hidden;
			&:not(.no-container-query) {
				container-type: inline-size;
			}

			.main-content {
				padding: 30px;
			}
		}

		.main-footer {
			container-type: inline-size;
			border-block-start: var(--border-small-050);
			padding: 0 30px;

			.wrap {
				min-height: calc(var(--mb-toolbar-height) - 1px);
				width: 100%;
				display: flex;
				align-items: center;
			}
		}
	}

	@media (max-width: 700px) {
		--mb-toolbar-height: 62px;
		height: 100%;
		overflow: hidden;
		border-radius: 0;
		border: none;

		&::before {
			content: "";
			width: 100vw;
			display: block;
			background-color: var(--bg-body);
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			opacity: 0;
			transition:
				opacity 0.25s ease-in-out,
				transform 0s linear 0.3s;
			z-index: 1;
		}

		.sidebar {
			position: absolute;
			top: 0;
			left: 0;
			bottom: 0;
			transform: translateX(-100%);
			transition: transform 0.25s ease-in-out;
			z-index: 3;
			min-width: 300px;
			max-width: min(450px, 80vw);

			&::before {
				content: "";
				width: 100%;
				height: 100%;
				display: block;
				background-color: var(--bg-color);
				z-index: -1;
				position: absolute;
			}

			.sidebar-header,
			.sidebar-footer {
				padding: 0 20px;
			}

			.sidebar-main {
				.sidebar-main-content {
					padding: 20px;
				}
			}
		}
		.main {
			.main-toolbar {
				padding: 0 20px;
				gap: 14px;

				.menu-btn {
					display: flex;
				}
			}

			.main-view {
				.main-content {
					padding: 20px;
				}
			}
			.main-footer {
				padding: 0 20px;
			}
		}

		&.sidebar-position-left {
			:deep() {
				.n-split-pane-1 {
					min-width: 0 !important;
					max-width: 0 !important;
				}
			}
		}

		&.sidebar-position-right {
			:deep() {
				.n-split-pane-1 {
					min-width: 100% !important;
					max-width: 100% !important;
				}
			}

			&::before,
			.sidebar {
				left: initial;
				right: 0;
				transform: translateX(100%);
			}

			.main {
				.main-toolbar {
					flex-direction: row-reverse;
					justify-content: space-between;
				}
			}
		}

		&.sidebar-open {
			&::before {
				transform: translateX(0);
				opacity: 0.4;
				transition:
					opacity 0.25s ease-in-out,
					transform 0s linear 0s;
			}

			.sidebar {
				transform: translateX(0);
				box-shadow: 0px 0px 80px 0px rgba(0, 0, 0, 0.1);
			}
		}
	}
}

.direction-rtl {
	.wrapper {
		.sidebar,
		.main {
			direction: rtl;
		}
	}
}
</style>

```
src/composables/useHideLayoutFooter.ts
```ts
import { useThemeStore } from "@/stores/theme"
import { onBeforeMount, onBeforeUnmount } from "vue"

// :has() CSS relational pseudo-class not yet supported by Firefox
// (https://caniuse.com/css-has)
// at the moment this worker around permit to hide Layout Footer

export function useHideLayoutFooter() {
	const themeStore = useThemeStore()

	if (themeStore.isFooterShown) {
		onBeforeMount(() => {
			themeStore.setFooterShow(false)
		})
		onBeforeUnmount(() => {
			themeStore.setFooterShow(true)
		})
	}
}

```
src/design-tokens.json
```json
{
	"borderRadius": {
		"base": "6px",
		"small": "3px"
	},
	"lineHeight": {
		"base": "1.35"
	},
	"fontSize": {
		"base": "13px",
		"cardTitle": "18px"
	},
	"fontFamily": {
		"base": "'Public Sans', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
		"display": "'Lexend', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'",
		"mono": "'JetBrains Mono', SFMono-Regular, Menlo, Consolas, Courier, monospace"
	},
	"typography": {
		"h1": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "30px",
			"fontWeight": "700",
			"lineHeight": "41"
		},
		"h2": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "26px",
			"fontWeight": "700",
			"lineHeight": "35"
		},
		"h3": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "22px",
			"fontWeight": "700",
			"lineHeight": "30"
		},
		"h4": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "18px",
			"fontWeight": "500",
			"lineHeight": "24"
		},
		"h5": {
			"fontFamily": "{fontFamily.display}",
			"fontSize": "14px",
			"fontWeight": "700",
			"lineHeight": "19"
		},
		"h6": {
			"fontFamily": "{fontFamily.base}",
			"fontSize": "12px",
			"fontWeight": "500",
			"lineHeight": "16"
		},
		"p": {
			"fontFamily": "{fontFamily.base}",
			"fontSize": "{fontSize.base}",
			"lineHeight": "20"
		}
	},
	"colors": {
		"light": {
			"sidebarBackground": "#ffffff",
			"bodyBackground": "#f5f7f9",
			"text": "#000000",
			"textSecondary": "#495465",
			"background": "#ffffff",
			"backgroundSecondary": "#fafbfc",
			"primary": "rgb(0, 178, 123)",
			"primary005": "rgba(0, 178, 123, 0.05)",
			"primary010": "rgba(0, 178, 123, 0.1)",
			"primary015": "rgba(0, 178, 123, 0.15)",
			"primary020": "rgba(0, 178, 123, 0.2)",
			"primary030": "rgba(0, 178, 123, 0.3)",
			"primary040": "rgba(0, 178, 123, 0.4)",
			"primary050": "rgba(0, 178, 123, 0.5)",
			"primary060": "rgba(0, 178, 123, 0.6)",
			"info": "#6267FF",
			"success": "#00B27B",
			"warning": "#FFB600",
			"error": "#FF0156",
			"info005": "rgba(98, 103, 255, 0.05)",
			"success005": "rgba(0, 178, 123, 0.05)",
			"warning005": "rgba(227, 194, 47, 0.05)",
			"error005": "rgba(255, 1, 86, 0.05)",
			"secondary1": "rgb(98, 103, 255)",
			"secondary1Opacity005": "rgba(98, 103, 255, 0.05)",
			"secondary1Opacity010": "rgba(98, 103, 255, 0.1)",
			"secondary1Opacity020": "rgba(98, 103, 255, 0.2)",
			"secondary1Opacity030": "rgba(98, 103, 255, 0.3)",
			"secondary2": "rgb(255, 97, 201)",
			"secondary2Opacity005": "rgba(255, 97, 201, 0.05)",
			"secondary2Opacity010": "rgba(255, 97, 201, 0.1)",
			"secondary2Opacity020": "rgba(255, 97, 201, 0.2)",
			"secondary2Opacity030": "rgba(255, 97, 201, 0.3)",
			"secondary3": "rgb(255, 182, 0)",
			"secondary3Opacity005": "rgba(255, 182, 0, 0.05)",
			"secondary3Opacity010": "rgba(255, 182, 0, 0.1)",
			"secondary3Opacity020": "rgba(255, 182, 0, 0.2)",
			"secondary3Opacity030": "rgba(255, 182, 0, 0.3)",
			"secondary4": "rgb(255, 1, 86)",
			"secondary4Opacity005": "rgba(255, 1, 86, 0.05)",
			"secondary4Opacity010": "rgba(255, 1, 86, 0.1)",
			"secondary4Opacity020": "rgba(255, 1, 86, 0.2)",
			"secondary4Opacity030": "rgba(255, 1, 86, 0.3)",
			"divider005": "rgba(0, 0, 0, 0.05)",
			"divider010": "rgba(0, 0, 0, 0.1)",
			"divider020": "rgba(0, 0, 0, 0.2)",
			"divider030": "rgba(0, 0, 0, 0.3)",
			"hover005": "rgba(0, 0, 0, 0.05)",
			"hover010": "rgba(0, 0, 0, 0.1)",
			"hover050": "rgba(0, 0, 0, 0.5)"
		},
		"dark": {
			"sidebarBackground": "#1D1F25",
			"bodyBackground": "#14161A",
			"text": "#ffffff",
			"textSecondary": "#ACB5BE",
			"background": "#26282d",
			"backgroundSecondary": "#1D1F25",
			"primary": "rgb(0, 225, 155)",
			"primary005": "rgba(0, 225, 155, 0.05)",
			"primary010": "rgba(0, 225, 155, 0.1)",
			"primary015": "rgba(0, 225, 155, 0.15)",
			"primary020": "rgba(0, 225, 155, 0.2)",
			"primary030": "rgba(0, 225, 155, 0.3)",
			"primary040": "rgba(0, 225, 155, 0.4)",
			"primary050": "rgba(0, 225, 155, 0.5)",
			"primary060": "rgba(0, 225, 155, 0.6)",
			"info": "#6267FF",
			"success": "#00E19B",
			"warning": "#FFB600",
			"error": "#FF0156",
			"info005": "rgba(98, 103, 255, 0.05)",
			"success005": "rgba(0, 178, 123, 0.05)",
			"warning005": "rgba(227, 194, 47, 0.05)",
			"error005": "rgba(255, 1, 86, 0.05)",
			"secondary1": "rgb(98, 103, 255)",
			"secondary1Opacity005": "rgba(98, 103, 255, 0.05)",
			"secondary1Opacity010": "rgba(98, 103, 255, 0.1)",
			"secondary1Opacity020": "rgba(98, 103, 255, 0.2)",
			"secondary1Opacity030": "rgba(98, 103, 255, 0.3)",
			"secondary2": "rgb(255, 97, 201)",
			"secondary2Opacity005": "rgba(255, 97, 201, 0.05)",
			"secondary2Opacity010": "rgba(255, 97, 201, 0.1)",
			"secondary2Opacity020": "rgba(255, 97, 201, 0.2)",
			"secondary2Opacity030": "rgba(255, 97, 201, 0.3)",
			"secondary3": "rgb(255, 182, 0)",
			"secondary3Opacity005": "rgba(255, 182, 0, 0.05)",
			"secondary3Opacity010": "rgba(255, 182, 0, 0.1)",
			"secondary3Opacity020": "rgba(255, 182, 0, 0.2)",
			"secondary3Opacity030": "rgba(255, 182, 0, 0.3)",
			"secondary4": "rgb(255, 1, 86)",
			"secondary4Opacity005": "rgba(255, 1, 86, 0.05)",
			"secondary4Opacity010": "rgba(255, 1, 86, 0.1)",
			"secondary4Opacity020": "rgba(255, 1, 86, 0.2)",
			"secondary4Opacity030": "rgba(255, 1, 86, 0.3)",
			"divider005": "rgba(255, 255, 255, 0.05)",
			"divider010": "rgba(255, 255, 255, 0.1)",
			"divider020": "rgba(255, 255, 255, 0.2)",
			"divider030": "rgba(255, 255, 255, 0.3)",
			"hover005": "rgba(255, 255, 255, 0.05)",
			"hover010": "rgba(255, 255, 255, 0.1)",
			"hover050": "rgba(255, 255, 255, 0.5)"
		}
	}
}

```
src/mock/mailbox.ts
```ts
import dayjs from "@/utils/dayjs"
import { faker } from "@faker-js/faker"

export const labels = [
	{
		id: "personal",
		title: "Personal"
	},
	{
		id: "office",
		title: "Office"
	},
	{
		id: "important",
		title: "Important"
	},
	{
		id: "shop",
		title: "Shop"
	}
]

export const folders = [
	{
		id: "inbox",
		title: "Inbox"
	},
	{
		id: "sent",
		title: "Sent"
	},
	{
		id: "draft",
		title: "Draft"
	},
	{
		id: "starred",
		title: "Starred"
	},
	{
		id: "spam",
		title: "Spam"
	},
	{
		id: "trash",
		title: "Trash"
	}
]

export interface Email {
	id: string
	date: Date
	dateText?: string
	subject: string
	body: string
	seen: boolean
	starred: boolean
	folder: string
	labels: {
		id: string
		title: string
	}[]
	name: string
	email: string
	avatar: string
	attachments: { name: string; size: string }[]
	selected: boolean
}

export function getEmails(): Email[] {
	const emails = []

	for (let i = 0; i < 150; i++) {
		const folder = faker.helpers.arrayElements(folders, 1)[0].id
		const body = []
		const bodyParagraphs = faker.number.int({ min: 2, max: 4 })
		for (let i = 0; i < bodyParagraphs; i++) {
			body.push(faker.lorem.sentence(faker.number.int({ min: 50, max: 100 })))
		}

		emails.push({
			id: faker.string.nanoid(),
			date: faker.date.between({ from: dayjs().subtract(2, "w").toDate(), to: dayjs().toDate() }),
			subject: faker.lorem.sentence(faker.number.int({ min: 3, max: 7 })).slice(0, -1),
			body: body.join("<br/><br/>"),
			seen: faker.datatype.boolean(),
			starred: faker.datatype.boolean() || folder === "starred",
			folder,
			labels: faker.helpers.arrayElements(labels, { min: 0, max: 2 }),
			name: faker.person.fullName(),
			email: faker.internet.email(),
			avatar: faker.image.avatarGitHub(),
			attachments: faker.helpers.uniqueArray(faker.word.sample, faker.number.int({ min: 0, max: 2 })).map(() => ({
				name: faker.system.commonFileName(),
				size: `${faker.number.int({ min: 50, max: 999 })}KB`
			})),
			selected: false
		})
	}
	return emails
}

```
src/stores/apps/useMailboxStore.ts
```ts
import { type Email, folders, getEmails, labels } from "@/mock/mailbox"
import { defineStore } from "pinia"

export const useMailboxStore = defineStore("mailbox", {
	state: () => ({
		emails: getEmails(),
		folders,
		labels,
		activeFolder: "inbox",
		activeLabel: ""
	}),
	actions: {
		setActiveFolder(folder: string) {
			this.activeFolder = folder
		},
		setActiveLabel(label: string) {
			if (this.activeLabel === label) {
				this.activeLabel = ""
			} else {
				this.activeLabel = label
			}
		},
		toggleCheck(email: Email) {
			const eml = this.emails.find(e => e.id === email.id)
			if (eml) {
				eml.selected = !eml.selected
			}
		},
		toggleStar(email: Email) {
			const eml = this.emails.find(e => e.id === email.id)
			if (eml) {
				eml.starred = !eml.starred
			}
		}
	}
})

```
src/stores/theme.ts
```ts
import type { ColorType, Layout, RouterTransition, ThemeName } from "@/types/theme.d"
import type { BuiltInGlobalTheme } from "naive-ui/es/themes/interface"
import { getCssVars, getDefaultState } from "@/theme"
import { ThemeEnum } from "@/types/theme.d"
import { exportPrimaryShades, getThemeColors, getTypeValue, type PrimaryShade } from "@/utils/theme"
import _get from "lodash/get"
import _pick from "lodash/pick"
import _set from "lodash/set"
import { darkTheme, type GlobalThemeOverrides, lightTheme, type ThemeCommonVars } from "naive-ui"
import { acceptHMRUpdate, defineStore } from "pinia"

export const useThemeStore = defineStore("theme", {
	state: () => getDefaultState(),
	actions: {
		setLayout(layout: Layout): void {
			this.layout = layout
		},
		setRTL(rtl: boolean): void {
			this.rtl = rtl
		},
		setBoxed(boxed: boolean): void {
			this.boxed.enabled = boxed
		},
		setFooterShow(show: boolean): void {
			this.footer.show = show
		},
		setToolbarBoxed(boxed: boolean): void {
			this.boxed.toolbar = boxed
		},
		setRouterTransition(routerTransition: RouterTransition): void {
			this.routerTransition = routerTransition
		},
		setTheme(themeName: ThemeName): void {
			this.themeName = themeName
		},
		setThemeLight(): void {
			this.themeName = ThemeEnum.Light
		},
		setThemeDark(): void {
			this.themeName = ThemeEnum.Dark
		},
		setColor(theme: ThemeName, colorType: ColorType, color: string): void {
			this.colors[theme][colorType] = color

			if (colorType === "primary") {
				const primaryShades = exportPrimaryShades(color)

				for (const k in primaryShades) {
					const name = k as PrimaryShade
					const shade = primaryShades[name]

					const colorKey = `${colorType}${name}` as keyof (typeof this.colors)[typeof theme]
					this.colors[theme][colorKey] = shade
				}
			}
		},
		toggleTheme(): void {
			if (this.isThemeDark) {
				this.setThemeLight()
			} else {
				this.setThemeDark()
			}
		},
		toggleSidebar(): void {
			this.sidebar.collapsed = !this.sidebar.collapsed
		},
		refreshSidebar(): void {
			// this is useful in context like NUXT
			this.sidebar.collapsed = !this.sidebar.collapsed
			setTimeout(() => {
				this.sidebar.collapsed = !this.sidebar.collapsed
			}, 10)
		},
		openSidebar(): void {
			this.sidebar.collapsed = false
		},
		closeSidebar(): void {
			this.sidebar.collapsed = true
		},
		updateVars() {
			for (const key in this.responsive.override) {
				if (_get(this, key) && key in this.responsive.override) {
					_set(
						this,
						key,
						window.innerWidth <= this.responsive.breakpoint
							? this.responsive.override[key as keyof typeof this.responsive.override].mobile
							: this.responsive.override[key as keyof typeof this.responsive.override].desk
					)
				}
			}

			// auto close sidebar on resize
			if (this.sidebar.autoClose) {
				if (!this.sidebar.collapsed && window.innerWidth <= this.sidebar.autoCloseBreakpoint) {
					this.sidebar.collapsed = true
				}
			}
		}
	},
	getters: {
		naiveTheme(state): BuiltInGlobalTheme {
			return state.themeName === ThemeEnum.Dark ? darkTheme : lightTheme
		},
		themeOverrides(state): GlobalThemeOverrides {
			const {
				primary,
				success,
				warning,
				error,
				info,
				background,
				bodyBackground,
				text,
				textSecondary,
				divider005,
				hover010
			} = state.colors[state.themeName]

			const themeColors = getThemeColors({ primary, success, warning, error, info })

			const lineHeight = state.lineHeight.base
			const borderRadius = state.borderRadius.base
			const borderRadiusSmall = state.borderRadius.small

			return {
				common: {
					...themeColors,
					textColorBase: text,
					textColor1: text,
					textColor2: text,
					textColor3: textSecondary,
					bodyColor: bodyBackground,
					baseColor: background,
					popoverColor: background,
					cardColor: background,
					modalColor: background,
					lineHeight,
					borderRadius,
					borderRadiusSmall,
					fontSize: state.fontSize.base,
					fontFamily: state.fontFamily.base,
					fontFamilyMono: state.fontFamily.mono,
					dividerColor: divider005,
					hoverColor: hover010
				},
				Card: {
					color: background,
					titleFontSizeSmall: state.fontSize.cardTitle,
					titleFontSizeMedium: state.fontSize.cardTitle,
					titleFontSizeLarge: state.fontSize.cardTitle,
					titleFontSizeHuge: state.fontSize.cardTitle
				},
				LoadingBar: {
					colorLoading: primary
				},
				Typography: {
					headerFontSize1: getTypeValue(state, state.typography.h1.fontSize),
					headerFontSize2: getTypeValue(state, state.typography.h2.fontSize),
					headerFontSize3: getTypeValue(state, state.typography.h3.fontSize),
					headerFontSize4: getTypeValue(state, state.typography.h4.fontSize),
					headerFontSize5: getTypeValue(state, state.typography.h5.fontSize),
					headerFontSize6: getTypeValue(state, state.typography.h6.fontSize)
				}
			}
		},
		darkPrimaryColor(state): string {
			return state.colors.dark.primary
		},
		lightPrimaryColor(state): string {
			return state.colors.light.primary
		},
		primaryColor(state): string {
			return state.colors[state.themeName].primary
		},
		sidebarBackground(state): string {
			return state.colors[state.themeName].sidebarBackground
		},
		bodyBackground(state): string {
			return state.colors[state.themeName].bodyBackground
		},
		backgroundSecondaryColor(state): string {
			return state.colors[state.themeName].backgroundSecondary
		},
		secondaryColors(state): { [key: string]: string } {
			const pick = ["secondary1", "secondary2", "secondary3", "secondary4"]
			return _pick(state.colors[state.themeName], pick)
		},
		secondaryOpacityColors(state): { [key: string]: string } {
			const pick = [
				"secondary1Opacity005",
				"secondary1Opacity010",
				"secondary1Opacity020",
				"secondary1Opacity030",
				"secondary2Opacity005",
				"secondary2Opacity010",
				"secondary2Opacity020",
				"secondary2Opacity030",
				"secondary3Opacity005",
				"secondary3Opacity010",
				"secondary3Opacity020",
				"secondary3Opacity030",
				"secondary4Opacity005",
				"secondary4Opacity010",
				"secondary4Opacity020",
				"secondary4Opacity030"
			]
			return _pick(state.colors[state.themeName], pick)
		},
		dividerColors(state): { [key: string]: string } {
			const pick = ["divider005", "divider010", "divider020", "divider030"]
			return _pick(state.colors[state.themeName], pick)
		},
		hoverColors(state): { [key: string]: string } {
			const pick = ["hover005", "hover010", "hover050"]
			return _pick(state.colors[state.themeName], pick)
		},
		primaryColors(state): { [key: string]: string } {
			const pick = [
				"primary005",
				"primary010",
				"primary015",
				"primary020",
				"primary030",
				"primary040",
				"primary050",
				"primary060"
			]
			return _pick(state.colors[state.themeName], pick)
		},
		naiveCommon(): ThemeCommonVars {
			return { ...this.naiveTheme.common, ...this.themeOverrides.common }
		},
		style(state): { [key: string]: string } {
			return getCssVars(state, this)
		},
		isThemeDark(state): boolean {
			return state.themeName === ThemeEnum.Dark
		},
		isThemeLight(state): boolean {
			return state.themeName === ThemeEnum.Light
		},
		isBoxed(state): boolean {
			return state.boxed.enabled
		},
		isRTL(state): boolean {
			return state.rtl
		},
		isFooterShown(state): boolean {
			return state.footer.show
		},
		isToolbarBoxed(state): boolean {
			return state.boxed.toolbar && state.boxed.enabled
		}
	},
	persist: {
		// use this param to save specific state chunk on localStorage
		pick: ["layout", "themeName", "routerTransition", "boxed", "sidebar.collapsed"]
	}
})

if (import.meta.hot) {
	import.meta.hot.accept(acceptHMRUpdate(useThemeStore, import.meta.hot))
}

```
src/theme/index.ts
```ts
import tokens from "@/design-tokens.json"
import { Layout, RouterTransition, ThemeEnum } from "@/types/theme.d"
import { hex2rgb } from "@/utils/theme"
import { type ThemeCommonVars, useOsTheme } from "naive-ui"

type ThemeState = ReturnType<typeof getDefaultState>

interface ThemeGetters {
	dividerColors: { [key: string]: string }
	hoverColors: { [key: string]: string }
	primaryColors: { [key: string]: string }
	secondaryOpacityColors: { [key: string]: string }
	secondaryColors: { [key: string]: string }
	sidebarBackground: string
	bodyBackground: string
	backgroundSecondaryColor: string
	naiveCommon: ThemeCommonVars
}

const osTheme = useOsTheme()

export function getDefaultState() {
	return {
		layout: Layout.VerticalNav, // Type of layout, with vertical or horizontal navigation
		themeName: osTheme.value || ThemeEnum.Light, // Theme name (Dark, Light), with fallback to the light theme
		routerTransition: RouterTransition.FadeUp, // Type of transition for the router
		routerTransitionDuration: 0.3, // Duration of the router transition in seconds
		rtl: false, // RTL (right to left) mode toggle
		boxed: {
			enabled: true, // Choose whether to apply a maximum width to the page
			toolbar: true, // Choose whether to apply the maximum width to the toolbar as well
			width: 1600 // Maximum width size in pixels
		},
		sidebar: {
			autoClose: true, // Choose whether to automatically close the sidebar when the view goes below the "autoCloseBreakpoint" value
			collapsed: false, // Indicates if the sidebar is collapsed
			autoCloseBreakpoint: 1000, // Breakpoint for the automatic closing of the sidebar (in pixels)
			animEase: "ease-in-out", // Type of easing for animations
			animDuration: 0.3, // Duration of sidebar animations (in seconds)
			openWidth: 300, // Width of the open sidebar (in pixels)
			closeWidth: 64 // Width of the closed sidebar (in pixels)
		},
		footer: {
			show: true // Show or hide the footer
		},
		responsive: {
			breakpoint: 700, // Breakpoint in pixels (Desk -> Mobile)
			// Parameters to be adjusted based on the breakpoint
			override: {
				viewPadding: {
					desk: 40, // View padding for desktop
					mobile: 20 // View padding for mobile devices
				},
				toolbarHeight: {
					desk: 80, // Height of the toolbar for desktop
					mobile: 70 // Height of the toolbar for mobile devices
				}
			}
		},
		toolbarHeight: 80, // Default toolbar height (in pixels)
		viewPadding: 40, // Default view padding (in pixels)
		headerBarHeight: 60, // Height of the header bar (in pixels)
		colors: tokens.colors, // Color definitions from the token
		borderRadius: tokens.borderRadius, // Border radius from the token
		lineHeight: tokens.lineHeight, // Line height from the token
		fontSize: tokens.fontSize, // Font size from the token
		fontFamily: tokens.fontFamily, // Font family from the token
		typography: tokens.typography // Typography configurations from the token
	}
}

export function getCssVars(state: ThemeState, getters: ThemeGetters): { [key: string]: string } {
	const naive = getters.naiveCommon

	const bgColor = naive.baseColor
	const bgColorRGB = hex2rgb(bgColor).join(", ")
	const bgSecondaryColor = getters.backgroundSecondaryColor
	const fgColor = naive.textColorBase
	const fgSecondaryColor = naive.textColor3

	const tabFgColorActive = naive.textColor2
	const borderColor = naive.dividerColor
	const primaryColor = naive.primaryColor

	const successColor = naive.successColor
	const errorColor = naive.errorColor
	const warningColor = naive.warningColor
	const infoColor = naive.infoColor

	const { success005, warning005, error005, info005 } = state.colors[state.themeName]

	const modalColor = naive.modalColor
	const modalColorRGB = hex2rgb(modalColor).join(", ")
	const codeColor = naive.codeColor
	const tabColor = naive.tabColor
	const tabColorActive = naive.inputColor
	const bezierEase = naive.cubicBezierEaseInOut

	const buttonColorSecondary = naive.buttonColor2
	const buttonColorSecondaryHover = naive.buttonColor2Hover
	const buttonColorSecondaryPressed = naive.buttonColor2Pressed

	const bgSidebar = getters.sidebarBackground
	const bgSidebarRGB = hex2rgb(bgSidebar).join(", ")
	const bgBody = getters.bodyBackground
	const bgBodyRGB = hex2rgb(bgBody).join(", ")

	const boxedWidth = state.boxed.width
	const routerTransitionDuration = state.routerTransitionDuration
	const sidebarAnimEase = state.sidebar.animEase
	const sidebarAnimDuration = state.sidebar.animDuration
	const sidebarOpenWidth = state.sidebar.openWidth
	const sidebarCloseWidth = state.sidebar.closeWidth
	const toolbarHeight = state.toolbarHeight
	const viewPadding = state.viewPadding
	const headerBarHeight = state.headerBarHeight
	const fontFamily = state.fontFamily.base
	const fontFamilyDisplay = state.fontFamily.display
	const fontFamilyMono = state.fontFamily.mono

	const borderRadius = state.borderRadius.base
	const borderRadiusSmall = state.borderRadius.small

	const { divider005, divider010, divider020, divider030 } = getters.dividerColors
	const { hover005, hover010, hover050 } = getters.hoverColors
	const { primary005, primary010, primary015, primary020, primary030, primary040, primary050, primary060 } =
		getters.primaryColors
	const {
		secondary1Opacity005,
		secondary1Opacity010,
		secondary1Opacity020,
		secondary1Opacity030,
		secondary2Opacity005,
		secondary2Opacity010,
		secondary2Opacity020,
		secondary2Opacity030,
		secondary3Opacity005,
		secondary3Opacity010,
		secondary3Opacity020,
		secondary3Opacity030,
		secondary4Opacity005,
		secondary4Opacity010,
		secondary4Opacity020,
		secondary4Opacity030
	} = getters.secondaryOpacityColors

	const { secondary1, secondary2, secondary3, secondary4 } = getters.secondaryColors
	const secondary1RGB = hex2rgb(secondary1).join(", ")
	const secondary2RGB = hex2rgb(secondary2).join(", ")
	const secondary3RGB = hex2rgb(secondary3).join(", ")
	const secondary4RGB = hex2rgb(secondary4).join(", ")

	// This style object, imported via the themeStore, will be available application-wide and is exposed in the HTML tag as a list of CSS variables, which you can use in your CSS/SCSS code like: var(-–bg-color)
	return {
		"bg-body": `${bgBody}`,
		"bg-body-rgb": `${bgBodyRGB}`,
		"bg-sidebar": `${bgSidebar}`,
		"bg-sidebar-rgb": `${bgSidebarRGB}`,

		"fg-color": `${fgColor}`,
		"fg-secondary-color": `${fgSecondaryColor}`,
		"bg-color": `${bgColor}`,
		"bg-color-rgb": `${bgColorRGB}`,
		"bg-secondary-color": `${bgSecondaryColor}`,

		"border-color": `${borderColor}`,
		"bezier-ease": `${bezierEase}`,
		"router-transition-duration": `${routerTransitionDuration}s`,
		"sidebar-anim-ease": `${sidebarAnimEase}`,
		"sidebar-anim-duration": `${sidebarAnimDuration}s`,
		"sidebar-open-width": `${sidebarOpenWidth}px`,
		"sidebar-close-width": `${sidebarCloseWidth}px`,
		"boxed-width": `${boxedWidth}px`,
		"toolbar-height": `${toolbarHeight}px`,
		"header-bar-height": `${headerBarHeight}px`,
		"view-padding": `${viewPadding}px`,
		"border-radius": `${borderRadius}`,
		"border-radius-small": `${borderRadiusSmall}`,
		"font-family": `${fontFamily}`,
		"font-family-display": `${fontFamilyDisplay}`,
		"font-family-mono": `${fontFamilyMono}`,
		"code-color": `${codeColor}`,
		"primary-color": `${primaryColor}`,
		"tab-color": `${tabColor}`,
		"tab-color-active": `${tabColorActive}`,
		"tab-fg-color-active": `${tabFgColorActive}`,
		"modal-color": `${modalColor}`,
		"modal-color-rgb": `${modalColorRGB}`,

		"button-color-secondary": `${buttonColorSecondary}`,
		"button-color-secondary-hover": `${buttonColorSecondaryHover}`,
		"button-color-secondary-pressed": `${buttonColorSecondaryPressed}`,

		"primary-005-color": `${primary005}`,
		"primary-010-color": `${primary010}`,
		"primary-015-color": `${primary015}`,
		"primary-020-color": `${primary020}`,
		"primary-030-color": `${primary030}`,
		"primary-040-color": `${primary040}`,
		"primary-050-color": `${primary050}`,
		"primary-060-color": `${primary060}`,

		"hover-005-color": `${hover005}`,
		"hover-010-color": `${hover010}`,
		"hover-050-color": `${hover050}`,

		"divider-005-color": `${divider005}`,
		"divider-010-color": `${divider010}`,
		"divider-020-color": `${divider020}`,
		"divider-030-color": `${divider030}`,

		"success-color": `${successColor}`,
		"error-color": `${errorColor}`,
		"warning-color": `${warningColor}`,
		"info-color": `${infoColor}`,
		"success-005-color": `${success005}`,
		"error-005-color": `${error005}`,
		"warning-005-color": `${warning005}`,
		"info-005-color": `${info005}`,

		"secondary1-color": `${secondary1}`,
		"secondary1-color-rgb": `${secondary1RGB}`,
		"secondary2-color": `${secondary2}`,
		"secondary2-color-rgb": `${secondary2RGB}`,
		"secondary3-color": `${secondary3}`,
		"secondary3-color-rgb": `${secondary3RGB}`,
		"secondary4-color": `${secondary4}`,
		"secondary4-color-rgb": `${secondary4RGB}`,

		"secondary1-opacity-005-color": `${secondary1Opacity005}`,
		"secondary1-opacity-010-color": `${secondary1Opacity010}`,
		"secondary1-opacity-020-color": `${secondary1Opacity020}`,
		"secondary1-opacity-030-color": `${secondary1Opacity030}`,
		"secondary2-opacity-005-color": `${secondary2Opacity005}`,
		"secondary2-opacity-010-color": `${secondary2Opacity010}`,
		"secondary2-opacity-020-color": `${secondary2Opacity020}`,
		"secondary2-opacity-030-color": `${secondary2Opacity030}`,
		"secondary3-opacity-005-color": `${secondary3Opacity005}`,
		"secondary3-opacity-010-color": `${secondary3Opacity010}`,
		"secondary3-opacity-020-color": `${secondary3Opacity020}`,
		"secondary3-opacity-030-color": `${secondary3Opacity030}`,
		"secondary4-opacity-005-color": `${secondary4Opacity005}`,
		"secondary4-opacity-010-color": `${secondary4Opacity010}`,
		"secondary4-opacity-020-color": `${secondary4Opacity020}`,
		"secondary4-opacity-030-color": `${secondary4Opacity030}`
	}
}

```
src/types/theme.d.ts
```ts
export enum Layout {
	VerticalNav = "VerticalNav",
	HorizontalNav = "HorizontalNav",
	Blank = "Blank"
}

export enum RouterTransition {
	Fade = "fade",
	FadeUp = "fade-up",
	FadeBottom = "fade-bottom",
	FadeLeft = "fade-left",
	FadeRight = "fade-right"
}

export enum ThemeEnum {
	Dark = "dark",
	Light = "light"
}

export type Theme = "light" | "dark"

export type ThemeName = ThemeEnum | Theme

export type ColorType = "primary" | "info" | "success" | "warning" | "error"
export type ColorScene = "" | "Suppl" | "Hover" | "Pressed"
export type ColorKey = `${ColorType}Color${ColorScene}`
export type ThemeColor = Partial<Record<ColorKey, string>>

export interface ColorAction {
	scene: ColorScene
	handler: (color: string) => string
}

```
src/utils/dayjs.ts
```ts
import dayjs from "dayjs"
import locale_en from "dayjs/locale/en.js"
import customParseFormat from "dayjs/plugin/customParseFormat"
import timezone from "dayjs/plugin/timezone"
/*
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import utc from "dayjs/plugin/utc"
import duration from "dayjs/plugin/duration"
import relativeTime from "dayjs/plugin/relativeTime"
dayjs.extend(isSameOrAfter)
dayjs.extend(utc)
dayjs.extend(relativeTime)
dayjs.extend(duration)
*/
dayjs.extend(customParseFormat)
dayjs.extend(timezone)
dayjs.locale(locale_en)
dayjs.tz.setDefault(dayjs.tz.guess())

export default dayjs

```
src/utils/index.ts
```ts
import process from "node:process"
import Icon from "@/components/common/Icon.vue"
import { isMobile as detectMobile } from "detect-touch-device"
import { type Component, h } from "vue"

export type OS = "Unknown" | "Windows" | "MacOS" | "UNIX" | "Linux"

// Transform File Instance in base64 string
export function file2Base64(blob: Blob): Promise<string> {
	return new Promise((resolve, reject) => {
		const reader = new FileReader()
		reader.readAsDataURL(blob)
		reader.onload = () => resolve(reader.result as string)
		reader.onerror = error => reject(error)
	})
}

export function isEnvDev() {
	return process.env.NODE_ENV === "development"
}
export function isEnvTest() {
	return process.env.NODE_ENV === "test"
}
export function isEnvProd() {
	return process.env.NODE_ENV === "production"
}

export function isMobile() {
	return detectMobile
}

export function renderIcon(icon: Component | string) {
	if (typeof icon === "string") {
		return () => h(Icon, { name: icon })
	} else {
		return () => h(Icon, null, { default: () => h(icon) })
	}
}

export function getOS(): OS {
	let os: OS = "Unknown"
	if (navigator.userAgent.includes("Win")) os = "Windows"
	if (navigator.userAgent.includes("Mac")) os = "MacOS"
	if (navigator.userAgent.includes("X11")) os = "UNIX"
	if (navigator.userAgent.includes("Linux")) os = "Linux"

	return os
}

export function delay(t: number) {
	return new Promise(res => setTimeout(res, t))
}

```
src/utils/theme.ts
```ts
import type { ColorAction, ColorKey, ColorType, ThemeColor } from "@/types/theme.d"
import { colord } from "colord"
import _get from "lodash/get"

export type PrimaryShade = "005" | "010" | "015" | "020" | "030" | "040" | "050" | "060"

export function toggleSidebarClass(
	sidebarCollapsed: boolean,
	elementId: string,
	classOpen: string,
	classClose: string
) {
	const el = window?.document?.getElementById(elementId)
	if (!el) return

	el.classList.toggle(classOpen, !sidebarCollapsed)
	el.classList.toggle(classClose, sidebarCollapsed)
}

export function hex2rgb(hex: string): number[] {
	const rgba = colord(hex).toRgb()
	return [rgba.r, rgba.g, rgba.b]
}
export function hex2hsl(hex: string): number[] {
	const hsl = colord(hex).toHsl()
	return [hsl.h, hsl.s, hsl.l]
}

export function exposure(color: string, amount: number): string {
	if (amount >= 0) {
		return colord(color).lighten(amount).desaturate(amount).toHex()
	}
	return colord(color)
		.lighten(amount)
		.desaturate(amount * -1)
		.toHex()
}

export function exportPrimaryShades(color: string): { [key: string]: string } {
	const rgba = colord(color).toRgb()
	return {
		"005": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.05 }).toRgbString(),
		"010": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.1 }).toRgbString(),
		"015": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.15 }).toRgbString(),
		"020": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.2 }).toRgbString(),
		"030": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.3 }).toRgbString(),
		"040": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.4 }).toRgbString(),
		"050": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.5 }).toRgbString(),
		"060": colord({ r: rgba.r, g: rgba.g, b: rgba.b, a: 0.6 }).toRgbString()
	}
}

export function getTypeValue(origin: object, val: string) {
	if (val && val.indexOf("{") === 0) {
		const path = val.replace("{", "").replace("}", "")
		return _get(origin, path)
	}

	return val
}

export function getThemeColors(colors: Record<ColorType, string>) {
	const colorActions: ColorAction[] = [
		{ scene: "", handler: color => color },
		{ scene: "Suppl", handler: color => exposure(color, 0.1) },
		{ scene: "Hover", handler: color => exposure(color, 0.05) },
		{ scene: "Pressed", handler: color => exposure(color, -0.2) }
	]

	const themeColor: ThemeColor = {}

	for (const colorType in colors) {
		const colorValue = colors[colorType as ColorType]

		colorActions.forEach(action => {
			const colorKey: ColorKey = `${colorType as ColorType}Color${action.scene}`
			themeColor[colorKey] = action.handler(colorValue)
		})
	}

	return themeColor
}

```
src/views/Apps/Mailbox.vue
```vue
<template>
	<div class="page page-wrapped page-mobile-full flex flex-col page-without-footer">
		<SegmentedPage
			:main-content-class="showList ? '!p-0' : composeEmail ? '!h-full flex' : ''"
			hide-menu-btn
			enable-resize
			:use-main-scroll="!composeEmail"
			@mounted="setCtx"
		>
			<template #sidebar-header>
				<div class="compose-btn-wrap">
					<n-button strong secondary type="primary" size="large" @click="newEmail()">New message</n-button>
				</div>
			</template>
			<template #sidebar-content>
				<n-menu v-model:value="activeMenuKey" class="folders-list" :options="menuOptions" />

				<div class="section labels-list">
					<p class="mb-3 opacity-50">Labels</p>
					<div class="list">
						<div
							v-for="label of mailboxStore.labels"
							:key="label.title"
							class="label flex items-center"
							:class="[`l-${label.id}`, label.id === mailboxStore.activeLabel ? 'l-active' : '']"
							@click="setLabel(label.id)"
						>
							<div class="l-icon flex">
								<Icon :size="14" :name="LabelIcon" :color="labelsColors[label.id]" />
							</div>
							<div class="l-title">
								{{ label.title }}
							</div>
						</div>
					</div>
				</div>
			</template>

			<template #main-toolbar>
				<div v-if="showList" class="flex gap-5 item-appear item-appear-bottom">
					<ActionToolbar
						v-model:search="search"
						class="grow"
						:check-control="checkControl"
						@toggle-check-all="toggleCheckAll()"
					/>
					<div v-if="!checkControl" class="new-btn flex justify-center opacity-50">
						<n-button text @click="newEmail()">
							<Icon :size="20" :name="PenIcon" />
						</n-button>
					</div>
					<n-button
						v-if="!checkControl"
						text
						class="sidebar-toggler opacity-50"
						@click="ctxPage?.openSidebar()"
					>
						<Icon :size="24" :name="MenuIcon" />
					</n-button>
				</div>
				<div v-else-if="selectedEmail" class="flex gap-5 align-center item-appear item-appear-bottom">
					<n-button text @click="goBack()">
						<Icon :size="24" :name="ArrowLeftIcon" />
					</n-button>
					<EmailToolbar :email="selectedEmail" class="grow" />
					<Navigator />
				</div>
				<div v-else-if="composeEmail" class="flex gap-5 align-center item-appear item-appear-bottom">
					<n-button text @click="goBack()">
						<Icon :size="24" :name="ArrowLeftIcon" />
					</n-button>
					<span>Compose message</span>
				</div>
			</template>
			<template #main-content>
				<template v-if="showList">
					<EmailComponent
						v-for="email of emails"
						:key="email.id"
						:email="email"
						class="item-appear item-appear-bottom item-appear-005"
						@select="selectedEmail = $event"
					/>
				</template>
				<EmailContent
					v-else-if="selectedEmail"
					:email="selectedEmail"
					class="item-appear item-appear-bottom item-appear-010"
				/>
				<ComposeView
					v-else-if="composeEmail"
					:email="composeEmail"
					class="item-appear item-appear-bottom item-appear-010"
				/>
			</template>
		</SegmentedPage>
	</div>
</template>

<script setup lang="ts">
import type { Email } from "@/mock/mailbox"
import ActionToolbar from "@/components/apps/Mailbox/ActionToolbar.vue"
import ComposeView from "@/components/apps/Mailbox/ComposeView.vue"
import EmailComponent from "@/components/apps/Mailbox/Email.vue"
import EmailContent from "@/components/apps/Mailbox/EmailContent.vue"
import EmailToolbar from "@/components/apps/Mailbox/EmailToolbar.vue"
import Navigator from "@/components/apps/Mailbox/Navigator.vue"
import Icon from "@/components/common/Icon.vue"
import SegmentedPage, { type CtxSegmentedPage } from "@/components/common/SegmentedPage.vue"
import { useHideLayoutFooter } from "@/composables/useHideLayoutFooter"
import { useMailboxStore } from "@/stores/apps/useMailboxStore"
import { useThemeStore } from "@/stores/theme"
import { renderIcon } from "@/utils"
import dayjs from "@/utils/dayjs"
import { type MenuOption, NButton, NMenu } from "naive-ui"
import { computed, type ComputedRef, onMounted, ref, watch } from "vue"

const InboxIcon = "carbon:email"
const SentIcon = "carbon:send"
const DraftIcon = "carbon:edit"
const StarredIcon = "carbon:star"
const ArrowLeftIcon = "carbon:arrow-left"
const SpamIcon = "ion:alert-circle-outline"
const TrashIcon = "carbon:trash-can"
const LabelIcon = "carbon:bookmark-filled"
const MenuIcon = "ph:list-light"
const PenIcon = "carbon:pen"

const themeStore = useThemeStore()
const mailboxStore = useMailboxStore()
const ctxPage = ref<CtxSegmentedPage | null>(null)

const loadList = ref(false)
const search = ref("")
const selectedEmail = ref<Email | null>(null)
const composeEmail = ref<Partial<Email> | null>(null)
const activeMenuKey = ref(mailboxStore.activeFolder)
const showList = computed(() => !selectedEmail.value && !composeEmail.value)

const emails = computed(() => {
	return mailboxStore.emails
		.filter((e: Email) => e.folder === mailboxStore.activeFolder)
		.filter((e: Email) =>
			mailboxStore.activeLabel ? e.labels.map(l => l.id).includes(mailboxStore.activeLabel) : true
		)
		.filter((e: Email) =>
			search.value ? (e.name + e.subject).toLowerCase().includes(search.value.toLowerCase()) : true
		)
		.map((e: Email) => {
			e.dateText =
				dayjs(e.date).format("YYYY-MM-DD") === dayjs().format("YYYY-MM-DD")
					? dayjs(e.date).format("HH:mm")
					: dayjs(e.date).format("D MMM")
			return e
		})
		.sort((a: Email, b: Email) => b.date.getTime() - a.date.getTime())
})

const checkControl: ComputedRef<0 | 1 | 2> = computed(() => {
	const emlCount = emails.value.length
	const selCount = emails.value.filter((e: Email) => e.selected).length

	if (!emlCount) {
		return 0
	}
	if (selCount === emlCount) {
		return 1
	}
	if (selCount) {
		return 2
	}
	return 0
})

const secondaryColors = computed(() => themeStore.secondaryColors)

const labelsColors = {
	personal: secondaryColors.value.secondary1,
	office: secondaryColors.value.secondary2,
	important: secondaryColors.value.secondary3,
	shop: secondaryColors.value.secondary4
} as unknown as { [key: string]: string }

const menuOptions = mailboxStore.folders.map(folder => {
	let icon

	if (folder.id === "inbox") {
		icon = InboxIcon
	}
	if (folder.id === "sent") {
		icon = SentIcon
	}
	if (folder.id === "draft") {
		icon = DraftIcon
	}
	if (folder.id === "starred") {
		icon = StarredIcon
	}
	if (folder.id === "spam") {
		icon = SpamIcon
	}
	if (folder.id === "trash") {
		icon = TrashIcon
	}

	return {
		label: folder.title,
		icon: icon && renderIcon(icon),
		key: folder.id
	}
}) as MenuOption[]

watch(activeMenuKey, val => {
	setFolder(val)
})

function setCtx(ctx: CtxSegmentedPage) {
	ctxPage.value = ctx
}

function goBack() {
	selectedEmail.value = null
	composeEmail.value = null
}

function setLabel(label: string) {
	selectedEmail.value = null
	mailboxStore.setActiveLabel(label)
}

function setFolder(folder: string) {
	selectedEmail.value = null
	mailboxStore.setActiveFolder(folder)
}

function toggleCheckAll() {
	const check = checkControl.value !== 1

	for (const email of emails.value) {
		if (email.selected !== check) {
			mailboxStore.toggleCheck(email)
		}
	}
}

function newEmail() {
	selectedEmail.value = null
	ctxPage.value?.closeSidebar?.()

	composeEmail.value = {
		email: "",
		subject: "",
		body: ""
	}
}

onMounted(() => {
	setTimeout(() => {
		loadList.value = true
	}, 100)
})

// :has() CSS relational pseudo-class not yet supported by Firefox
// (https://caniuse.com/css-has)
// at the moment this worker around permit to hide Layout Footer
useHideLayoutFooter()
</script>

<style lang="scss" scoped>
.page {
	// border: 1px solid red;

	.compose-btn-wrap {
		width: 100%;

		:deep() {
			.n-button {
				width: 100%;
			}
		}
	}

	.folders-list {
		margin-bottom: 20px;

		:deep() {
			.n-menu-item-content::before {
				left: 0;
				right: 0;
			}
		}
	}

	.labels-list {
		border: var(--border-small-050);
		border-radius: var(--border-radius);
		padding: 16px 22px;

		.list {
			.label {
				cursor: pointer;
				gap: 8px;
				margin-bottom: 6px;
				.l-icon {
					width: 10px;
					height: 10px;
					background-color: var(--color, --primary-color);
					border-radius: 50%;
				}
				.l-title {
					font-size: 14px;
					opacity: 0.9;
					padding-top: 3px;
					line-height: 1.2;
				}

				&:hover {
					.l-title {
						opacity: 1;
					}
				}
				&.l-active {
					.l-title {
						opacity: 1;
						font-weight: bold;
					}
				}
			}
		}
	}

	.sidebar-toggler,
	.new-btn {
		display: none;
	}

	@media (max-width: 700px) {
		.sidebar-toggler,
		.new-btn {
			display: flex;
		}
	}
}
</style>

```