- src/views/Apps/Mailbox.vue
- src/components/apps/Mailbox/ActionToolbar.vue
- src/components/apps/Mailbox/ComposeView.vue
- src/components/apps/Mailbox/Email.vue
- src/components/apps/Mailbox/EmailContent.vue
- src/components/apps/Mailbox/EmailToolbar.vue
- src/components/apps/Mailbox/Navigator.vue
- src/components/common/Icon.vue
- src/components/common/SegmentedPage.vue
- src/composables/useHideLayoutFooter.ts

src/views/Apps/Mailbox.vue
```vue
<template>
	<div class="page page-wrapped page-mobile-full flex flex-col page-without-footer">
		<SegmentedPage
			:main-content-class="showList ? '!p-0' : composeEmail ? '!h-full flex' : ''"
			hide-menu-btn
			enable-resize
			:use-main-scroll="!composeEmail"
			@mounted="setCtx"
		>
			<template #sidebar-header>
				<div class="compose-btn-wrap">
					<n-button strong secondary type="primary" size="large" @click="newEmail()">New message</n-button>
				</div>
			</template>
			<template #sidebar-content>
				<n-menu v-model:value="activeMenuKey" class="folders-list" :options="menuOptions" />

				<div class="section labels-list">
					<p class="mb-3 opacity-50">Labels</p>
					<div class="list">
						<div
							v-for="label of mailboxStore.labels"
							:key="label.title"
							class="label flex items-center"
							:class="[`l-${label.id}`, label.id === mailboxStore.activeLabel ? 'l-active' : '']"
							@click="setLabel(label.id)"
						>
							<div class="l-icon flex">
								<Icon :size="14" :name="LabelIcon" :color="labelsColors[label.id]" />
							</div>
							<div class="l-title">
								{{ label.title }}
							</div>
						</div>
					</div>
				</div>
			</template>

			<template #main-toolbar>
				<div v-if="showList" class="flex gap-5 item-appear item-appear-bottom">
					<ActionToolbar
						v-model:search="search"
						class="grow"
						:check-control="checkControl"
						@toggle-check-all="toggleCheckAll()"
					/>
					<div v-if="!checkControl" class="new-btn flex justify-center opacity-50">
						<n-button text @click="newEmail()">
							<Icon :size="20" :name="PenIcon" />
						</n-button>
					</div>
					<n-button
						v-if="!checkControl"
						text
						class="sidebar-toggler opacity-50"
						@click="ctxPage?.openSidebar()"
					>
						<Icon :size="24" :name="MenuIcon" />
					</n-button>
				</div>
				<div v-else-if="selectedEmail" class="flex gap-5 align-center item-appear item-appear-bottom">
					<n-button text @click="goBack()">
						<Icon :size="24" :name="ArrowLeftIcon" />
					</n-button>
					<EmailToolbar :email="selectedEmail" class="grow" />
					<Navigator />
				</div>
				<div v-else-if="composeEmail" class="flex gap-5 align-center item-appear item-appear-bottom">
					<n-button text @click="goBack()">
						<Icon :size="24" :name="ArrowLeftIcon" />
					</n-button>
					<span>Compose message</span>
				</div>
			</template>
			<template #main-content>
				<template v-if="showList">
					<EmailComponent
						v-for="email of emails"
						:key="email.id"
						:email="email"
						class="item-appear item-appear-bottom item-appear-005"
						@select="selectedEmail = $event"
					/>
				</template>
				<EmailContent
					v-else-if="selectedEmail"
					:email="selectedEmail"
					class="item-appear item-appear-bottom item-appear-010"
				/>
				<ComposeView
					v-else-if="composeEmail"
					:email="composeEmail"
					class="item-appear item-appear-bottom item-appear-010"
				/>
			</template>
		</SegmentedPage>
	</div>
</template>
```

src/components/apps/Mailbox/ActionToolbar.vue
```vue
<template>
	<div class="action-toolbar flex items-center">
		<div class="flex">
			<n-checkbox
				:checked="checkControl === 1"
				:indeterminate="checkControl === 2"
				size="large"
				@click="toggleCheckAll()"
			/>
		</div>
		<div v-if="checkControl" class="flex grow items-center gap-3">
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="TrashIcon" />
					</n-button>
				</template>
				<span>Delete</span>
			</n-tooltip>

			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="LabelOutIcon" />
					</n-button>
				</template>
				<span>Add label</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="FolderIcon" />
					</n-button>
				</template>
				<span>Move to folder</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="StarredIcon" />
					</n-button>
				</template>
				<span>Star</span>
			</n-tooltip>
		</div>
		<div v-if="!checkControl" class="flex grow search-box">
			<n-input v-model:value="search" placeholder="Search..." clearable size="medium">
				<template #prefix>
					<Icon :name="SearchIcon" />
				</template>
			</n-input>
		</div>
		<div v-if="!checkControl" class="flex justify-center opacity-50">
			<n-button text>
				<Icon :size="18" :name="RefreshIcon" />
			</n-button>
		</div>
		<div v-if="!checkControl" class="menu-btn flex justify-center opacity-50">
			<n-button text @click="sidebarOpen = true">
				<Icon :size="24" :name="MenuIcon" />
			</n-button>
		</div>
	</div>
</template>
```

src/components/apps/Mailbox/ComposeView.vue
```vue
<template>
	<div class="compose-view flex flex-col grow">
		<n-form label-placement="left" size="small" label-width="auto">
			<n-form-item label="To">
				<n-input-group>
					<n-auto-complete
						v-model:value="emailForm.email"
						:input-props="{
							autocomplete: 'disabled'
						}"
						:options="autoCompleteOptions"
						placeholder="Email"
					/>
					<n-button type="tertiary">CC</n-button>
					<n-button type="tertiary">BCC</n-button>
				</n-input-group>
			</n-form-item>
			<n-form-item label="Subject">
				<n-input placeholder="Message subject..." />
			</n-form-item>
		</n-form>
		<div class="compose-view-attachments flex justify-end">
			<n-button ghost>
				<template #icon>
					<Icon :name="DocumentAddIcon" />
				</template>

				Add attachment
			</n-button>
		</div>
		<div class="compose-view-body grow flex flex-col scrollbar-styled">
			<QuillEditor v-if="mounted" theme="snow" toolbar="minimal" @blur="resetScroll()" />
		</div>
		<div class="compose-view-footer flex justify-end">
			<n-button-group>
				<n-button type="primary" ghost>
					<template #icon>
						<Icon :name="SentIcon" />
					</template>
					Send
				</n-button>
				<n-dropdown
					trigger="click"
					:options="[
						{
							label: 'Save as draft',
							key: 'Save as draft'
						},
						{
							label: 'Postponed sending',
							key: 'Postponed sending'
						}
					]"
				>
					<n-button type="primary" ghost>
						<template #icon>
							<Icon :name="ChevronDownIcon" />
						</template>
					</n-button>
				</n-dropdown>
			</n-button-group>
		</div>
	</div>
</template>
```

src/components/apps/Mailbox/Email.vue
```vue
<template>
	<div class="email flex items-center" :class="{ selected: email.selected, seen: email.seen }" @click="select(email)">
		<div class="check">
			<n-checkbox :checked="email.selected" size="large" @click.stop="toggleCheck(email)" />
		</div>
		<div class="starred flex" :class="{ 'opacity-50': !email.starred }">
			<n-button text @click.stop="toggleStar(email)">
				<Icon v-if="email.starred" :size="16" :name="StarActiveIcon" :color="primaryColor" />
				<Icon v-else :size="16" :name="StarIcon" />
			</n-button>
		</div>
		<div class="avatar flex">
			<n-avatar round size="small" :src="email.avatar" :img-props="{ alt: `${email.name}-avatar` }" />
		</div>
		<div class="title grow">
			<span class="name">
				{{ email.name }}
			</span>
			<span class="subject">
				{{ email.subject }}
			</span>
		</div>
		<div class="labels flex">
			<Icon
				v-for="label of email.labels"
				:key="label.id"
				:size="16"
				:color="labelsColors[label.id]"
				:name="LabelIcon"
			/>
		</div>
		<div v-if="email.attachments.length" class="attachments flex">
			<Icon :size="16" :name="AttachmentIcon" />
		</div>
		<div class="date text-secondary-color">
			{{ email.dateText }}
		</div>
		<div class="actions text-secondary-color flex items-start gap-3">
			<n-button text>
				<Icon :size="20" :name="TrashIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="LabelOutIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="FolderIcon" />
			</n-button>
		</div>
	</div>
</template>
```

src/components/apps/Mailbox/EmailContent.vue
```vue
<template>
	<div class="email-content">
		<div class="email-sender flex flex-wrap items-center">
			<div class="avatar flex">
				<n-avatar round :size="45" :src="email.avatar" :img-props="{ alt: `${email.name}-avatar` }" />
			</div>
			<div class="info grow flex flex-wrap items-center">
				<div class="title grow flex flex-col">
					<span class="name">
						{{ email.name }}
					</span>
					<span class="email">
						{{ email.email }}
					</span>
				</div>
				<div class="date">
					<n-time :time="email.date" format="d MMM @ HH:mm" />
				</div>
			</div>
		</div>
		<div class="email-subject">
			<span class="subject">
				{{ email.subject }}
			</span>
			<span
				v-for="label of email.labels"
				:key="label.id"
				class="label custom-label"
				:style="`--label-color:${labelsColors[label.id]}`"
			>
				{{ label.title }}
			</span>
		</div>
		<div class="email-body" v-html="email.body" />
		<div v-if="email.attachments.length" class="email-attachments flex flex-wrap">
			<div v-for="attachment of email.attachments" :key="attachment.name" class="attachment-item flex">
				<div class="attachment-icon">
					<Icon :size="26" :name="FileIcon" />
				</div>
				<div class="attachment-info">
					<div class="attachment-name">{{ attachment.name }}</div>
					<div class="attachment-size">{{ attachment.size }}</div>
				</div>
			</div>
		</div>
	</div>
</template>
```

src/components/apps/Mailbox/EmailToolbar.vue
```vue
<template>
	<div class="email-toolbar flex items-center">
		<div class="actions-btns flex items-center gap-2">
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="TrashIcon" />
					</n-button>
				</template>
				<span>Delete</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="LabelOutIcon" />
					</n-button>
				</template>
				<span>Add label</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="FolderIcon" />
					</n-button>
				</template>
				<span>Move to folder</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text>
						<Icon :size="20" :name="PrinterIcon" />
					</n-button>
				</template>
				<span>Print</span>
			</n-tooltip>
			<n-tooltip>
				<template #trigger>
					<n-button text @click.stop="toggleStar(email)">
						<Icon v-if="email.starred" :size="20" :name="StarActiveIcon" :color="primaryColor" />
						<Icon v-else :size="20" :name="StarIcon" />
					</n-button>
				</template>
				<span>Star</span>
			</n-tooltip>
		</div>
		<div class="menu-btns flex items-center">
			<n-dropdown :options="menuOptions">
				<n-button text>
					<Icon :size="24" :name="MenuHorizontalIcon" />
				</n-button>
			</n-dropdown>
		</div>
		<div class="grow" />
		<div class="reply-btns flex items-center gap-2">
			<n-button text>
				<Icon :size="20" :name="ReplyIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="ReplyAllIcon" />
			</n-button>
			<n-button text>
				<Icon :size="20" :name="ForwardIcon" />
			</n-button>
		</div>
	</div>
</template>
```

src/components/apps/Mailbox/Navigator.vue
```vue
<template>
	<div class="navigator flex items-center">
		<div class="nav-btns flex items-center gap-2">
			<span class="opacity-70">1 - 30 of 635</span>
			<n-button text size="small">
				<Icon :size="24" :name="ChevronLeftIcon" />
			</n-button>
			<n-button text size="small">
				<Icon :size="24" :name="ChevronRightIcon" />
			</n-button>
		</div>
	</div>
</template>
```

src/components/common/Icon.vue
```vue
<template>
	<component :is="componentName" v-bind="options">
		<template v-if="$slots.default">
			<slot />
		</template>
		<template v-else>
			<Icon v-if="icon" :icon :width="size" :height="size" />
		</template>
	</component>
</template>
```

src/components/common/SegmentedPage.vue
```vue
<template>
	<n-split
		ref="splitPane"
		direction="horizontal"
		:default-size="sanitizedDefaultSplit"
		:min="0"
		:max="1"
		:resize-trigger-size="0"
		:disabled="!enableResize || splitDisabled"
		class="wrapper flex grow"
		:class="[{ 'sidebar-open': sidebarOpen }, `sidebar-position-${sidebarPosition}`]"
		:pane1-style="pane1Style"
	>
		<template #[tplNameSide]>
			<div v-if="sidebarAvailable" ref="sidebar" class="sidebar flex flex-col">
				<div v-if="$slots['sidebar-header']" class="sidebar-header flex items-center">
					<slot name="sidebar-header" />
				</div>
				<div v-if="$slots['sidebar-content']" class="sidebar-main grow">
					<n-scrollbar class="max-h-full">
						<div class="sidebar-main-content" :style="sidebarContentStyle" :class="sidebarContentClass">
							<slot name="sidebar-content" />
						</div>
					</n-scrollbar>
				</div>
				<div v-if="$slots['sidebar-footer']" class="sidebar-footer flex items-center">
					<slot name="sidebar-footer" />
				</div>
			</div>
		</template>

		<template #resize-trigger>
			<div class="split-trigger">
				<div class="split-trigger-icon">
					<Icon :name="SplitIcon" :size="12" />
				</div>
			</div>
		</template>

		<template #[tplNameMain]>
			<div class="main flex-grow flex flex-col">
				<div v-if="$slots['main-toolbar']" class="main-toolbar flex items-center">
					<div v-if="sidebarAvailable && !hideMenuBtn" class="menu-btn flex justify-center opacity-50">
						<n-button text @click="sidebarOpen = true">
							<Icon :size="24" :name="MenuIcon" />
						</n-button>
					</div>

					<div class="grow">
						<slot name="main-toolbar" />
					</div>
				</div>
				<div class="main-view grow" :class="{ 'no-container-query': disableContainerQuery }">
					<n-scrollbar v-if="useMainScroll" ref="mainScrollbar" class="max-h-full">
						<div class="main-content" :style="mainContentStyle" :class="mainContentClass">
							<slot name="main-content" />
						</div>
					</n-scrollbar>
					<div v-else class="main-content" :style="mainContentStyle" :class="mainContentClass">
						<slot name="main-content" />
					</div>
				</div>
				<div v-if="$slots['main-footer']" class="main-footer flex items-center">
					<div class="wrap">
						<slot name="main-footer" />
					</div>
				</div>

				<div v-if="sidebarOpen" class="main-overlay" />
			</div>
		</template>
	</n-split>
</template>
```
