import { readFileSync, existsSync, writeFileSync } from 'fs';
import { resolve, parse, relative } from 'path';

const inputFilePath = Bun.argv[2];

if (!inputFilePath) {
	console.error('Please provide a file path as an argument.');
	process.exit(1);
}

const absolutePath = resolve(inputFilePath);

if (!existsSync(absolutePath)) {
	console.log(`Can't find ${absolutePath}`);
	process.exit(1);
}

const fileContent = readFileSync(absolutePath, 'utf-8');
const filePaths = fileContent.trim().split('\n');

let markdownTable = '| File | Line Count |\n|------|------------|\n';
let totalLines = 0;

filePaths.forEach(filePath => {
	const absoluteFilePath = resolve(filePath);
	if (existsSync(absoluteFilePath)) {
		const fileContent = readFileSync(absoluteFilePath, 'utf-8');
		const lineCount = fileContent.split('\n').length;
		markdownTable += `| ${filePath} | ${lineCount} |\n`;
		totalLines += lineCount;
	} else {
		markdownTable += `| ${filePath} | not found |\n`;
	}
});

markdownTable += `\ntotal lines = ${totalLines}`;

// Generate output file name
const { name, ext } = parse(absolutePath);
const outputFileName = `${name}${ext}.line-count.md`;
const outputPath = resolve(outputFileName);

// Write to output file
writeFileSync(outputPath, markdownTable);

// Get relative path for logging
const relativePath = relative(process.cwd(), outputPath);

console.log(`created file ${relativePath}, ${totalLines} lines total`);
