const fs = require('fs');
const path = require('path');

function findFile(basePath) {
	if (fs.existsSync(basePath) && fs.statSync(basePath).isFile()) {
		return basePath;
	}

	const possibleExtensions = ['', '.ts', '.js', '.mjs', '.cjs', '.json', '.vue'];

	// Check for file with extensions
	for (const ext of possibleExtensions) {
		const fullPath = basePath + ext;
		if (fs.existsSync(fullPath) && fs.statSync(fullPath).isFile()) {
			return fullPath;
		}
	}

	// Check for index file in directory
	if (fs.existsSync(basePath) && fs.statSync(basePath).isDirectory()) {
		for (const ext of possibleExtensions) {
			const indexPath = path.join(basePath, `index${ext}`);
			if (fs.existsSync(indexPath) && fs.statSync(indexPath).isFile()) {
				return indexPath;
			}
		}
	}

	return null;
}

function findAllImports(filePath, visited = new Set()) {
	if (visited.has(filePath)) return [];
	visited.add(filePath);

	const resolvedPath = findFile(filePath);
	if (!resolvedPath) {
		console.warn(`Skipping: ${filePath} (File not found)`);
		return [];
	}

	const content = fs.readFileSync(resolvedPath, 'utf8');
	const imports = content.match(/^import.*from\s+["']@\/.*["']/gm) || [];

	const childPaths = imports.map(imp => {
		const match = imp.match(/@\/(.*?)["']/);
		if (!match) return null;

		const importPath = match[1];
		const srcPath = path.resolve(process.cwd(), 'src', importPath);

		const resolvedChildPath = findFile(srcPath);
		if (!resolvedChildPath) {
			console.warn(`Could not find file for import: src/${importPath}`);
			return null;
		}
		return resolvedChildPath;
	}).filter(Boolean);

	const allPaths = [resolvedPath, ...childPaths];

	for (const childPath of childPaths) {
		allPaths.push(...findAllImports(childPath, visited));
	}

	return [...new Set(allPaths)];
}

function extractAllImports(filePath) {
	const resolvedPath = findFile(filePath);
	if (!resolvedPath) {
		console.warn(`Skipping import extraction: ${filePath} (File not found)`);
		return [];
	}
	const content = fs.readFileSync(resolvedPath, 'utf8');
	return content.match(/^import.*$/gm) || [];
}

function processComponent(componentPath) {
	const fullPath = path.resolve(process.cwd(), componentPath);
	const resolvedPath = findFile(fullPath);
	if (!resolvedPath) {
		console.error(`Component not found: ${fullPath}`);
		return;
	}

	// Step 1
	const allPaths = findAllImports(resolvedPath);
	const relativePaths = allPaths.map(p => path.relative(process.cwd(), p));
	const uniquePaths = [...new Set(relativePaths)];

	const vueFiles = uniquePaths.filter(p => p.endsWith('.vue') && p !== componentPath).sort();
	const otherFiles = uniquePaths.filter(p => !p.endsWith('.vue')).sort();

	const sortedPaths = [componentPath, ...vueFiles, ...otherFiles];

	const componentName = path.basename(componentPath);
	const globsFileName = path.resolve(process.cwd(), `globs.${componentName}.txt`);
	fs.writeFileSync(globsFileName, sortedPaths.join('\n'));

	// Step 2
	const allImports = new Set();
	const includedFiles = new Set(sortedPaths.map(p => {
		return '@/' + p.replace(/^src\//, '').replace(/\.[^/.]+$/, '');
	}));

	const libraryImports = {};

	for (const filePath of allPaths) {
		const imports = extractAllImports(filePath);
		imports.forEach(imp => {
			const match = imp.match(/from\s+['"]([@/].*?)['"]!/);
			if (match) {
				const importPath = match[1];
				const importPathWithoutExt = importPath.replace(/\.[^/.]+$/, '');
				if (!includedFiles.has(importPathWithoutExt)) {
					allImports.add(imp);
				}
			} else {
				const libraryMatch = imp.match(/{\s*(.*?)\s*}\s*from\s*['"](.*?)['"]/);
				if (libraryMatch) {
					const [, components, library] = libraryMatch;
					if (!libraryImports[library]) {
						libraryImports[library] = new Set();
					}
					components.split(',').forEach(comp => {
						libraryImports[library].add(comp.trim());
					});
				} else {
					allImports.add(imp);
				}
			}
		});
	}

	// Combine and sort library imports
	for (const [library, components] of Object.entries(libraryImports)) {
		const sortedComponents = [...components].sort((a, b) => {
			const aName = a.replace(/^type\s+/, '');
			const bName = b.replace(/^type\s+/, '');
			return aName.localeCompare(bName);
		});
		const combinedImport = `import { ${sortedComponents.join(', ')} } from "${library}"`;
		allImports.add(combinedImport);
	}

	const sortedImports = [...allImports]
		.filter(imp => !imp.includes('from "@/'))
		.sort((a, b) => {
			const getLibrary = (imp) => {
				const match = imp.match(/from\s+['"](.*?)['"]/);
				return match ? match[1] : '';
			};
			return getLibrary(a).localeCompare(getLibrary(b));
		});

	const importsFileName = path.resolve(process.cwd(), `imports.${componentName}.txt`);
	fs.writeFileSync(importsFileName, sortedImports.join('\n'));

	// Log only the absolute paths of the created files
	console.log('\nCreated files:');
	console.log(globsFileName);
	console.log(importsFileName);
}

// Usage
const componentPath = process.argv[2];
if (!componentPath) {
	console.error('Please provide a component path');
} else {
	processComponent(componentPath);
}
