import React, { useMemo, useEffect, useState } from 'react';
import chroma from 'chroma-js';
import { range } from 'lodash';
import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';
import { auColors } from './au-colors';

export interface OrderConstraintsProps {
  constraints: DeBidConstraints | null;
  height?: number;
  maxQuantity?: number;
  orderQuantity: number | null;
  orderType: OrderType | null;
  showLabels?: boolean;
  tickFontSize?: number;
  tickStep?: number;
  width?: number;
  priceChange?: 'up' | 'down' | null;
  className?: string;
}

interface TickData {
  vol: number;
  x: number;
}

export const OrderConstraints: React.FC<OrderConstraintsProps> = ({
  constraints,
  height = 30,
  maxQuantity = 50,
  orderQuantity,
  orderType,
  showLabels = true,
  tickFontSize = 12,
  tickStep = 10,
  width = 300,
  priceChange = null,
  className = ''
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  // Trigger animation when price changes
  useEffect(() => {
    if (priceChange) {
      setIsAnimating(true);
      const timer = setTimeout(() => setIsAnimating(false), 1000);
      return () => clearTimeout(timer);
    }
  }, [priceChange]);

  // Layout calculations
  const barWidth = width;
  const midX = barWidth / 2;
  const pixelsPerVol = (0.9 * width) / (maxQuantity * 2);
  const toPixels = (vol: number) => vol * pixelsPerVol;
  const tickWidth = tickStep * pixelsPerVol;

  // Color calculations
  const buyDimmed = chroma(auColors.auBuy()).alpha(0.1).toString();
  const sellDimmed = chroma(auColors.auSell()).alpha(0.1).toString();
  const orderColor = auColors.orderBright(orderType);

  // Constraint values
  const maxBuyQuantity = constraints?.max_buy_quantity ?? 0;
  const minBuyQuantity = constraints?.min_buy_quantity ?? 0;
  const maxSellQuantity = constraints?.max_sell_quantity ?? 0;
  const minSellQuantity = constraints?.min_sell_quantity ?? 0;

  // Buy constraint calculations
  const buyConstraintX = midX - toPixels(maxBuyQuantity);
  const buyConstraintWidth = toPixels(maxBuyQuantity - minBuyQuantity);

  // Sell constraint calculations  
  const sellConstraintX = midX + toPixels(minSellQuantity);
  const sellConstraintWidth = toPixels(maxSellQuantity - minSellQuantity);

  // Order indicator calculations
  const orderX = useMemo(() => {
    if (!orderQuantity || !orderType) return 0;
    
    if (orderType === OrderType.BUY) {
      return midX - toPixels(orderQuantity);
    } else if (orderType === OrderType.SELL) {
      return midX + toPixels(orderQuantity);
    }
    return midX;
  }, [orderQuantity, orderType, midX, toPixels]);

  const orderWidth = 2;

  // Tick calculations
  const tickXArr = useMemo(() => {
    const ticks = [midX];
    const count = Math.floor(maxQuantity / tickStep);

    for (let i = 1; i <= count; i++) {
      const offset = i * tickStep * pixelsPerVol;
      ticks.push(midX - offset);
      ticks.push(midX + offset);
    }

    return ticks;
  }, [midX, maxQuantity, tickStep, pixelsPerVol]);

  const buyTicks: TickData[] = useMemo(() => {
    const ticks = range(tickStep, maxQuantity + 1, tickStep);
    return ticks.reverse().map(vol => ({
      vol,
      x: midX - toPixels(vol)
    }));
  }, [tickStep, maxQuantity, midX, toPixels]);

  const sellTicks: TickData[] = useMemo(() => {
    const ticks = range(tickStep, maxQuantity + 1, tickStep);
    return ticks.map(vol => ({
      vol,
      x: midX + toPixels(vol)
    }));
  }, [tickStep, maxQuantity, midX, toPixels]);

  return (
    <div
      className={`order-constraints-bar ${className}`}
      style={{
        width: `${width}px`,
        height: `${height}px`,
        position: 'relative'
      }}
    >
      {/* Labels section */}
      {showLabels && (
        <div
          style={{
            position: 'absolute',
            fontSize: `${tickFontSize}px`,
            top: `${height - 2 + tickFontSize / 2}px`
          }}
        >
          {/* Zero label */}
          <div
            style={{
              color: 'white',
              position: 'absolute',
              left: `${midX}px`,
              transform: 'translateX(-50%)',
              textAlign: 'center'
            }}
          >
            0
          </div>

          {/* Buy tick labels */}
          {buyTicks.map((tick, i) => (
            <div
              key={`buy-${i}`}
              style={{
                position: 'absolute',
                left: `${tick.x}px`,
                color: auColors.auBuy(),
                width: `${tickWidth}px`,
                textAlign: 'center',
                transform: 'translateX(-50%)'
              }}
            >
              {tick.vol}
            </div>
          ))}

          {/* Sell tick labels */}
          {sellTicks.map((tick, i) => (
            <div
              key={`sell-${i}`}
              style={{
                position: 'absolute',
                left: `${tick.x}px`,
                color: auColors.auSell(),
                width: `${tickWidth}px`,
                textAlign: 'center',
                transform: 'translateX(-50%)'
              }}
            >
              {tick.vol}
            </div>
          ))}
        </div>
      )}

      {/* SVG Bar */}
      <svg
        width={barWidth}
        height={height}
        viewBox={`0 0 ${barWidth} ${height}`}
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Buy side base */}
        <rect
          x={0}
          y={3}
          width={barWidth / 2}
          height={height - 6}
          fill={buyDimmed}
          className="base-rect"
        />

        {/* Buy constraints */}
        <rect
          x={buyConstraintX}
          y={3}
          width={buyConstraintWidth}
          height={height - 6}
          fill={auColors.auBuyDimmed()}
          className={`constraint-rect buy-constraint ${
            isAnimating && priceChange === 'up' ? 'animating-increase' : ''
          } ${
            isAnimating && priceChange === 'down' ? 'animating-decrease' : ''
          }`}
        />

        {/* Sell side base */}
        <rect
          x={barWidth / 2}
          y={3}
          width={barWidth / 2}
          height={height - 6}
          fill={sellDimmed}
          className="base-rect"
        />

        {/* Sell constraints */}
        <rect
          x={sellConstraintX}
          y={3}
          width={sellConstraintWidth}
          height={height - 6}
          fill={auColors.auSellDim()}
          className={`constraint-rect sell-constraint ${
            isAnimating && priceChange === 'down' ? 'animating' : ''
          } ${
            isAnimating && priceChange === 'up' ? 'animating-increase' : ''
          }`}
        />

        {/* Tick marks */}
        {tickXArr.map((x, i) => (
          <rect
            key={i}
            x={x}
            y={0}
            width={0.25}
            height={height}
            fill="white"
          />
        ))}

        {/* Order indicator */}
        {orderType && orderType !== OrderType.NONE && (
          <rect
            x={orderX}
            y={0}
            width={orderWidth}
            height={height}
            fill={orderColor}
            className="order-indicator"
          />
        )}
      </svg>

      <style>{`
        .constraint-rect {
          transition: all 0.3s ease-in-out;
        }

        .animating-increase {
          opacity: 0.8;
          transform: scaleX(1.1);
        }

        .animating-decrease {
          opacity: 0.8;
          transform: scaleX(1.1);
        }

        .order-indicator {
          filter: drop-shadow(0 0 2px rgba(0,0,0,0.3));
        }
      `}</style>
    </div>
  );
};
