import chroma from 'chroma-js';
import { OrderType } from '../../api-client/connector/types/generated';

enum COLOR_THEME {
  RED_GREEN = 'RED_GREEN',
  PINK_GREEN = 'PINK_GREEN'
}

export class AuColors {
  private currentTheme = COLOR_THEME.RED_GREEN;

  // Base colors
  private auRed = 'hsl(0, 92%, 75%)';
  private sellPink4 = 'hsl(331, 95%, 70%)';
  private auGreen = 'hsl(120, 47%, 64%)';
  private buyGreen4 = 'hsl(91, 74%, 60%)';
  private auBlue = 'hsl(187, 50%, 60%)';
  private auBlueBright = 'hsla(187, 73.2%, 70.8%, 1)';

  switchTheme() {
    this.currentTheme = this.currentTheme === COLOR_THEME.RED_GREEN 
      ? COLOR_THEME.PINK_GREEN 
      : COLOR_THEME.RED_GREEN;
  }

  auBuy(): string {
    return this.currentTheme === COLOR_THEME.RED_GREEN ? this.auGreen : this.buyGreen4;
  }

  auSell(): string {
    return this.currentTheme === COLOR_THEME.RED_GREEN ? this.auRed : this.sellPink4;
  }

  auBuyBright(): string {
    return this.currentTheme === COLOR_THEME.RED_GREEN 
      ? 'hsl(120, 100%, 50%)' 
      : 'hsl(91, 84%, 70%)';
  }

  auSellBright(): string {
    return this.currentTheme === COLOR_THEME.RED_GREEN 
      ? 'hsl(0, 100%, 60%)' 
      : 'hsl(331, 100%, 75%)';
  }

  auBuyDimmed(): string {
    return chroma(this.auBuy()).alpha(0.50).hex();
  }

  auSellDim(): string {
    return chroma(this.auSell()).alpha(0.50).hex();
  }

  auNone(): string {
    return 'hsl(0,0%,90%)';
  }

  orderBright(side: OrderType | null): string {
    switch (side) {
      case OrderType.BUY:
        return this.auBuyBright();
      case OrderType.SELL:
        return this.auSellBright();
      default:
        return 'hsl(0, 0%, 90%)';
    }
  }

  orderQuantityTextColor(side: OrderType | null): string {
    switch (side) {
      case OrderType.BUY:
        return this.auBuy();
      case OrderType.SELL:
        return this.auSell();
      default:
        return this.auNone();
    }
  }
}

// Export singleton instance
export const auColors = new AuColors();
