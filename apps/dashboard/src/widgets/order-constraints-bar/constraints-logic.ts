import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';

export type PriceChange = 'INCREASE' | 'DECREASE';

export interface ConstraintUpdateParams {
  currentConstraints: DeBidConstraints;
  orderType: OrderType | null;
  orderQuantity: number;
  priceChange: PriceChange;
}

export const INITIAL_CONSTRAINTS: DeBidConstraints = {
  max_buy_quantity: 50,
  min_buy_quantity: 0,
  max_sell_quantity: 50,
  min_sell_quantity: 0
};

export function updateConstraints({
  currentConstraints,
  orderType,
  orderQuantity,
  priceChange
}: ConstraintUpdateParams): DeBidConstraints {
  // If no order, return current constraints
  if (!orderType || orderType === OrderType.NONE) {
    return currentConstraints;
  }

  // Make a copy to modify
  const newConstraints = { ...currentConstraints };

  if (orderType === OrderType.BUY) {
    if (priceChange === 'INCREASE') {
      // Shrink max buy to order quantity
      newConstraints.max_buy_quantity = orderQuantity;
    } else { // DECREASE
      // Zero out sell side
      newConstraints.min_sell_quantity = 0;
      newConstraints.max_sell_quantity = 0;
      // Set min buy to order quantity
      newConstraints.min_buy_quantity = orderQuantity;
    }
  } else if (orderType === OrderType.SELL) {
    if (priceChange === 'INCREASE') {
      // Zero out buy side
      newConstraints.max_buy_quantity = 0;
      newConstraints.min_buy_quantity = 0;
      // Set min sell to order quantity
      newConstraints.min_sell_quantity = orderQuantity;
    } else { // DECREASE
      // Shrink max sell to order quantity
      newConstraints.max_sell_quantity = orderQuantity;
    }
  }

  return newConstraints;
}

// Test scenarios for demos
export const TEST_SCENARIOS = {
  BASIC_TWO_SIDED: {
    max_buy_quantity: 50,
    min_buy_quantity: 0,
    max_sell_quantity: 50,
    min_sell_quantity: 0
  } as DeBidConstraints,

  ONE_SIDED_BUY: {
    max_buy_quantity: 40,
    min_buy_quantity: 10,
    max_sell_quantity: 0,
    min_sell_quantity: 0
  } as DeBidConstraints,

  ONE_SIDED_SELL: {
    max_buy_quantity: 0,
    min_buy_quantity: 0,
    max_sell_quantity: 40,
    min_sell_quantity: 10
  } as DeBidConstraints,

  SWITCH_LEFT: {
    max_buy_quantity: 40,
    min_buy_quantity: 0,
    max_sell_quantity: 50,
    min_sell_quantity: 0
  } as DeBidConstraints,

  SWITCH_RIGHT: {
    max_buy_quantity: 50,
    min_buy_quantity: 0,
    max_sell_quantity: 40,
    min_sell_quantity: 0
  } as DeBidConstraints
};
