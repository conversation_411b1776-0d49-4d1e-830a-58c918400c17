import React, { useMemo } from 'react';
import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';
import { OrderConstraints } from './OrderConstraints';
import { auColors } from './au-colors';

export interface OrderConstraintsWithRangeProps {
  orderQuantity: number;
  orderType: OrderType;
  constraints: DeBidConstraints;
  quantityLabel: string;
  className?: string;
}

export const OrderConstraintsWithRange: React.FC<OrderConstraintsWithRangeProps> = ({
  orderQuantity,
  orderType,
  constraints,
  quantityLabel,
  className = ''
}) => {
  const buyRange = useMemo(() => {
    if (constraints.min_buy_quantity === 0 && constraints.max_buy_quantity === 0) {
      return 'none';
    } else if (constraints.min_buy_quantity === constraints.max_buy_quantity) {
      return constraints.max_buy_quantity.toString();
    } else {
      return `${constraints.min_buy_quantity} to ${constraints.max_buy_quantity} ${quantityLabel}`;
    }
  }, [constraints.min_buy_quantity, constraints.max_buy_quantity, quantityLabel]);

  const sellRange = useMemo(() => {
    if (constraints.min_sell_quantity === 0 && constraints.max_sell_quantity === 0) {
      return 'none';
    } else if (constraints.min_sell_quantity === constraints.max_sell_quantity) {
      return constraints.max_sell_quantity.toString();
    } else {
      return `${constraints.min_sell_quantity} to ${constraints.max_sell_quantity} ${quantityLabel}`;
    }
  }, [constraints.min_sell_quantity, constraints.max_sell_quantity, quantityLabel]);

  return (
    <div
      className={`order-constraints-with-range ${className}`}
      style={{
        fontSize: '12px',
        height: '63px',
        overflow: 'hidden',
        padding: '2px',
        width: '260px'
      }}
    >
      <div
        style={{
          display: 'inline-block',
          fontSize: '12px',
          fontWeight: 'bold',
          marginLeft: '8px',
          textAlign: 'left',
          width: '120px',
          color: auColors.auBuyDimmed()
        }}
      >
        Buy: {buyRange}
      </div>

      <div
        style={{
          display: 'inline-block',
          fontSize: '12px',
          fontWeight: 'bold',
          marginLeft: '8px',
          textAlign: 'left',
          width: '120px',
          color: auColors.auSellDim()
        }}
      >
        Sell: {sellRange}
      </div>

      <div style={{ marginTop: '4px' }}>
        <OrderConstraints
          width={250}
          height={12}
          tickFontSize={10}
          constraints={constraints}
          orderQuantity={orderQuantity}
          orderType={orderType}
        />
      </div>
    </div>
  );
};
