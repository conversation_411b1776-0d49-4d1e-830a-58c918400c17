import type { <PERSON>a, StoryObj } from '@storybook/react';
import React, { useState } from 'react';
import { OrderConstraints } from './OrderConstraints';
import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';
import { INITIAL_CONSTRAINTS, updateConstraints, PriceChange, TEST_SCENARIOS } from './constraints-logic';

const meta: Meta = {
  title: 'Widgets/OrderConstraints/Logic Demos',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Interactive demos showing how order constraints logic works with different scenarios and price changes.'
      }
    }
  }
};

export default meta;
type Story = StoryObj;

export const BasicLogicDemo: Story = {
  render: () => {
    const [orderType, setOrderType] = useState<OrderType | null>(null);
    const [orderQuantity, setOrderQuantity] = useState(25);
    const [constraints, setConstraints] = useState<DeBidConstraints>({ ...INITIAL_CONSTRAINTS });
    const [lastPriceChange, setLastPriceChange] = useState<'up' | 'down' | null>(null);
    const [priorConstraints, setPriorConstraints] = useState<DeBidConstraints>({ ...INITIAL_CONSTRAINTS });

    const handlePriceChange = (change: PriceChange) => {
      setLastPriceChange(change === 'INCREASE' ? 'up' : 'down');

      setPriorConstraints(constraints);
      setConstraints(updateConstraints({
        currentConstraints: constraints,
        orderType: orderType,
        orderQuantity: orderQuantity,
        priceChange: change
      }));

      // Reset lastPriceChange after animation duration
      setTimeout(() => {
        setLastPriceChange(null);
      }, 1000);
    };

    const resetConstraints = () => {
      setConstraints({ ...INITIAL_CONSTRAINTS });
      setPriorConstraints({ ...INITIAL_CONSTRAINTS });
      setLastPriceChange(null);
    };

    const undoConstraints = () => {
      setConstraints({ ...priorConstraints });
      setLastPriceChange(null);
    };

    return (
      <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
          {/* Order Controls */}
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <h3>Order:</h3>
            <select value={orderType || 'none'} onChange={(e) => setOrderType(e.target.value === 'none' ? null : e.target.value as OrderType)}>
              <option value="none">None</option>
              <option value={OrderType.BUY}>Buy</option>
              <option value={OrderType.SELL}>Sell</option>
            </select>
            {orderType && (
              <>
                <input
                  type="range"
                  min="0"
                  max="50"
                  value={orderQuantity}
                  onChange={(e) => setOrderQuantity(Number(e.target.value))}
                />
                <span>Quantity: {orderQuantity}</span>
              </>
            )}
          </div>

          {/* Price Change Buttons */}
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <button
              disabled={!orderType}
              onClick={() => handlePriceChange('INCREASE')}
              style={{ 
                padding: '8px 16px', 
                borderRadius: '4px', 
                border: '1px solid #ccc', 
                cursor: orderType ? 'pointer' : 'not-allowed',
                opacity: orderType ? 1 : 0.5
              }}
            >
              Price Increase
            </button>
            <button
              disabled={!orderType}
              onClick={() => handlePriceChange('DECREASE')}
              style={{ 
                padding: '8px 16px', 
                borderRadius: '4px', 
                border: '1px solid #ccc', 
                cursor: orderType ? 'pointer' : 'not-allowed',
                opacity: orderType ? 1 : 0.5
              }}
            >
              Price Decrease
            </button>
            <button
              onClick={undoConstraints}
              style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc', cursor: 'pointer' }}
            >
              Undo
            </button>
            <button
              onClick={resetConstraints}
              style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc', cursor: 'pointer' }}
            >
              Reset
            </button>
          </div>
        </div>

        {/* Constraints Bar */}
        <OrderConstraints
          constraints={constraints}
          height={30}
          orderQuantity={orderQuantity}
          orderType={orderType}
          priceChange={lastPriceChange}
          showLabels={true}
          tickFontSize={12}
          tickStep={10}
          width={400}
        />

        {/* Debug Info */}
        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
          <pre>{JSON.stringify(constraints, null, 2)}</pre>
        </div>
      </div>
    );
  }
};

export const AllScenariosDemo: Story = {
  render: () => {
    const [lastPriceChange, setLastPriceChange] = useState<'up' | 'down' | null>(null);
    const [basicTwoSidedLeft, setBasicTwoSidedLeft] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.BASIC_TWO_SIDED });
    const [basicTwoSidedRight, setBasicTwoSidedRight] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.BASIC_TWO_SIDED });
    const [oneSidedLeft, setOneSidedLeft] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.ONE_SIDED_BUY });
    const [oneSidedRight, setOneSidedRight] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.ONE_SIDED_SELL });
    const [switchLeft, setSwitchLeft] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.SWITCH_LEFT });
    const [switchRight, setSwitchRight] = useState<DeBidConstraints>({ ...TEST_SCENARIOS.SWITCH_RIGHT });

    const handlePriceChange = (change: PriceChange) => {
      setLastPriceChange(change === 'INCREASE' ? 'up' : 'down');

      // Update each scenario
      setBasicTwoSidedLeft(updateConstraints({
        currentConstraints: basicTwoSidedLeft,
        orderType: OrderType.BUY,
        orderQuantity: 25,
        priceChange: change
      }));

      setBasicTwoSidedRight(updateConstraints({
        currentConstraints: basicTwoSidedRight,
        orderType: OrderType.SELL,
        orderQuantity: 25,
        priceChange: change
      }));

      setOneSidedLeft(updateConstraints({
        currentConstraints: oneSidedLeft,
        orderType: OrderType.BUY,
        orderQuantity: 20,
        priceChange: change
      }));

      setOneSidedRight(updateConstraints({
        currentConstraints: oneSidedRight,
        orderType: OrderType.SELL,
        orderQuantity: 20,
        priceChange: change
      }));

      setSwitchLeft(updateConstraints({
        currentConstraints: switchLeft,
        orderType: OrderType.SELL,
        orderQuantity: 25,
        priceChange: change
      }));

      setSwitchRight(updateConstraints({
        currentConstraints: switchRight,
        orderType: OrderType.BUY,
        orderQuantity: 25,
        priceChange: change
      }));

      // Reset lastPriceChange after animation duration
      setTimeout(() => {
        setLastPriceChange(null);
      }, 1000);
    };

    const resetAll = () => {
      setBasicTwoSidedLeft({ ...TEST_SCENARIOS.BASIC_TWO_SIDED });
      setBasicTwoSidedRight({ ...TEST_SCENARIOS.BASIC_TWO_SIDED });
      setOneSidedLeft({ ...TEST_SCENARIOS.ONE_SIDED_BUY });
      setOneSidedRight({ ...TEST_SCENARIOS.ONE_SIDED_SELL });
      setSwitchLeft({ ...TEST_SCENARIOS.SWITCH_LEFT });
      setSwitchRight({ ...TEST_SCENARIOS.SWITCH_RIGHT });
      setLastPriceChange(null);
    };

    return (
      <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
        {/* Controls */}
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
          <button
            onClick={() => handlePriceChange('INCREASE')}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc', cursor: 'pointer' }}
          >
            Price Increase
          </button>
          <button
            onClick={() => handlePriceChange('DECREASE')}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc', cursor: 'pointer' }}
          >
            Price Decrease
          </button>
          <button
            onClick={resetAll}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc', cursor: 'pointer' }}
          >
            Reset All
          </button>
        </div>

        {/* Test Scenarios */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '30px' }}>
          {/* Basic Two-sided */}
          <div>
            <h3>Basic Two-sided</h3>
            <div style={{ display: 'flex', gap: '20px' }}>
              <OrderConstraints
                constraints={basicTwoSidedLeft}
                height={30}
                orderQuantity={25}
                orderType={OrderType.BUY}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
              <OrderConstraints
                constraints={basicTwoSidedRight}
                height={30}
                orderQuantity={25}
                orderType={OrderType.SELL}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
            </div>
          </div>

          {/* One-sided */}
          <div>
            <h3>One-sided</h3>
            <div style={{ display: 'flex', gap: '20px' }}>
              <OrderConstraints
                constraints={oneSidedLeft}
                height={30}
                orderQuantity={20}
                orderType={OrderType.BUY}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
              <OrderConstraints
                constraints={oneSidedRight}
                height={30}
                orderQuantity={20}
                orderType={OrderType.SELL}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
            </div>
          </div>

          {/* Switch */}
          <div>
            <h3>Switch</h3>
            <div style={{ display: 'flex', gap: '20px' }}>
              <OrderConstraints
                constraints={switchLeft}
                height={30}
                orderQuantity={25}
                orderType={OrderType.SELL}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
              <OrderConstraints
                constraints={switchRight}
                height={30}
                orderQuantity={25}
                orderType={OrderType.BUY}
                priceChange={lastPriceChange}
                showLabels={true}
                tickFontSize={12}
                tickStep={10}
                width={400}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
};
