import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import React, { useState } from 'react';
import { OrderConstraints } from './OrderConstraints';
import { OrderConstraintsWithRange } from './OrderConstraintsWithRange';
import { DeBidConstraints, OrderType } from '../../api-client/connector/types/generated';

const meta: Meta<typeof OrderConstraints> = {
  title: 'Widgets/OrderConstraints',
  component: OrderConstraints,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A volume meter component that visually displays bidding eligibility constraints in auction trading.'
      }
    }
  },
  argTypes: {
    height: {
      control: { type: 'range', min: 20, max: 100, step: 5 }
    },
    width: {
      control: { type: 'range', min: 100, max: 500, step: 10 }
    },
    maxQuantity: {
      control: { type: 'range', min: 10, max: 100, step: 5 }
    },
    tickStep: {
      control: { type: 'range', min: 5, max: 20, step: 5 }
    },
    tickFontSize: {
      control: { type: 'range', min: 8, max: 16, step: 1 }
    },
    orderQuantity: {
      control: { type: 'range', min: 0, max: 50, step: 1 }
    },
    orderType: {
      control: { type: 'select' },
      options: [OrderType.BUY, OrderType.SELL, OrderType.NONE, null]
    },
    priceChange: {
      control: { type: 'select' },
      options: ['up', 'down', null]
    },
    showLabels: {
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof OrderConstraints>;

// Default constraints for stories
const defaultConstraints: DeBidConstraints = {
  max_buy_quantity: 40,
  max_sell_quantity: 20,
  min_buy_quantity: 0,
  min_sell_quantity: 0
};

export const Default: Story = {
  args: {
    constraints: defaultConstraints,
    height: 30,
    width: 300,
    maxQuantity: 50,
    orderQuantity: 10,
    orderType: OrderType.BUY,
    showLabels: true,
    tickFontSize: 12,
    tickStep: 10,
    priceChange: null
  }
};

export const BuyOrder: Story = {
  args: {
    ...Default.args,
    orderType: OrderType.BUY,
    orderQuantity: 25
  }
};

export const SellOrder: Story = {
  args: {
    ...Default.args,
    orderType: OrderType.SELL,
    orderQuantity: 15
  }
};

export const NoOrder: Story = {
  args: {
    ...Default.args,
    orderType: null,
    orderQuantity: null
  }
};

export const PriceIncrease: Story = {
  args: {
    ...Default.args,
    priceChange: 'up' as const
  }
};

export const PriceDecrease: Story = {
  args: {
    ...Default.args,
    priceChange: 'down' as const
  }
};

export const OneSidedBuy: Story = {
  args: {
    ...Default.args,
    constraints: {
      max_buy_quantity: 40,
      min_buy_quantity: 10,
      max_sell_quantity: 0,
      min_sell_quantity: 0
    },
    orderType: OrderType.BUY,
    orderQuantity: 20
  }
};

export const OneSidedSell: Story = {
  args: {
    ...Default.args,
    constraints: {
      max_buy_quantity: 0,
      min_buy_quantity: 0,
      max_sell_quantity: 40,
      min_sell_quantity: 10
    },
    orderType: OrderType.SELL,
    orderQuantity: 20
  }
};

export const WithRange: Story = {
  render: (args) => (
    <OrderConstraintsWithRange
      orderQuantity={args.orderQuantity || 10}
      orderType={args.orderType || OrderType.BUY}
      constraints={args.constraints || defaultConstraints}
      quantityLabel="MMlb"
    />
  ),
  args: {
    constraints: defaultConstraints,
    orderQuantity: 10,
    orderType: OrderType.BUY
  }
};

// Interactive demo story
export const InteractiveDemo: Story = {
  render: () => {
    const [constraints, setConstraints] = useState<DeBidConstraints>(defaultConstraints);
    const [orderType, setOrderType] = useState<OrderType | null>(OrderType.BUY);
    const [orderQuantity, setOrderQuantity] = useState(25);
    const [priceChange, setPriceChange] = useState<'up' | 'down' | null>(null);

    const handlePriceChange = (change: 'up' | 'down') => {
      setPriceChange(change);
      // Reset after animation
      setTimeout(() => setPriceChange(null), 1000);
    };

    return (
      <div style={{ padding: '20px', display: 'flex', flexDirection: 'column', gap: '20px' }}>
        <div style={{ display: 'flex', gap: '20px', alignItems: 'center' }}>
          <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
            <label>Order Type:</label>
            <select 
              value={orderType || 'none'} 
              onChange={(e) => setOrderType(e.target.value === 'none' ? null : e.target.value as OrderType)}
            >
              <option value="none">None</option>
              <option value={OrderType.BUY}>Buy</option>
              <option value={OrderType.SELL}>Sell</option>
            </select>
          </div>
          
          {orderType && (
            <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
              <label>Quantity:</label>
              <input
                type="range"
                min="0"
                max="50"
                value={orderQuantity}
                onChange={(e) => setOrderQuantity(Number(e.target.value))}
              />
              <span>{orderQuantity}</span>
            </div>
          )}
        </div>

        <div style={{ display: 'flex', gap: '10px' }}>
          <button 
            disabled={!orderType}
            onClick={() => handlePriceChange('up')}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            Price Increase
          </button>
          <button 
            disabled={!orderType}
            onClick={() => handlePriceChange('down')}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            Price Decrease
          </button>
          <button 
            onClick={() => setConstraints(defaultConstraints)}
            style={{ padding: '8px 16px', borderRadius: '4px', border: '1px solid #ccc' }}
          >
            Reset
          </button>
        </div>

        <OrderConstraints
          constraints={constraints}
          height={30}
          width={400}
          orderQuantity={orderQuantity}
          orderType={orderType}
          priceChange={priceChange}
          showLabels={true}
          tickFontSize={12}
          tickStep={10}
        />

        <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
          <pre>{JSON.stringify(constraints, null, 2)}</pre>
        </div>
      </div>
    );
  }
};
