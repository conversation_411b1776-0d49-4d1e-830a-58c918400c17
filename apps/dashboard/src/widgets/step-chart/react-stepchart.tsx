import {
  Responsive<PERSON><PERSON>r,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";

// Generate the dataset
const generateData = () => {
  const data = [];
  for (let price = 20; price <= 40; price += 2) {
    data.push({
      price: price,
      buyVolume: 110 - 2.5 * price, // Example decreasing function
      sellVolume: 2.5 * price - 30, // Example increasing function
    });
  }
  return data;
};

const data = generateData();

const O1MiniChart = () => {
  return (
    <div style={{ width: "100%", height: 500 }}>
      <h2>Double-Sided Clock Auction Volumes</h2>
      <ResponsiveContainer>
        <LineChart
          data={data}
          margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="price"
            label={{
              value: "Price (cpp)",
              position: "insideBottom",
              offset: -10,
            }}
          />
          <YAxis
            label={{
              value: "Volume (MMlb)",
              angle: -90,
              position: "insideLeft",
              offset: 10,
            }}
          />
          <Tooltip />
          <Legend verticalAlign="top" height={36} />
          <Line
            type="step"
            dataKey="buyVolume"
            name="Buy Volume"
            stroke="#00C49F"
            strokeWidth={1}
            dot={{ r: 3 }}
          />
          <Line
            type="step"
            dataKey="sellVolume"
            name="Sell Volume"
            stroke="#FF0000"
            strokeWidth={1}
            dot={{ r: 3 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default O1MiniChart;